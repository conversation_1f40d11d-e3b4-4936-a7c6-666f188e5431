// utils.js
// 接收一个 URL 字符串，并通过正则表达式 https?:\/\/[^\s]+ 提取有效的 URL。如果匹配到，则返回该 URL，否则返回 null。
// 优化后的 getImageUrl 方法
export const getImageUrl = (urlString) => {
  // 定义有效的图片格式后缀
  const imageFormats = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
  // 使用正则表达式提取 URL
  const match = urlString.match(/https?:\/\/[^\s]+/);
  if (match) {
    let url = match[0];
    // 截取到 '?' 之前的部分
    url = url.split('?')[0];
    // 检查 URL 是否以有效的图片格式结尾
    const isImageUrl = imageFormats.some(format => url.toLowerCase().endsWith(`.${format}`));
    return isImageUrl ? url : null;
  }
  return null;
}

/**
 * 清理Markdown格式文本，提取纯文本内容
 * @param {string} content - 包含Markdown格式的文本内容
 * @returns {string} 清理后的纯文本内容
 */
export const cleanMarkdownText = (content) => {
  if (!content || typeof content !== 'string') {
    return '';
  }
  return content
    // 移除代码块
    .replace(/```[\s\S]*?```/g, '')
    // 移除行内代码
    .replace(/`([^`]+)`/g, '$1')
    // 处理链接：保留http/https链接，其他链接只保留文本
    .replace(/\[([^\]]+)\]\((https?:\/\/[^)]+)\)/g, '$1 ($2)')
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // 移除图片
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
    // 移除粗体和斜体标记
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // 移除删除线
    .replace(/~~([^~]+)~~/g, '$1')
    // 移除标题标记
    .replace(/^#{1,6}\s+/gm, '')
    // 移除引用标记
    .replace(/^>\s+/gm, '')
    // 移除列表标记
    .replace(/^[\s]*[-*+]\s+/gm, '')
    .replace(/^[\s]*\d+\.\s+/gm, '')
    // 移除水平分割线
    .replace(/^[-*_]{3,}$/gm, '')
    // 移除HTML标签（包括<br>标签）
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    // 移除多余的空行，保留单个换行
    .replace(/\n{3,}/g, '\n\n')
    // 移除首尾空白字符
    .trim();
};

// 复制消息内容的方法
export const copyMessage = (content) => {
  // 清理Markdown格式，只保留纯文本
  const cleanContent = cleanMarkdownText(content);
  uni.setClipboardData({
    data: cleanContent,
    success: () => {
      uni.showToast({
        title: "复制成功",
        icon: "success",
      });
    },
    fail: () => {
      uni.showToast({
        title: "复制失败",
        icon: "none",
      });
    },
  });
};

// 防抖函数，延迟执行传入的函数
export function debounce(func, delay) {
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(...args);
    }, delay);
  };
}

/**
 * 检测当前浏览器类型
 * @returns {string} 浏览器类型
 */
export function detectBrowser() {
  // 安全检查：在app端或某些环境中navigator可能不存在
  if (typeof navigator === 'undefined' || !navigator.userAgent) {
    // #ifdef APP-PLUS
    return "App"; // App端环境
    // #endif
    
    // #ifndef APP-PLUS
    return "Unknown browser"; // 其他未知环境
    // #endif
  }
  
  const userAgent = navigator.userAgent;
  if (userAgent.indexOf("Chrome")!== -1 && userAgent.indexOf("Safari") !== -1 && userAgent.indexOf("Edg") === -1) {
    return "Google Chrome"; // Chrome
  } else if (userAgent.indexOf("Firefox") !== -1) {
    return "Mozilla Firefox"; // Firefox
  } else if (userAgent.indexOf("Safari") !== -1 && userAgent.indexOf("Chrome") === -1 && userAgent.indexOf("Edge") === -1) {
    return "Apple Safari"; // Safari
  } else if (userAgent.indexOf("Edg") !== -1) {
    return "Microsoft Edge"; // Edge
  } else {
    return "Unknown browser"; // 其他浏览器
  }
}
/**
 * 判断当前设备是PC端还是移动端
 * @returns {string} 'mobile' | 'pc'
 */
export function detectDeviceType() {
  // #ifdef H5
  const userAgent = navigator.userAgent;
  // 移动设备特征检测
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i;
  // 平板设备特征检测
  const tabletRegex = /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*\bTablet\b)/i;
  // 检测屏幕尺寸（作为辅助判断）
  const screenWidth = window.screen.width;
  const screenHeight = window.screen.height;
  const maxScreenSize = Math.max(screenWidth, screenHeight);
  // 优先检测移动设备
  if (mobileRegex.test(userAgent)) {
    return 'mobile';
  }
  // 检测平板设备（iPad等）
  if (tabletRegex.test(userAgent)) {
    return 'mobile'; // 将平板归类为移动端
  }
  // 基于屏幕尺寸的辅助判断
  if (maxScreenSize <= 768) {
    return 'mobile';
  }
  // 检测触摸设备
  if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
    // 有触摸功能但屏幕较大，可能是触摸屏PC，需要进一步判断
    if (maxScreenSize > 1024) {
      return 'pc';
    } else {
      return 'mobile';
    }
  }
  return 'pc';
  // #endif
}

/**
 * 判断当前是否为移动端
 * @returns {boolean} true为移动端，false为PC端
 */
export function isMobile() {
  return detectDeviceType() === 'mobile';
}

/**
 * 判断当前是否为PC端
 * @returns {boolean} true为PC端，false为移动端
 */
export function isPC() {
  return detectDeviceType() === 'pc';
}

/**
 * 删除字符串中的content属性和属性值
 * @param {string} jsonStr - 包含content属性的JSON字符串
 * @returns {string} - 删除content属性后的JSON字符串
 */
export function removeContentProperty(jsonStr) {
  // 匹配 'content': '...' 格式，支持多行内容和转义字符
  const contentRegex = /'content':\s*'[^']*(?:\\'[^']*)*',?\s*/g;
  // 删除content属性
  let cleanedStr = jsonStr.replace(contentRegex, '');
  // 清理可能产生的多余逗号
  cleanedStr = cleanedStr.replace(/,\s*,/g, ','); // 连续逗号
  cleanedStr = cleanedStr.replace(/\{\s*,/g, '{'); // 对象开始的逗号
  cleanedStr = cleanedStr.replace(/,\s*\}/g, '}'); // 对象结束前的逗号
  return cleanedStr;
}

/**
 * 将Python风格的单引号JSON转换为标准JSON格式
 * @param {string} pythonStyleJson - Python风格的JSON字符串
 * @returns {string} - 标准JSON格式字符串
 */
export function convertPythonJsonToStandard(pythonStyleJson) {
  // 将单引号转换为双引号，但要避免转换字符串内容中的单引号
  return pythonStyleJson.replace(/'/g, '"');
}

/**
 * 打开链接，兼容H5和APP端
 * @param {string} url - 要打开的链接地址
 * @param {string} title - 链接标题（可选）
 */
export function openLink(url, title = '') {
  if (!url) {
    uni.showToast({
      title: '链接地址无效',
      icon: 'none'
    });
    return;
  }

  // 确保URL格式正确
  let formattedUrl = url;
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    formattedUrl = 'https://' + url;
  }

  // #ifdef H5
  // H5端直接使用window.open打开链接
  try {
    window.open(formattedUrl, '_blank');
  } catch (error) {
    console.error('打开链接失败:', error);
    uni.showToast({
      title: '打开链接失败',
      icon: 'none'
    });
  }
  // #endif

  // #ifdef APP-PLUS
  // APP端使用plus.runtime.openURL打开链接
  try {
    plus.runtime.openURL(formattedUrl);
  } catch (error) {
    console.error('打开链接失败:', error);
    uni.showToast({
      title: '打开链接失败',
      icon: 'none'
    });
  }
  // #endif
}


/**
 * 清理并格式化文本，为语音播放做准备。
 * 
 * 该方法主要用于从输入的文本中移除 URL 链接、清理多余的空白字符和处理标点符号问题，
 * 目的是使文本更适合语音合成播放。处理后返回的文本将去掉所有符合规则的 URL，并进行必要的格式化。
 *
 * 主要处理：
 * 1. 移除文本中的 URL（包括 http/https 链接、域名、IP 地址等）。
 * 2. 清理多余的空白字符（如连续空格、换行符等）。
 * 3. 处理标点符号问题（如去除标点前的空格、合并连续标点等）。
 * 
 * @param {string} text - 输入的待处理文本。
 * @returns {string} - 处理后的文本，适合用于语音合成。
 */
export function preprocessTextForVoice(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  // 定义多种URL匹配的正则表达式
  const urlPatterns = [
    // 1. 标准HTTP/HTTPS链接
    /https?:\/\/[^\s<>"{}|\\^`[\]]+/gi,

    // 2. www开头的链接
    /www\.[a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}[^\s<>"{}|\\^`[\]]*/gi,

    // 3. 域名格式（包含常见顶级域名）
    /\b[a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]*\.(com|org|net|edu|gov|mil|int|cn|jp|uk|de|fr|it|ru|br|in|au|ca|mx|es|nl|se|no|dk|fi|pl|cz|hu|io|co|me|tv|cc|ly|to|be|at|ch|ie|pt|gr|tr|il|za|eg|ma|ng|ke|gh|tz|ug|app|dev|tech|info|biz|name|pro|museum|aero|coop|jobs|mobi|travel|xxx|asia|cat|post|tel|xxx)\b[^\s<>"{}|\\^`[\]]*/gi,

    // 4. 带端口号的链接
    /\b[a-zA-Z0-9][-a-zA-Z0-9]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}:[0-9]{1,5}[^\s<>"{}|\\^`[\]]*/gi,

    // 5. IP地址格式
    /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}(?::[0-9]{1,5})?[^\s<>"{}|\\^`[\]]*/gi
  ];

  let processedText = text;
  let removedUrlCount = 0;

  // 使用多种正则模式逐一过滤URL
  urlPatterns.forEach((pattern, index) => {
    const beforeLength = processedText.length;
    processedText = processedText.replace(pattern, (match) => {
      // 记录移除的URL
      console.log(`移除URL (模式${index + 1}):`, match.substring(0, 50) + (match.length > 50 ? '...' : ''));
      removedUrlCount++;
      return '';
    });
    const afterLength = processedText.length;
    if (beforeLength !== afterLength) {
      console.log(`URL模式${index + 1}处理完成，移除了${beforeLength - afterLength}个字符`);
    }
  });

  // 清理多余的空白字符
  processedText = processedText
    .replace(/\s+/g, ' ')  // 将多个空格替换为单个空格
    .replace(/\n\s*\n/g, '\n')  // 将多个换行替换为单个换行
    .trim();  // 移除首尾空白

  // 处理可能出现的标点符号问题
  processedText = processedText
    .replace(/\s+([,.!?;:])/g, '\$1')  // 移除标点符号前的空格
    .replace(/([,.!?;:])\s*([,.!?;:])/g, '\$1\$2');  // 合并连续的标点符号

  // console.log('语音播放文本预处理:', {
  //   originalLength: text.length,
  //   processedLength: processedText.length,
  //   removedUrlCount: removedUrlCount,
  //   original: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
  //   processed: processedText.substring(0, 100) + (processedText.length > 100 ? '...' : ''),
  //   hasRemovedContent: text.length - processedText.length > 10
  // });
  return processedText;
}

/**
 * 清理Markdown格式文本，提取纯文本内容，并返回前20个字符（移除换行符号）
 * @param {string} content - 包含Markdown格式的文本内容
 * @returns {string} 清理后的纯文本内容的前20个字符（移除换行符号）
 */
export const cleanMarkdownTextCleanText = (content) => {
  if (!content || typeof content !== 'string') {
    return '';
  }
  const cleanedText = content
    // 移除代码块
    .replace(/```[\s\S]*?```/g, '')
    // 移除行内代码
    .replace(/`([^`]+)`/g, '\\$1')
    // 处理链接：保留http/https链接，其他链接只保留文本
    .replace(/\[([^\]]+)\]\((https?:\/\/[^)]+)\)/g, '\\$1 (\\$2)')
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '\\$1')
    // 移除图片
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '\\$1')
    // 移除粗体和斜体标记
    .replace(/\*\*([^*]+)\*\*/g, '\\$1')
    .replace(/\*([^*]+)\*/g, '\\$1')
    .replace(/__([^_]+)__/g, '\\$1')
    .replace(/_([^_]+)_/g, '\\$1')
    // 移除删除线
    .replace(/~~([^~]+)~~/g, '\\$1')
    // 移除标题标记
    .replace(/^#{1,6}\s+/gm, '')
    // 移除引用标记
    .replace(/^>\s+/gm, '')
    // 移除列表标记
    .replace(/^[\s]*[-*+]\s+/gm, '')
    .replace(/^[\s]*\d+\.\s+/gm, '')
    // 移除水平分割线
    .replace(/^[-*_]{3,}$/gm, '')
    // 移除HTML标签（包括<br>标签）
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    // 移除多余的空行，保留单个换行
    .replace(/\n{3,}/g, '\n\n')
    // 移除首尾空白字符
    .trim();
  // 移除所有换行符和空白字符
  const noNewlineText = cleanedText.replace(/[\n\r]/g, '').replace(/\s+/g, ' ');
  // 提取并返回前20个字符
  return noNewlineText.slice(0, 50);
};
