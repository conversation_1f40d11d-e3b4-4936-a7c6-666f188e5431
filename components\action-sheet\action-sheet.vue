<template>
  <transition name="fade">
    <view 
      v-if="visible" 
      class="action-sheet-mask" 
      :class="{ 'action-sheet-mask--transparent': maskStyle === 'transparent' }"
      @click="onClickMask"
    ></view>
  </transition>
  
  <transition name="slide-up">
    <view v-if="visible" class="action-sheet-container" :style="{ zIndex,width: isPC() ? '100%' : '85%' }">
      <!-- 默认面板内容 -->
      <template v-if="!customPanel">
        <!-- 标题 -->
        <view v-if="title" class="action-sheet-title">{{ title }}</view>
        
        <!-- 描述 -->
        <view v-if="description" class="action-sheet-description">{{ description }}</view>
        
        <!-- 选项列表 -->
        <view class="action-sheet-options">
          <view 
            v-for="(item, index) in actions" 
            :key="index"
            class="action-sheet-item"
            :class="{
              'action-sheet-item--disabled': item.disabled,
              'action-sheet-item--loading': item.loading
            }"
            @click="onSelect(index)"
          >
            <view class="action-sheet-item__content">
              <!-- 加载状态 -->
              <view v-if="item.loading" class="action-sheet-item__loading">
                <view class="action-sheet-item__loading-spinner"></view>
              </view>
              
              <!-- 选项文本 -->
              <text 
                class="action-sheet-item__text"
                :class="{ 'action-sheet-item__text--danger': item.color === 'danger' }"
                :style="{ color: item.color && item.color !== 'danger' ? item.color : '' }"
              >
                {{ item.name }}
              </text>
              
              <!-- 子描述 -->
              <text v-if="item.subname" class="action-sheet-item__subname">{{ item.subname }}</text>
            </view>
          </view>
        </view>
      </template>
      
      <!-- 自定义面板内容 -->
      <slot v-else name="custom-panel"></slot>
      
      <!-- 取消按钮 -->
      <view v-if="showCancel && !customPanel" class="action-sheet-cancel" @click="onCancel">
        <view class="action-sheet-cancel__text">{{ cancelText }}</view>
      </view>
      
      <!-- 自定义取消按钮 -->
      <slot v-if="showCancel && customPanel" name="custom-cancel"></slot>
    </view>
  </transition>
</template>

<script setup>
import { ref, watch,onUnmounted } from 'vue'
import { isPC } from '@/utils/utils.js'
// ActionSheet 动作面板
// 对标：https://vant-ui.github.io/vant/v3/#/zh-CN/action-sheet#yin-ru

const props = defineProps({
  // 是否显示动作面板
  show: Boolean,
  // 是否使用自定义面板
  customPanel: Boolean,
  // 标题
  title: String,
  // 选项上方的描述信息
  description: String,
  // 新增lockScroll属性
  lockScroll: {
    type: Boolean,
    default: true
  },
  // 面板选项列表
  actions: {
    type: Array,
    default: () => []
  },
  // 取消按钮文字
  cancelText: {
    type: String,
    default: '取消'
  },
  // 是否显示取消按钮
  showCancel: {
    type: Boolean,
    default: true
  },
  // 点击选项后是否关闭面板
  closeOnClickAction: {
    type: Boolean,
    default: true
  },
  // 点击遮罩是否关闭面板
  closeOnClickMask: {
    type: Boolean,
    default: true
  },
  // 自定义遮罩样式
  maskStyle: {
    type: String,
    default: 'normal' // 'normal' | 'transparent'
  },
  // 层级
  zIndex: {
    type: Number,
    default: 1000,
  },
});

const emit = defineEmits([
  'update:show',
  'select',
  'cancel',
  'close'
])

const visible = ref(props.show)

watch(() => props.show, (val) => {
  visible.value = val
  // 显示/隐藏时处理滚动锁定
  handleScrollLock(val)
})

watch(visible, (val) => {
  emit('update:show', val)
  if (!val) {
    emit('close')
  }
})

// 点击选项
const onSelect = (index) => {
  const action = props.actions[index]
  
  if (action && !action.disabled && !action.loading) {
    emit('select', action, index)
    
    if (props.closeOnClickAction) {
      visible.value = false
    }
  }
}
// H5滚动锁定方法
const handleScrollLock = (lock) => {
  if (!props.lockScroll) return // 如果lockScroll为false则不处理
  if (typeof document !== 'undefined') {
    // #ifdef H5
    if (lock) {
      document.body.classList.add('action-sheet-scroll-lock')
    } else {
      document.body.classList.remove('action-sheet-scroll-lock')
    }
    // #endif
  }
}

// 点击取消按钮
const onCancel = () => {
  emit('cancel')
  visible.value = false
}

// 点击遮罩
const onClickMask = () => {
  if (props.closeOnClickMask) {
    onCancel()
  }
}

onUnmounted(() => {
  // 组件卸载时确保解除锁定
  handleScrollLock(false)
})

// 暴露方法
defineExpose({
  show: () => { visible.value = true },
  close: () => { visible.value = false }
})

</script>

<style lang="scss" scoped>
/* 遮罩层 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;

  &--transparent {
    background-color: transparent;
  }
}

/* 容器 */
.action-sheet-container {
  position: fixed;
  left: 50%; /* 水平居中 */
  top: 50%;  /* 垂直居中 */
  transform: translate(-50%, -50%); /* 通过平移调整容器位置 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  border-radius: 10px;
  z-index: 1000;
  box-sizing: border-box;
  max-width: 750px;
  width: 100%;
}
/* 标题 */
.action-sheet-title {
  padding: 28rpx 32rpx;
  color: #969799;
  font-size: 28rpx;
  line-height: 1.4;
  text-align: center;
}

/* 描述 */
.action-sheet-description {
  padding: 20rpx 32rpx;
  color: #969799;
  font-size: 28rpx;
  line-height: 1.4;
  text-align: center;
}

/* 选项列表 */
.action-sheet-options {
  position: relative;
  max-height: 70vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 选项项 */
.action-sheet-item {
  position: relative;
  padding: 28rpx 32rpx;
  text-align: center;
  background-color: #fff;

  &:active {
    background-color: #f2f3f5;
  }

  &--disabled {
    opacity: 0.6;

    &:active {
      background-color: #fff;
    }
  }

  &--loading {
    .action-sheet-item__text {
      opacity: 0;
    }
  }

  &::after {
    position: absolute;
    content: "";
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 32rpx;
    border-bottom: 1rpx solid #ebedf0;
    transform: scaleY(0.5);
  }

  &:last-child::after {
    display: none;
  }
}

.action-sheet-item__content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-sheet-item__text {
  color: #323233;
  font-size: 32rpx;
  line-height: 1.4;

  &--danger {
    color: #ee0a24;
  }
}

.action-sheet-item__subname {
  margin-top: 8rpx;
  color: #969799;
  font-size: 24rpx;
  line-height: 1.4;
}

/* 加载状态 */
.action-sheet-item__loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.action-sheet-item__loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid transparent;
  border-top-color: #c8c9cc;
  border-radius: 50%;
  animation: action-sheet-spinner 0.8s linear infinite;
}

/* 取消按钮 */
.action-sheet-cancel {
  margin-top: 12rpx;
  padding: 28rpx 32rpx;
  text-align: center;

  &:active {
    background-color: #f2f3f5;
  }
}

.action-sheet-cancel__text {
  color: #323233;
  font-size: 32rpx;
  line-height: 1.4;
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.1s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.1s;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translate3d(0, 100%, 0);
}

@keyframes action-sheet-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>