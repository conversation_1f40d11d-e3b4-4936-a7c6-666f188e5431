var __wxsModules={};
__wxsModules.afd46426=(()=>{var X=(a,e)=>()=>(e||a((e={exports:{}}).exports,e),e.exports);var k=X((q,W)=>{var c=10,f=!1;typeof window=="object"&&(f=!0);function D(a,e,r,t){var i=t.getState();if(S(t,r),a&&a!=="none"){s(a,t,r);return}i.left&&s("none",t,r),b(t)}function v(a,e){var r=a.instance,t=r.getDataset().disabled,i=r.getState();S(r,e),t=(typeof t=="string"?JSON.parse(t):t)||!1,!t&&(r.requestAnimationFrame(function(){r.removeClass("ani"),e.callMethod("closeSwipe")}),i.x=i.left||0,C(a,e))}function g(a,e){var r=a.instance,t=r.getDataset().disabled,i=r.getState();t=(typeof t=="string"?JSON.parse(t):t)||!1,!t&&(x(a),i.direction==="horizontal"&&(a.preventDefault&&a.preventDefault(),p(i.x+i.deltaX,r,e)))}function m(a,e){var r=a.instance,t=r.getDataset().disabled,i=r.getState();t=(typeof t=="string"?JSON.parse(t):t)||!1,!t&&M(i.left,r,e)}function p(a,e,r){a=a||0;var t=e.getState(),i=t.leftWidth,n=t.rightWidth;t.left=Y(a,-n,i),e.requestAnimationFrame(function(){e.setStyle({transform:"translateX("+t.left+"px)","-webkit-transform":"translateX("+t.left+"px)"})})}function S(a,e){var r=a.getState(),t=e.selectComponent(".button-group--left"),i=e.selectComponent(".button-group--right"),n={width:0},o={width:0};t&&(n=t.getBoundingClientRect()),i&&(o=i.getBoundingClientRect()),r.leftWidth=n.width||0,r.rightWidth=o.width||0,r.threshold=a.getDataset().threshold}function Y(a,e,r){return Math.min(Math.max(a,e),r)}function M(a,e,r){var t=e.getState(),i=t.threshold,n=t.position,o=t.isopen||"none",h=t.leftWidth,d=t.rightWidth;if(t.deltaX===0){s("none",e,r);return}o==="none"&&d>0&&-a>i||o!=="none"&&d>0&&d+a<i?s("right",e,r):o==="none"&&h>0&&a>i||o!=="none"&&h>0&&h-a<i?s("left",e,r):s("none",e,r)}function s(a,e,r){var t=e.getState(),i=t.leftWidth,n=t.rightWidth,o="";switch(t.isopen=t.isopen?t.isopen:"none",a){case"left":o=i;break;case"right":o=-n;break;default:o=0}t.isopen!==a&&(t.throttle=!0,r.callMethod("change",{open:a})),t.isopen=a,e.requestAnimationFrame(function(){e.addClass("ani"),p(o,e,r)})}function A(a,e){return a>e&&a>c?"horizontal":e>a&&e>c?"vertical":""}function b(a){var e=a.getState();e.direction="",e.deltaX=0,e.deltaY=0,e.offsetX=0,e.offsetY=0}function C(a){var e=a.instance,r=e.getState();b(e);var t=a.touches[0];f&&u()&&(t=a),r.startX=t.clientX,r.startY=t.clientY}function x(a){var e=a.instance,r=e.getState(),t=a.touches[0];f&&u()&&(t=a),r.deltaX=t.clientX-r.startX,r.deltaY=t.clientY-r.startY,r.offsetY=Math.abs(r.deltaY),r.offsetX=Math.abs(r.deltaX),r.direction=r.direction||A(r.offsetX,r.offsetY)}function u(){for(var a=navigator.userAgent,e=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"],r=!0,t=0;t<e.length-1;t++)if(a.indexOf(e[t])>0){r=!1;break}return r}var l=!1;function N(a,e){f&&u()&&(v(a,e),l=!0)}function O(a,e){f&&u()&&l&&g(a,e)}function P(a,e){f&&u()&&(m(a,e),l=!1)}function T(a,e){f&&u()&&(l=!1)}W.exports={showWatch:D,touchstart:v,touchmove:g,touchend:m,mousedown:N,mousemove:O,mouseup:P,mouseleave:T}});return k();})();

__wxsModules["42613348"]=(()=>{var N=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var U=N((z,B)=>{var T=0,p=-1,A=-1;function W(e,r,a,t){var s=a.getState()||{};s.currentIns=t;var i=t.getDataset(),l=i.loading==!0;if(e&&e.indexOf("end")!=-1){var f=e.split("end")[0];O("translateY(0px)",t,!1,f),s.moveDis=0,s.oldMoveDis=0,T=0}else if(e&&e.indexOf("begin")!=-1){var h=t.getDataset().refresherthreshold;M(h,t,s,!1)}}function P(e,r){var a=R(r),t={},s={};if(r.callMethod("_handleListTouchstart"),!(a&&(t=a.getState(),s=a.getDataset(),Y(e,a,0)))){var i=t.isTouchEnded;t.oldMoveDis=0;var l=b(e),f=d(s.loading);t.startY=l.touchY,A=t.startY,t.lastTouch=l,!f&&i&&(t.isTouchmoving=!1),t.isTouchEnded=!1,r.callMethod("_handleRefresherTouchstart",l)}}function F(e,r){var a=b(e),t=R(r),s=t.getDataset(),i=s.refresherthreshold,l=s.refresherf2threshold,f=d(s.refresherf2enabled),h=d(s.isios),o=t.getState(),n=d(s.watchtouchdirectionchange),v={},c=0,u=!1;if(n){v=C(e,t),c=v.currentDis,u=v.isDown;var D=u?"top":"bottom";u==o.oldTouchDirection&&u!=o.oldEmitedTouchDirection&&(r.callMethod("_handleTouchDirectionChange",{direction:D}),o.oldEmitedTouchDirection=u),o.oldTouchDirection=u}if(Y(e,t,1)||!L(e,a,o,s))return m(o,r,!1),!0;if(v=C(e,t),c=v.currentDis,u=v.isDown,c<0)return M(0,t,o,!1),m(o,r,!1),!0;if(u&&!o.disabledBounce)return r.callMethod("_handleScrollViewBounce",{bounce:!1}),o.disabledBounce=!0,m(o,r,u),!u;M(c,t,o,!1);var g=o.refresherStatus,H=d(s.oldistouchmoving),X=d(s.hastouchmove),_=o.isTouchmoving;return o.refresherStatus=c>=i?f&&c>l?"goF2":"releaseToRefresh":"default",_||(o.isTouchmoving=!0,_=!0),o.isTouchEnded&&(o.isTouchEnded=!1),X&&r.callMethod("_handleWxsPullingDown",{moveDis:c,diffDis:v.diffDis}),(g==null||g!=o.refresherStatus||H!=_)&&r.callMethod("_handleRefresherTouchmove",c,a),m(o,r,u),!u}function E(e,r){var a=b(e),t=R(r),s=t.getDataset(),i=t.getState();if(i.disabledBounce&&(r.callMethod("_handleScrollViewBounce",{bounce:!0}),i.disabledBounce=!1),!Y(e,t,2)&&(i.reachMaxAngle=!0,i.hitReachMaxAngleCount=0,i.fixedIsTopHitCount=0,!!i.isTouchmoving)){var l=i.refresherStatus,f=i.moveDis,h=t.getDataset().refresherthreshold,o=C(e,t).currentDis;if(o>=h&&l==="releaseToRefresh"||(i.isTouchmoving=!1),r.callMethod("_handleRefresherTouchend",o),i.isTouchEnded=!0,!(f<h)){var n=!1;o>=h&&(o=h,n=!0),M(o,t,i,n)}}}function S(){if(!navigator)return!1;if(p!=-1)return p;var e=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"];return p=e.every(function(r){return navigator.userAgent.indexOf(r)<0}),p}var x=!1;function q(e,r){S()&&(P(e,r),x=!0)}function y(e,r){!S()||!x||F(e,r)}function j(e,r){S()&&(E(e,r),x=!1)}function J(e,r){S()&&(x=!1)}function M(e,r,a,t){e=e||0,a.moveDis!=e&&(a.moveDis=e,O("translateY("+e+"px)",r,t,""))}function O(e,r,a,t){var s=r.getDataset();d(s.refreshernotransform)||(e=e=="translateY(0px)"?"none":e,r.requestAnimationFrame(function(){var i={transform:e};a&&(i.transition="transform .1s linear"),t.length&&(i.transition=t),r.setStyle(i)}))}function C(e,r){var a=r.getState(),t=parseFloat(r.getDataset().refresherthreshold),s=parseFloat(r.getDataset().refresheroutrate),i=parseFloat(r.getDataset().refresherpullrate),l=b(e),f=!a.startY||a.startY=="NaN"?A:a.startY,h=l.touchY-f,o=a.oldMoveDis||0;a.oldMoveDis=h;var n=h-o;return n>0&&(n=n*i,T>t&&(n=n*(1-s))),n=n>100?n/100:n>20?n/2.2:n,T+=n,T=Math.max(0,T),{currentDis:T,diffDis:n,isDown:n>0}}function b(e){var r=e;return e.touches&&e.touches.length?r=e.touches[0]:e.changedTouches&&e.changedTouches.length?r=e.changedTouches[0]:e.datail&&e.datail!={}&&(r=e.datail),{touchX:r.clientX,touchY:r.clientY}}function R(e){var r=e.getState().currentIns;return r||e.callMethod("_handlePropUpdate"),r}function Y(e,r,a){var t=r.getDataset(),s=r.getState(),i=d(t.loading),l=d(t.usechatrecordmode),f=d(t.refresherenabled),h=d(t.usecustomrefresher),o=d(t.usepagescroll),n=parseFloat(t.pagescrolltop),v=parseFloat(t.scrolltop),c=o?n:v,u=!1,D=!1;D&&c==(s.startScrollTop||0)&&c<=105&&(u=!0);var g=s.fixedIsTopHitCount||0;return u?(g++,g<=2&&(u=!1),s.fixedIsTopHitCount=g):s.fixedIsTopHitCount=0,D&&a===0&&(s.startScrollTop=c||0),D&&a===2&&(u=!0),i||l||!f||!h||o&&h&&n>5&&!u||!o&&h&&v>5&&!u}function L(e,r,a,t){var s=t.refreshermaxangle,i=d(t.refresheraecc),l=a.lastTouch,f=a.reachMaxAngle,h=a.oldMoveDis;if(!l)return!0;if(s>=0&&s<=90&&l){if((!h||h<1)&&!i&&f!=null&&!f)return!1;var o=Math.abs(r.touchX-l.touchX),n=Math.abs(r.touchY-l.touchY),v=Math.sqrt(Math.pow(o,2)+Math.pow(n,2));if((o||n)&&o>1){var c=Math.asin(n/v)/Math.PI*180;if(c<s){var u=a.hitReachMaxAngleCount||0;return a.hitReachMaxAngleCount=++u,a.hitReachMaxAngleCount>2&&(a.lastTouch=r,a.reachMaxAngle=!1),!1}}}return a.lastTouch=r,!0}function m(e,r,a){var t=e.onPullingDown||!1;t!=a&&r.callMethod("_handleWxsPullingDownStatusChange",a),e.onPullingDown=a}function d(e){return e=(typeof e=="string"?JSON.parse(e):e)||!1,e==!0||e=="true"}B.exports={touchstart:P,touchmove:F,touchend:E,mousedown:q,mousemove:y,mouseup:j,mouseleave:J,propObserver:W}});return U();})();

__wxsModules["2f992f8c"]=(()=>{var u=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports);var i=u((l,t)=>{var n={abbr:!0,b:!0,big:!0,code:!0,del:!0,em:!0,i:!0,ins:!0,label:!0,q:!0,small:!0,span:!0,strong:!0,sub:!0,sup:!0};t.exports={isInline:function(r,e){return n[r]||(e||"").indexOf("display:inline")!==-1}}});return i();})();
