<template>
  <!-- 客户文件模块 -->
  <view class="customer-details-container">
    <!-- 面包屑 -->
    <view class="section-box s-p-b" v-if="routes.length>1">
      <uni-breadcrumb separator="/">
        <uni-breadcrumb-item
          v-for="(route, index) in routes"
          :key="index"
          @click="handleBreadcrumbClick(index)"
        >
          {{ route.name }}
        </uni-breadcrumb-item>
      </uni-breadcrumb>
    </view>
    <!-- 当前 -->
    <view class="section-box s-p-b">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">当前</view>
      </view>
      <uni-icons
        type="plusempty"
        size="20"
        color="#4D5BDE"
        @click="handeAddFileClick"
      ></uni-icons>
    </view>
    <!-- 文件列表 -->
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
        <!-- 添加空文件夹提示在模板中替换 -->
        <view class="empty-folder" v-if="currentDisplayData.length === 0">
          <image class="empty-icon" src="/static/file/unknown.svg"></image>
          <text class="empty-text">该文件夹为空</text>
        </view>
        <block v-for="(item, index) in currentDisplayData" :key="index">
          <view
            class="todo-item"
            @click.stop="item.type === 'dir' ? handleFileClick(item) : null"
          >
            <!-- 文件图标/图片预览 -->
            <view class="file-thumbnail">
              <!-- 对于图片类型，直接显示缩略图 -->
              <image
                v-if="isImageFile(item.name)"
                class="file-preview"
                :src="item.url"
                mode="aspectFill"
              ></image>
              <!-- 对于非图片类型，显示文件图标 -->
              <image
                v-else
                class="flie-icon"
                :src="item.type === 'dir' ? '/static/file/document.svg' : getFileIconPath(item.name)"
              ></image>
            </view>
            <view class="content-wrapper">
              <uni-easyinput
                v-if="item.isShowInput"
                trim="all"
                v-model="item.name"
                @change="handelInputSetName(item)"
                focus
              ></uni-easyinput>
              <view class="todo-title" v-else @click="handleFilePreview(item)">{{ item.name }}</view>
              <!-- 添加上传时间显示 -->
              <view class="title-row">
                <view class="upload-time">{{ filterDateTime(item.created_at) }}</view>
                <!-- <view class="file-size" v-if="item.size">{{ formatFileSize(item.size) }}</view> -->
              </view>
            </view>
            <uni-icons
              v-if="item.type !== 'dir'"
              type="more-filled"
              size="20"
              color="#E2E4E9"
              @click="handeOpenClick(item)"
            ></uni-icons>
          </view>
          <!-- 分割线：除最后一项外 -->
          <view v-if="index !== fileList.length - 1" class="item-divider"></view>
        </block>
      </view>
    </transition>
    <ActionSheet
      v-if="operationFileShow"
      v-model:show="operationFileShow"
      :actions="actions"
      title="文件操作"
      @select="onFileSelect"
    />
    <!-- xe-upload 组件（隐藏，用于文件选择） -->
    <xe-upload 
      ref="xeUpload" 
      @callback="handleXeUploadCallback"
    />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import XeUpload from "@/uni_modules/xe-upload/components/xe-upload/xe-upload.vue";
import {
  generateFileUploadSignature,
  saveOrEditAttachment,
  deleteAttachment,
} from "@/http/attachment.js";
import { filterDateTime } from "@/utils/formatTime.js";
import { 
  isImageFile, 
  formatFileSize,
  openWebOfficePreview,
  getFileIconPath,
  getMimeType,
  getFileExtension,
  downloadFile,
 } from "@/utils/fileUtils.js";
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具
import { useFileUpload } from '@/utils/useFileUpload.js'; // 导入文件上传hooks
// 使用文件上传hooks
  const {
    chooseAndUpload,
    uploadProgress, 
    setXeUploadRef, 
    handleXeUploadCallback 
  } = useFileUpload();

const emit = defineEmits(["refresh"]);
const props = defineProps({
  // 客户文件列表数据
  fileList: {
    type: Array,
    default: [],
  },
  customer_id:String,
  customer_parent_id:String | Number,
});
const currentFile = ref(null); // 当前操作的文件
const operationFileShow = ref(false);
// 修改状态管理
const expandStates = ref({
  current: true,
  finished: true,
});
const routes = ref([]);
// 面包屑导航相关
const pathStack = ref([]);
// xe-upload 组件引用
const xeUpload = ref(null);

const actions = ref([
  { name: "预览" },
  { name: "下载" },
  { name: "重命名" },
  { name: "删除", color: "danger" },
  // { name: '重命名', subname: '描述信息' },
  // { name: '禁用选项', disabled: true },
  // { name: '加载选项', loading: true },
]);

// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
  expandStates.value[type] = !expandStates.value[type];
};
// 弹出客户文件下拉
const handeOpenClick = (item) => {
  currentFile.value = item;
  operationFileShow.value = true;
};

// 获取当前路径下的数据 修改计算属性，使其能够根据当前选中的文件夹显示对应的数据
const currentDisplayData = computed(() => {
  // 如果有路径栈且长度大于1，说明已经进入了某个文件夹
  if (pathStack.value && pathStack.value.length > 1) {
    // 获取当前文件夹的数据
    const currentFolder = pathStack.value[pathStack.value.length - 1];
    // 返回当前文件夹的子项，如果没有则返回空数组
    return currentFolder.data || [];
  }
  // 如果没有选中文件夹，则使用原来的逻辑
  // 查找第一个文件夹
  const firstFolder = props.fileList.find(item => item.type === "dir");
  // 如果找到文件夹并且有子项，返回子项
  if (firstFolder && firstFolder.children && firstFolder.children.length > 0) {
    return firstFolder.children;
  }
  // 否则返回根目录数据
  return props.fileList;
});

// 点击文件夹
const handleFileClick = (items) => {
  if (items.type !== "dir") return;
  // 初始化pathStack，如果为空
  if (pathStack.value.length === 0) {
    pathStack.value = [{
      id: 0,
      name: "全部文件",
      data: props.fileList,
    }];
  }
  // 无论是否有子项，都进入文件夹
  pathStack.value.push({
    id: items.id,
    name: items.name || items.title, // 优先使用 name，兼容不同数据结构
    data: items.children || [], // 如果没有 children，则使用空数组
  });
  // 更新面包屑导航
  updateBreadcrumb();
  // 如果有子文件夹，自动进入第一个子文件夹
  if (items.children && items.children.length > 0) {
    const firstSubFolder = items.children.find(child => child.type === "dir");
    if (firstSubFolder) {
      // 使用 nextTick 确保 DOM 更新后再触发点击
      nextTick(() => {
        handleFileClick(firstSubFolder);
      });
    }
  } else {
    console.log('当前文件夹为空');
  }
};

// 更新面包屑
const updateBreadcrumb = () => {
  routes.value = pathStack.value.map((item) => ({
    to: `#${item.id}`,
    name: item.name,
  }));
};

// 点击面包屑
const handleBreadcrumbClick = (index) => {
  pathStack.value = pathStack.value.slice(0, index + 1);
  updateBreadcrumb();
};

// 获取当前文件夹ID
const getCurrentFolderId = () => {
  // 如果pathStack为空或只有根目录，返回undefined（表示在根目录）
  if (pathStack.value.length <= 1) {
    return undefined;
  }
  // 否则返回当前文件夹的ID
  return pathStack.value[pathStack.value.length - 1].id;
};

/**
 * 选取文件进行上传
 */
const handeAddFileClick = async () => {
  console.log('🚀 开始选择文件上传...')
  try {
    // 使用 hooks 选择并上传文件（在调用签名接口前验证文件格式）
    const results = await chooseAndUpload('file', {
      count: 9, // 最多选择9个文件
      // 直接指定允许的文件格式
      // allowedTypes: [
      //   '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // 图片
      //   '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', // 文档
      //   '.txt', '.rtf', // 文本
      //   '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', // 视频
      //   '.mp3', '.wav', '.aac', '.flac', // 音频
      //   '.zip', '.rar', '.7z', '.tar', '.gz' // 压缩包
      // ],
      // #ifdef H5
      extension: [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // 图片
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', // 文档
        '.txt', '.rtf', // 文本
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', // 视频
        '.mp3', '.wav', '.aac', '.flac', // 音频
        '.zip', '.rar', '.7z', '.tar', '.gz' // 压缩包
      ],
      // #endif
      // #ifdef APP
      extension:'*',
      // #endif
    });
    if (results && results.length > 0) {
      console.log('✅ 文件上传成功:', results);
      // 批量保存附件信息到后端
      for (const result of results) {
        try {
          const attachmentData = {
            // type: 'file'
            // 获取当前文件夹ID
            classify: 1,
            parent_id: getCurrentFolderId() || props.customer_parent_id,
            classify_id: props.customer_id,
            name: result.fileName,
            type: 'file',
            url: result.url,
            size: result.size || 0,
          };
          const saveRes = await saveOrEditAttachment(attachmentData);
          if (saveRes.code === 0) {
            console.log('附件信息保存成功:', result.fileName);
          }
        } catch (saveError) {
          console.error('保存附件信息失败:', result.fileName, saveError);
        }
      }
      // 触发刷新事件，通知父组件刷新文件列表
      emit('refresh');
      uni.showToast({
        title: `上传成功`,
        icon: 'success',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('💥 文件上传过程出错:', error);
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none',
      duration: 3000
    });
  }
};

// 点击选项时触发，禁用或加载状态下不会触发
const onFileSelect = (item, index) => {
  if (!currentFile.value) return;
  switch (item.name) {
    case "预览":
    handleFilePreview(currentFile.value);
    operationFileShow.value = false;
      break;
    case "下载":
    oneClickDownload(currentFile.value);
      operationFileShow.value = false;
      break;
    case "删除":
      deleteFile(currentFile.value);
      operationFileShow.value = false;
      break;
    case "重命名":
      setRenameFile(currentFile.value);
      operationFileShow.value = false;
      break;
  }
};

/**
 * 处理文件预览
 * @param {Object} file - 文件对象
 */
 const handleFilePreview = async (file) => {
  try {
    // 调用预览服务，传入不同平台的预览回调
    await previewFile(file, {
      onWebOfficePreview: openWebOfficePreview,// H5端WebOffice预览
    });
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};

/**
 * 下载文件 - 调用公共下载函数
 * @param {Object} file - 文件对象，包含url和name属性
 */
 const oneClickDownload = async (file) => {
  try {
    await downloadFile(file.url, file.name);
  } catch (error) {
    console.error('下载失败:', error);
    // 公共函数内部已经处理了错误提示，这里不需要重复提示
  }
};
// 删除文件
const deleteFile = async (delItem) => {
  try {
    // 确保在删除操作前就将输入框状态设为false，防止闪现
    if(delItem) {
      delItem.isShowInput = false;
    }
    // 显示加载中，阻止用户操作，防止输入框闪现
    uni.showLoading({
      title: '删除中...'
    });
    const res = await deleteAttachment({
      id: delItem.id,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除文件成功",
        icon: "success",
      });
      emit("refresh");
    }
  } catch (error) {
    console.error("删除失败:", error);
    uni.showToast({
      title: "删除失败",
      icon: "none",
    });
  } finally {
    // 无论成功或失败，都隐藏加载状态
    uni.hideLoading();
  }
};

// 重命名
const setRenameFile = async (nameItem) => {
  nameItem.isShowInput = true;
};
// 输入框修改事件
const handelInputSetName = async (item) => {
  item.isShowInput = false;
  console.error("输入框修改事件", item);
  try {
    const res = await saveOrEditAttachment({
      id:item.id,
      classify: 1,
      name: item.name,
      classify_id: props.customer_id,
    });
    if (res.code === 0) {
      emit("refresh");
    }
  } catch (error) {
    console.error("重命名请求失败:", error);
  }
};

// 添加递归查找并更新文件夹数据的方法
const updateFolderDataInPathStack = (fileList) => {
  // 如果pathStack为空，不需要更新
  if (pathStack.value.length === 0) return;
  // 获取当前文件夹ID
  const currentFolderId = getCurrentFolderId();
  if (!currentFolderId) {
    // 如果是根目录，直接更新pathStack中的根目录数据
    if (pathStack.value.length > 0) {
      pathStack.value[0].data = fileList;
    }
    return;
  }
  // 递归查找当前文件夹并更新其数据
  const findAndUpdateFolder = (list, path = []) => {
    // 遍历当前层级的文件列表
    for (const item of list) {
      // 如果找到了当前文件夹
      if (item.id === currentFolderId) {
        // 更新pathStack中对应位置的数据
        const pathIndex = path.length;
        if (pathIndex < pathStack.value.length) {
          pathStack.value[pathIndex].data = item.children || [];
        }
        return true;
      }
      // 如果当前项是文件夹且有子项，递归查找
      if (item.type === 'dir' && item.children && item.children.length > 0) {
        const newPath = [...path, item.id];
        if (findAndUpdateFolder(item.children, newPath)) {
          return true;
        }
      }
    }
    return false;
  };
  // 开始递归查找
  findAndUpdateFolder(fileList);
};
// 添加对props.fileList的监听，当文件列表更新时，更新当前文件夹数据
// watch(() => props.fileList, (newFileList) => {
//   // 如果当前在某个文件夹中，需要更新该文件夹的数据
//   if (pathStack.value && pathStack.value.length > 0) {
//     // 递归查找并更新当前文件夹的数据
//     updateFolderDataInPathStack(newFileList);
//   }
// }, { deep: true });

// 组件挂载后设置 xe-upload 引用
onMounted(() => {
  if (xeUpload.value) {
    setXeUploadRef(xeUpload.value);
  }
});
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.customer-details-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .section-box {
    display: flex;
    align-items: center;
    margin: 15px 5px 15px 5px;
    padding: 0px 10px;
    .section-left {
      display: flex;
      align-items: center;
      .icon-down {
        width: 25px;
        height: 25px;
      }
    }
    .section-title {
      color: var(---, #787d86);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
  .s-p-b {
    justify-content: space-between;
  }
  .empty-folder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    .empty-icon {
      width: 48px;
      height: 48px;
      margin-bottom: 10px;
      opacity: 0.5;
    }
    .empty-text {
      color: #999;
      font-size: 14px;
    }
  }
  .todo-card {
    margin: 15px;
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;

    .todo-item {
      display: flex;
      align-items: center;
      margin: 10px 14px 0 14px;
      height: 56px; /* 设置固定高度为56px */
      max-height: 56px;
      /* 文件缩略图/图标区域 */
      .file-thumbnail {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        .flie-icon {
          width: 24px;
          height: 24px;
        }
      }
      .content-wrapper {
        flex: 1;
        margin-left: 6px;
        overflow: hidden;
      }
      .todo-title {
        color: var(---, #000);
        font-size: 14px;
        white-space: nowrap; /* 确保文本不换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        text-overflow: ellipsis; /* 显示省略号 */
        max-width: 100%; /* 确保宽度不超过父容器 */
      }
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2px;
        color: var(---, #adb1ba);
        font-size: 10px;
        .upload-time {
          flex: 1;
        }
        .file-size {
          margin-left: 8px;
        }
      }
    }
    /* 分割线 */
    .item-divider {
      height: 1px;
      background-color: #f0f0f0;
      margin-left: 44px; /* 14px 左外边距 + 24px 缩略图 + 6px 间距 */
      margin-right: 16px;
    }
  }
}
</style>
