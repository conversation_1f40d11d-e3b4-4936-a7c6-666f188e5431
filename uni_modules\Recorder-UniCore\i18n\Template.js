/*
Recorder ../app-support-sample/demo_UniApp/uni_modules/Recorder-UniCore/i18n/Template.js
https://github.com/xiangyuecn/Recorder

Usage: Recorder.i18n.lang="Your-Language-Name" or "your-language"

Desc: This file is a language translation template file. After copying and renaming, translate the text into the corresponding language. 此文件为语言翻译模板文件，复制并改名后，将文本翻译成对应语言即可。

注意：请勿修改//@@打头的文本行；以下代码结构由/src/package-i18n.js自动生成，只允许在字符串中填写翻译后的文本，请勿改变代码结构；翻译的文本如果需要明确的空值，请填写"=Empty"；文本中的变量用{n}表示（n代表第几个变量），所有变量必须都出现至少一次，如果不要某变量用{n!}表示

Note: Do not modify the text lines starting with //@@; The following code structure is automatically generated by /src/package-i18n.js, only the translated text is allowed to be filled in the string, please do not change the code structure; If the translated text requires an explicit empty value, please fill in "=Empty"; Variables in the text are represented by {n} (n represents the number of variables), all variables must appear at least once, if a variable is not required, it is represented by {n!}
*/
(function(factory){
	var browser=typeof window=="object" && !!window.document;
	var win=browser?window:Object; //非浏览器环境，Recorder挂载在Object下面
	factory(win.Recorder,browser);
}(function(Recorder,isBrowser){
"use strict";
var i18n=Recorder.i18n;

//@@User Code-1 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-1 End @@

//@@Exec i18n.lang="Your-Language-Name";
Recorder.CLog('Import Page[Recorder_UniCore] lang="Your-Language-Name"');

//@@Exec i18n.alias["Your-Language-Name"]="your-language";

var putSet={lang:"your-language"};

//@@Exec i18n.data["rtl$your-language"]=false;
i18n.data["desc-page-Recorder_UniCore$your-language"]="This file is a language translation template file. After copying and renaming, translate the text into the corresponding language. 此文件为语言翻译模板文件，复制并改名后，将文本翻译成对应语言即可。";
//@@Exec i18n.GenerateDisplayEnglish=true;



//*************** Begin srcFile=../app-support-sample/demo_UniApp/uni_modules/Recorder-UniCore/app-uni-support.js ***************
i18n.put(putSet,
[ //@@PutList 

//@@zh="微信小程序中需要：{1}"
//@@en="WeChat miniProgram requires: {1}"
//@@Put0
 "RXs7:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="Recorder-UniCore目前只支持：H5、APP(Android iOS)、MP-WEIXIN，其他平台环境需要自行编写适配文件实现接入"
//@@en="Recorder-UniCore currently only supports: H5, APP (Android iOS), MP-WEIXIN, other platform environments need to write their own adaptation files to achieve access"
,"4ATo:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniWebViewActivate 需要传入当前页面或组件的this对象作为参数"
//@@en="RecordApp.UniWebViewActivate needs to pass in the this object of the current page or component as a parameter"
,"GwCz:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniWebViewActivate 发生不应该出现的错误（可能需要升级插件代码）："
//@@en="An error occurred in RecordApp.UniWebViewActivate that should not occur (the plug-in code may need to be upgraded): "
,"ipB3:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniWebViewActivate 已切换当前页面或组件的renderjs所在的WebView"
//@@en="RecordApp.UniWebViewActivate has switched the WebView where the renderjs of the current page or component is located"
,"WpKg:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniRenderjsRegister 发生不应该出现的错误（可能需要升级插件代码）："
//@@en="An error occurred in RecordApp.UniRenderjsRegister that should not occur (the plugin code may need to be upgraded): "
,"Uc9E:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniRenderjsRegister 重复注册当前页面renderjs模块，一个组件内只允许一个renderjs模块进行注册"
//@@en="RecordApp.UniRenderjsRegister repeatedly registers the renderjs module of the current page. Only one renderjs module is allowed to be registered in a component"
,"mzKj:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniRenderjsRegister 已注册当前页面renderjs模块"
//@@en="RecordApp.UniRenderjsRegister has registered the renderjs module of the current page"
,"7kJS:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="严重兼容性问题：无法获取页面或组件this.$root.$scope或.$page"
//@@en="Serious compatibility issue: Unable to get page or component this.$root.$scope or .$page"
,"KpY6:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需要先调用RecordApp.UniWebViewActivate方法"
//@@en="You need to call the RecordApp.UniWebViewActivate method first"
,"AGd7:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需先调用RecordApp.RequestPermission方法"
//@@en="You need to call the RecordApp.RequestPermission method first"
,"7ot0:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需重新调用RecordApp.RequestPermission方法"
//@@en="The RecordApp.RequestPermission method needs to be called again"
,"VsdN:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="检测到有其他页面或组件调用了RecordApp.UniPageOnShow（WvCid={1}），但未调用过RecordApp.UniWebViewActivate（当前WvCid={2}），部分功能会继续使用之前Activate的WebView和组件，请确保这是符合你的业务逻辑，不是因为忘记了调用UniWebViewActivate"
//@@en="It is detected that another page or component has called RecordApp.UniPageOnShow (WvCid={1}), but RecordApp.UniWebViewActivate (current WvCid={2}) has not been called. Some functions will continue to use the previously Activated WebView and components. Please make sure This is in line with your business logic, not because you forgot to call UniWebViewActivate"
,"SWsy:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="{1}未正确查询到节点，将使用传入的当前页面或组件this的$el.parentNode作为组件根节点。如果template下存在多个根节点(vue3 multi-root)，尽量在最外面再套一层view来避免兼容性问题"
//@@en="{1} does not query the node correctly, and will use the current page or component this's $el.parentNode as the component root node. If there are multiple root nodes under the template (vue3 multi-root), try to add another layer of view on the outermost to avoid compatibility issues"
,"dX7B:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}需在renderjs中调用并且传入当前模块的this"
//@@en="{1} needs to be called in renderjs and pass in this of the current module"
,"dX5B:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}需要传入当前页面或组件的this对象作为参数"
//@@en="{1} needs to pass in the this object of the current page or component as a parameter"
,"dX6B:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="当前不是App逻辑层"
//@@en="Currently it is not the App logic layer"
,"TfJX:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前还未调用过RecordApp.UniWebViewActivate"
//@@en="RecordApp.UniWebViewActivate has not been called yet"
,"peIm:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未找到此页面renderjs所在的WebView"
//@@en="The WebView where renderjs for this page is not found"
,"qDo1:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，不可以调用RecordApp.UniWebViewEval"
//@@en=", RecordApp.UniWebViewEval cannot be called"
,"igw2:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前不是App逻辑层"
//@@en="Currently it is not the App logic layer"
,"lU1W:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前还未调用过RecordApp.UniWebViewActivate"
//@@en="RecordApp.UniWebViewActivate has not been called yet"
,"mSbR:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未找到此页面renderjs所在的WebView Cid"
//@@en="The WebView Cid where renderjs for this page is not found"
,"6Iql:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，不可以调用RecordApp.UniWebViewVueCall"
//@@en=", RecordApp.UniWebViewVueCall cannot be called"
,"TtoS:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="renderjs中未import导入RecordApp"
//@@en="RecordApp is not imported in renderjs"
,"U1Be:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="renderjs中的mounted内需要调用RecordApp.UniRenderjsRegister"
//@@en="RecordApp.UniRenderjsRegister needs to be called in mounted in renderjs"
,"Bcgi:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="没有找到组件的renderjs模块"
//@@en="The renderjs module for the component was not found"
,"URyD:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="{1}连接renderjs超时"
//@@en="{1} connection renderjs timeout"
,"KQhJ:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="{1}处理超时"
//@@en="{1} processing timeout"
,"RDcZ:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="需要在页面中提供一个renderjs，在里面import导入RecordApp、录音格式编码器、可视化插件等"
//@@en="You need to provide a renderjs in the page, and import RecordApp, recording format encoder, visualization plug-in, etc."
,"TSmQ:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需在renderjs中import {1}"
//@@en="Need to import {1} in renderjs"
,"AN0e:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="不应该出现的MainReceiveBind重复绑定"
//@@en="MainReceiveBind duplicate binding that should not occur"
,"vEgr:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="从renderjs发回数据但UniMainCallBack回调不存在："
//@@en="Sending data back from renderjs but UniMainCallBack callback does not exist: "
,"kZx6:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="[MainReceive]从renderjs发回未知数据："
//@@en="[MainReceive] Unknown data sent back from renderjs: "
,"ZHwv:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="只允许在renderjs中调用RecordApp.UniWebViewSendBigBytesToMain"
//@@en="Only allowed to call RecordApp.UniWebViewSendBigBytesToMain in renderjs"
,"MujG:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="renderjs中的mounted内需要调用RecordApp.UniRenderjsRegister才能调用RecordApp.UniWebViewSendBigBytesToMain"
//@@en="RecordApp.UniRenderjsRegister needs to be called in mounted in renderjs to call RecordApp.UniWebViewSendBigBytesToMain"
,"kE91:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无效的BigBytes回传数据"
//@@en="Invalid BigBytes return data"
,"CjMb:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="保存文件{1}失败："
//@@en="Failed to save file {1}: "
,"UqfI:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="当前环境未支持保存本地文件"
//@@en="The current environment does not support saving local files"
,"kxOd:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh=" | RecordApp的uni-app支持文档和示例：{1} "
//@@en=" | RecordApp’s uni-app support documentation and examples: {1}"
,"1f2V:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="当前录音由原生录音插件提供支持"
//@@en="Current recording is powered by native recording plug-in"
,"XSYY:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前录音由uts插件提供支持"
//@@en="Current recording is powered by uts plugin"
,"nnM6:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前已配置RecordApp.UniWithoutAppRenderjs，必须提供原生录音插件或uts插件才能录音，请参考RecordApp.UniNativeUtsPlugin配置"
//@@en="RecordApp.UniWithoutAppRenderjs is currently configured. A native recording plug-in or uts plug-in must be provided to record. Please refer to the RecordApp.UniNativeUtsPlugin configuration"
,"fqhr:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前RecordApp运行在逻辑层中（性能会略低一些，可视化等插件不可用）"
//@@en="Currently RecordApp runs in the logical layer (performance will be slightly lower, and plug-ins such as visualization are not available) "
,"xYRb:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未找到当前页面renderjs所在的WebView"
//@@en="The WebView where renderjs of the current page is located is not found"
,"S3eF:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前RecordApp运行在renderjs所在的WebView中（逻辑层中只能做有限的实时处理，可视化等插件均需要在renderjs中进行调用）"
//@@en="The current RecordApp runs in the WebView where renderjs is located (only limited real-time processing can be done in the logic layer, and visualization and other plug-ins need to be called in renderjs) "
,"0hyi:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="，请检查此页面代码中是否编写了lang=renderjs的module，并且调用了RecordApp.UniRenderjsRegister；如果确实没有renderjs，比如nvue页面，请设置RecordApp.UniWithoutAppRenderjs=true并且搭配配套的原生插件在逻辑层中直接录音"
//@@en=", please check whether the module with lang=renderjs is written in the code of this page and RecordApp.UniRenderjsRegister is called; if there is indeed no renderjs, such as nvue page, please set RecordApp.UniWithoutAppRenderjs=true and use the matching native plug-in to record directly in the logic layer"
,"e6Mo:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="【在App内使用{1}的授权许可】"
//@@en="[License for use of {1} within the App] "
,"FabE:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="已购买原生录音插件，获得授权许可"
//@@en="Purchased the native recording plug-in and obtained the license"
,"w37G:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="已购买uts插件，获得授权许可"
//@@en="Purchased uts plug-in and obtained license"
,"e71S:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="UniAppUseLicense填写无效，如果已获取到了商用授权，请填写：{1}，否则请使用空字符串"
//@@en="UniAppUseLicense is invalid. If you have obtained a commercial license, please fill in: {1}, otherwise please use an empty string"
,"aPoj:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="未找到Canvas：{1}，请确保此DOM已挂载（可尝试用$nextTick等待DOM更新）"
//@@en="Canvas not found: {1}, please make sure this DOM is mounted (try $nextTick to wait for DOM update) "
,"k7im:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="RecordApp.UniFindCanvas未适配当前环境"
//@@en="RecordApp.UniFindCanvas does not adapt to the current environment"
,"yI24:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未配置RecordApp.UniNativeUtsPlugin原生录音插件"
//@@en="RecordApp.UniNativeUtsPlugin native recording plug-in is not configured"
,"H753:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="renderjs中不支持设置RecordApp.UniNativeUtsPlugin"
//@@en="Setting RecordApp.UniNativeUtsPlugin is not supported in renderjs"
,"l6sY:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="当前App未打包进双端原生插件[{1}]，尝试加载单端[{2}]"
//@@en="The current App is not packaged into the dual-end native plug-in [{1}], try to load the single-end [{2}]"
,"kSjQ:"+ //args: {1}-{2}
       "" /** TODO: translate to your-language **/

//@@zh="已加载原生录音插件[{1}]"
//@@en="Native recording plugin loaded [{1}]"
,"Xh1W:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="配置了RecordApp.UniNativeUtsPlugin，但当前App未打包进原生录音插件[{1}]"
//@@en="RecordApp.UniNativeUtsPlugin is configured, but the current App is not packaged with the native recording plug-in [{1}]"
,"SCW9:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="提供的RecordApp.UniNativeUtsPlugin值不是RecordApp的uts原生录音插件"
//@@en="The provided RecordApp.UniNativeUtsPlugin value is not RecordApp’s uts native recording plug-in"
,"TGMm:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需在App逻辑层中调用原生插件功能"
//@@en="The native plug-in function needs to be called in the App logic layer"
,"MrBx:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音，不可以调用{1}"
//@@en="Recording has not started and {1} cannot be called"
,"0FGq:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="需先调用RecordApp.UniWebViewActivate，然后才可以调用RequestPermission"
//@@en="RecordApp.UniWebViewActivate needs to be called first, and then RequestPermission can be called"
,"PkQ2:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不应当出现的非H5权限请求"
//@@en="Non-H5 permission requests that should not appear"
,"Jk72:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="正在调用plus.ios@AVAudioSession请求iOS原生录音权限"
//@@en="Calling plus.ios@AVAudioSession to request iOS native recording permissions"
,"Y3rC:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="项目配置中未声明iOS录音权限{1}"
//@@en="iOS recording permission {1} is not declared in the project configuration"
,"9xoE:"+ //args: {1}
       "" /** TODO: translate to your-language **/

//@@zh="已获得iOS原生录音权限"
//@@en="Obtained iOS native recording permissions"
,"j15C:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="plus.ios请求录音权限，状态值: "
//@@en="plus.ios requests recording permission, status value: "
,"iKhe:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="正在调用plus.android.requestPermissions请求Android原生录音权限"
//@@en="Calling plus.android.requestPermissions to request Android native recording permissions"
,"7Noe:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="已获得Android原生录音权限："
//@@en="Obtained Android native recording permission: "
,"Bgls:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="plus.android请求录音权限：无权限"
//@@en="plus.android requests recording permission: No permission"
,"Ruxl:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="plus.android请求录音权限出错："
//@@en="plus.android error in requesting recording permission: "
,"0JQw:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="调用plus的权限请求出错："
//@@en="An error occurred in the permission request to call plus: "
,"Mvl7:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="用户拒绝了录音权限"
//@@en="User denied recording permission"
,"0caE:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="正在调用原生插件请求录音权限"
//@@en="Calling the native plug-in to request recording permission"
,"Lx5r:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="已获得录音权限"
//@@en="Recording permission obtained"
,"Lx6r:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无录音权限"
//@@en="No recording permission"
,"Lx7r:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无法调用RequestPermission："
//@@en="Unable to call RequestPermission: "
,"ksoA:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无法连接到renderjs"
//@@en="Unable to connect to renderjs"
,"KnF0:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="需先调用RecordApp.UniWebViewActivate，然后才可以调用Start"
//@@en="RecordApp.UniWebViewActivate needs to be called first, and then Start can be called"
,"XCMU:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不应当出现的非H5录音Start"
//@@en="Start of non-H5 recordings that should not appear"
,"rSLO:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无法调用Start："
//@@en="Unable to call Start: "
,"Bjx9:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音，但收到renderjs回传的onRecEncodeChunk"
//@@en="Recording did not start, but onRecEncodeChunk returned by renderjs was received"
,"MTdp:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音，但收到Uni Native PCM数据"
//@@en="Recording did not start, but Uni Native PCM data was received"
,"BjGP:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音，但收到UniNativeUtsPlugin PCM数据"
//@@en="Recording did not start, but UniNativeUtsPlugin PCM data was received"
,"byzO:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音"
//@@en="Recording not started"
,"YP4V:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不应当出现的非H5录音Stop"
//@@en="Stop non-H5 recordings that should not appear"
,"TPhg:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="未开始录音"
//@@en="Recording not started"
,"pP4O:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="无法调用Stop："
//@@en="Unable to call Stop: "
,"H6cq:"+ //no args
       "" /** TODO: translate to your-language **/

//@@zh="不应该出现的renderjs发回的文件数据丢失"
//@@en="The file data sent back by renderjs should not be lost"
,"gomD:"+ //no args
       "" /** TODO: translate to your-language **/

]);
//*************** End srcFile=../app-support-sample/demo_UniApp/uni_modules/Recorder-UniCore/app-uni-support.js ***************

//@@User Code-2 Begin 手写代码放这里 Put the handwritten code here @@

//@@User Code-2 End @@

}));