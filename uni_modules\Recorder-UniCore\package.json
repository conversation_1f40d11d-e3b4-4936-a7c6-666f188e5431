{"id": "Recorder-UniCore", "displayName": "跨平台Recorder录音插件：支持多种格式、音频可视化、实时上传、语音识别", "version": "1.0.250331", "description": "支持H5、Android iOS App、微信小程序；mp3 wav pcm g711a g711u ogg amr 录音格式；实时帧回调处理 音频转码 波形动画显示 ASR语音转文字 无录制时长限制", "keywords": ["Recorder-UniCore", "recorder-core", "RecordApp", "record", "recording"], "repository": "https://github.com/xiangyuecn/Recorder", "engines": {"HBuilderX": "^3.6.11"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "753610399"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "录音权限"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y", "app-uvue": "n", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "n", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "u", "联盟": "u"}}}}}