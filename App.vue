<script>
// import versionChecker from '@/utils/versionChecker.js';
import pushNotificationService from '@/utils/pushNotificationService.js';
export default {
  // 捕获 app error
  onError (err) {
    // 页面显示时检测版本更新
    // versionChecker.checkVersion();
  },
  onLaunch: function () {
    console.log("App Launch");
    // http://local.spaceddd.com/pages/template/customer-details/index?customer_id=32
    // 获取 URL 中的 token 参数
    // #ifdef H5
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('authorization'); // 假设参数名为 token
    if (token) {
      // 存储 token
      uni.setStorageSync('token', token);
      console.log('已获取并存储 token');
    }
    const id = urlParams.get('id'); // 假设参数名为 id
    if (id) {
      // 存储 id
      uni.setStorageSync('id', id);
      console.log('已获取并存储 id');
    }
    // #endif

    // #ifdef APP-PLUS
    // 初始化推送通知服务
    pushNotificationService.init();
    // #endif
  },
  onShow: function () {
    console.log("App Show");
    // #ifdef APP-PLUS
    // 检查是否有待处理的推送跳转
    pushNotificationService.clearAppBadge();
    // #endif
  },
  onHide: function () {
    console.log("App Hide");
  },
  onPageNotFound: function (params) {
	  uni.showToast({
      title: "页面不存在",
      icon: "none",
    });
    // 使用uni.redirectTo进行无动画的页面跳转
    uni.redirectTo({
      url: "pages/error/404",
    });
  },
};
</script>

<style lang="scss">
// 全局样式
@import '/styles/md.scss';
/* 全局样式 - 添加滚动锁定样式 */
/* #ifdef H5 */
.action-sheet-scroll-lock {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}
/* #endif */

/* ===== TabBar 最大宽度限制样式 ===== */
/* H5环境下的tabBar样式覆盖 */
.uni-app--showtabbar{
  uni-page-wrapper{
    &::after {
      background: #f8f9fa;
    }
  }
  .uni-tabbar {
    max-width: 750px !important;
    margin: 0 auto !important;
  }
}
uni-app {
  uni-page {
    uni-page-wrapper{
      uni-page-body {
        background-color: #f8f9fa;
        min-height: 100%;
        height: auto;
      }
    }
  }
}

/* #ifdef H5 */
/* 媒体查询：电脑端（大于等于1024px宽度时） */
@media (min-width: 1024px) {
  .uni-app--showtabbar{
    uni-page-wrapper{
      &::after {
        height: 90px;
      }
    }
  }
}
/* #endif */
</style>