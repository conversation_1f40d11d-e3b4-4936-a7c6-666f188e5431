import PCMAudioPlayer from './audio_player.js';
// 定义默认的阿里云 TTS 配置
const defaultAliyunTtsConfig = {
    appkey: 'ZsmxFv9inn9RVE3N',
    token: uni.getStorageSync("aliyunToken"),
    url: 'wss://nls-gateway-cn-shenzhen.aliyuncs.com/ws/v1',
    voice: 'zhixiaoxia', // 默认使用艾夏音色
    format: 'PCM',    // 音频格式改为PCM
    sample_rate: 24000, // 采样率改为24000
    volume: 50,       // 音量
    speech_rate: 100,   // 语速
    pitch_rate: 50,    // 语调
    namespace: 'FlowingSpeechSynthesizer', // 命名空间
};

// 全局状态管理
let isCurrentlyPlaying = false;
let currentWs = null;
let currentPlayer = null;
let currentPlayingText = '';

/**
 * 生成32位随机字符串
 * @returns {string} 随机字符串
 */
function generateUUID() {
    let d = new Date().getTime();
    let d2 = (performance && performance.now && (performance.now() * 1000)) || 0;
    return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16;
        if (d > 0) {
            r = (d + r) % 16 | 0;
            d = Math.floor(d / 16);
        } else {
            r = (d2 + r) % 16 | 0;
            d2 = Math.floor(d2 / 16);
        }
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
}

/**
 * 生成 WebSocket 消息的头部信息
 * @param {Object} config - 配置对象
 * @param {string} task_id - 任务 ID
 * @returns {Object} 包含头部信息的对象
 */
const getHeader = (config, task_id) => {
    const timestamp = Date.now();
    return {
        message_id: generateUUID(),
        task_id: task_id,
        namespace: config.namespace,
        name: '',
        appkey: config.appkey
    };
};

/**
 * 清理所有现有播放资源
 * @param {WebSocket} ws - 要关闭的WebSocket连接
 * @param {PCMAudioPlayer} player - 要停止的播放器
 */
const cleanupResources = (ws, player) => {
    try {
        // 关闭WebSocket连接
        if (ws && ws.readyState !== WebSocket.CLOSED) {
            try {
                ws.close();
            } catch (e) {
                console.warn('关闭WebSocket失败:', e);
            }
        }
        
        // 停止播放器
        if (player) {
            try {
                player.stop();
            } catch (e) {
                console.warn('停止播放器失败:', e);
            }
        }
    } catch (e) {
        console.error('清理资源失败:', e);
    }
    
    // 重置全局状态
    isCurrentlyPlaying = false;
};

/**
 * 创建并返回语音合成相关方法
 * @param {Object} customConfig - 自定义的阿里云 TTS 配置
 * @returns {Object} 包含控制方法的对象
 */
export const createAliyunTts = (customConfig = {}) => {
    // 合并默认配置和自定义配置
    const aliyunTtsConfig = { ...defaultAliyunTtsConfig, ...customConfig };

    let ws = null;
    let task_id = null;
    let isSynthesisStarted = false;
    let player = new PCMAudioPlayer(aliyunTtsConfig.sample_rate);
    let onPlaybackEndCallback = null;
    let synthesisStartedPromise = null;
    let synthesisStartedResolve = null;

    /**
     * 发送 RunSynthesis 指令
     * @param {string} text - 要合成的文本
     */
    const sendRunSynthesis = async (text) => {
        // 存储当前播放文本，用于避免重复播放相同内容
        currentPlayingText = text;
        await synthesisStartedPromise;
        if (ws && isSynthesisStarted) {
            const header = getHeader(aliyunTtsConfig, task_id);
            const params = {
                header: { ...header, name: 'RunSynthesis' },
                payload: {
                    text
                }
            };
            ws.send(JSON.stringify(params));
        } else {
            console.error('Cannot send RunSynthesis: Synthesis has not started');
        }
    };

    /**
     * 发送 StopSynthesis 指令
     */
    const sendStopSynthesis = async () => {
        await synthesisStartedPromise;
        if (ws && isSynthesisStarted) {
            const header = getHeader(aliyunTtsConfig, task_id);
            const params = {
                header: { ...header, name: 'StopSynthesis' }
            };
            ws.send(JSON.stringify(params));
        } else {
            console.error('Cannot send StopSynthesis: Synthesis has not started');
        }
    };

    /**
     * 开始连接 WebSocket 并处理语音合成数据
     */
    const connectAndStartSynthesis = () => {
        // 在开始新连接前，清理现有资源
        if (currentWs && currentWs !== ws) {
            cleanupResources(currentWs, currentPlayer);
        }
        // 重置播放状态
        synthesisStartedPromise = new Promise((resolve) => {
            synthesisStartedResolve = resolve;
        });
        // 标记当前正在播放
        isCurrentlyPlaying = true;
        // 创建新的WebSocket连接
        ws = new WebSocket(`${aliyunTtsConfig.url}?token=${aliyunTtsConfig.token}`);
        currentWs = ws;
        currentPlayer = player;
        
        ws.binaryType = "arraybuffer";
        ws.onopen = () => {
            if (ws.readyState === WebSocket.OPEN) {
                task_id = generateUUID();
                const header = getHeader(aliyunTtsConfig, task_id);
                const params = {
                    header: { ...header, name: 'StartSynthesis' },
                    payload: {
                        voice: aliyunTtsConfig.voice,
                        format: aliyunTtsConfig.format,
                        sample_rate: aliyunTtsConfig.sample_rate,
                        volume: aliyunTtsConfig.volume,
                        speech_rate: aliyunTtsConfig.speech_rate,
                        pitch_rate: aliyunTtsConfig.pitch_rate,
                        enable_subtitle: true,
                        platform: 'javascript'
                    }
                };
                ws.send(JSON.stringify(params));
            }
        };
        ws.onerror = (err) => {
            console.error('WebSocket错误:', err);
            cleanupResources(ws, player);
        };
        ws.onclose = (err) => {
            console.info('WebSocket关闭:', err);
            // 清空当前WebSocket引用，避免多次关闭
            if (currentWs === ws) {
                currentWs = null;
            }
        };
        ws.onmessage = (event) => {
            const data = event.data;
            if (data instanceof ArrayBuffer) {
                player.pushPCM(data);
            } else {
                const body = JSON.parse(data);
                console.log('* text msg', body);
                if (body.header.name === 'SynthesisStarted' && body.header.status === 20000000) {
                    isSynthesisStarted = true;
                    synthesisStartedResolve();
                }
                if (body.header.name === 'SynthesisCompleted' && body.header.status === 20000000) {
                    cleanupResources(ws, null); // 只关闭WebSocket，播放器会继续播放缓冲的音频
                    ws = null;
                    isSynthesisStarted = false;
                }
            }
        };
        
        // 确保音频播放器已连接
        player.connect();
        
        // 设置播放器结束回调
        if (onPlaybackEndCallback) {
            player.setOnPlaybackEndCallback(() => {
                isCurrentlyPlaying = false; // 播放结束后重置状态
                currentPlayingText = ''; // 清空当前播放文本
                if (onPlaybackEndCallback) onPlaybackEndCallback();
            });
        }
    };

    /**
     * 停止当前音频播放和合成
     * 完全停止播放并清理所有资源
     */
    const stopAllPlayback = () => {
        cleanupResources(ws, player);
        
        // 重新初始化播放器，避免重用可能出现的问题
        player = new PCMAudioPlayer(aliyunTtsConfig.sample_rate);
        if (onPlaybackEndCallback) {
            player.setOnPlaybackEndCallback(onPlaybackEndCallback);
        }
        
        // 重置状态
        ws = null;
        isSynthesisStarted = false;
        isCurrentlyPlaying = false;
        currentPlayingText = '';
    };

    /**
     * 暂停当前音频播放
     * 注意：此方法仅暂停音频播放，而不关闭WebSocket连接
     */
    const pauseAudioPlayback = async () => {
        player.pause();
        console.log('音频播放已暂停');
    };
    /**
     * 设置音频播放结束的回调函数
     * @param {Function} callback - 播放结束时调用的回调函数
     */
    const setOnPlaybackEndCallback = (callback) => {
        onPlaybackEndCallback = callback;
        player.setOnPlaybackEndCallback(callback);
    };
    /**
     * 检查是否正在播放文本
     * @param {string} text - 要检查的文本
     * @returns {boolean} 如果正在播放该文本则返回true
     */
    const isPlayingText = (text) => {
        return isCurrentlyPlaying && currentPlayingText === text;
    };

    return {
        pauseAudioPlayback,
        connectAndStartSynthesis,
        sendRunSynthesis,
        sendStopSynthesis,
        setOnPlaybackEndCallback,
        stopAllPlayback,
        isPlayingText
    };
};
