{"version": 3, "file": "fileUtils.CQXjwZdz.js", "sources": ["../../../../../utils/fileUtils.js"], "sourcesContent": null, "names": ["isImageFile", "filename", "includes", "startsWith", "ext", "split", "pop", "toLowerCase", "getFileIconPath", "fileNameMatch", "match", "iconMap", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rvt", "mp3", "psd", "zip", "html", "png", "xmind", "exe", "jpg", "jpeg", "webp", "wav", "m4a", "aac", "default", "getShortFileName", "length", "parts", "name", "join", "substring", "formatFileSize", "size", "toFixed", "getFileNameFromUrl", "url", "decodedUrl", "decodeURIComponent", "pathParts", "fileName", "error", "console", "getMimeType", "css", "js", "json", "gif", "svg", "mp4", "avi", "mov", "rar", "getFileExtension", "openWebOfficePreview", "file", "log", "fileInfo", "id", "attachment_id", "showToast", "title", "icon", "fileInfoParam", "encodeURIComponent", "JSON", "stringify", "navigateTo", "success", "fail"], "mappings": "+CACY,MAACA,EAAeC,IAC1B,IAAKA,EAAiB,OAAA,EAGlB,GAAAA,EAASC,SAAS,KAEb,OAAAD,EAASE,WAAW,UAG7B,MAAMC,EAAMH,EAASI,MAAM,KAAKC,MAAMC,cAC/B,MARiB,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,OAAQ,MAAO,WAQrDL,SAASE,EAAG,EAIxBI,EAAmBP,IAC9B,IAAKA,EAAiB,MAAA,gCAEhB,MAAAQ,EAAgBR,EAASS,MAAM,iBAK/BC,EAAU,CACdC,IAAK,uBACLC,IAAK,uBACLC,KAAM,uBACNC,IAAK,uBACLC,KAAM,uBACNC,IAAK,uBACLC,KAAM,wBACNC,IAAK,uBACLC,IAAK,uBAELC,IAAK,0BACLC,IAAK,uBACLC,IAAK,uBACLC,KAAM,wBACNC,IAAK,uBACLC,MAAO,yBACPC,IAAK,uBACLC,IAAK,uBACLC,KAAM,uBACNJ,IAAK,uBACLK,KAAM,uBACNC,IAAK,0BACLC,IAAK,0BACLC,IAAK,0BACLC,QAAS,4BAYX,OAAOvB,GAzCUF,EAAgBA,EAAc,GAAKR,GAE/BI,MAAM,KAAKC,MAAMC,gBAuCfI,EAAiB,OAAA,EAI7BwB,EAAoBlC,IAC/B,IAAKA,EAAiB,MAAA,QAGtB,GAAIA,EAASmC,QADK,GACuB,OAAAnC,EAEnC,MAAAoC,EAAQpC,EAASI,MAAM,KACvBD,EAAMiC,EAAMD,OAAS,EAAI,IAAIC,EAAM/B,QAAU,GAC7CgC,EAAOD,EAAME,KAAK,KAExB,OAAID,EAAKF,QAPS,GAOahC,EAAIgC,OAAS,EAAUnC,EAC/C,GAAGqC,EAAKE,UAAU,EARP,GAQsBpC,EAAIgC,OAAS,QAAQhC,GAAG,EAIrDqC,EAAkBC,GACxBA,GAAiB,IAATA,EACTA,EAAO,KAAa,GAAGA,MACvBA,EAAO,QAAoB,IAAIA,EAAO,MAAMC,QAAQ,QACjD,IAAID,EAAQ,SAAcC,QAAQ,QAHT,GAOrBC,EAAsBC,IACjC,IAAKA,EAAY,MAAA,OAEb,IAEI,MAAAC,EAAaC,mBAAmBF,GAMhCG,EAHmBF,EAAWzC,MAAM,KAAK,GAGZA,MAAM,KACzC,IAAI4C,EAAWD,EAAUA,EAAUZ,OAAS,GAG5C,OAAKa,GAAiB,MAMvB,OAHQC,GAEA,OADCC,QAAAD,MAAM,gBAAiBA,GACxB,MACR,GAIUE,EAAeH,IA4B1B,IAAKA,EAAiB,MAAA,2BAGf,MA9Bc,CACnB9B,IAAO,aACPK,KAAQ,YACR6B,IAAO,WACPC,GAAM,yBACNC,KAAQ,mBACR3C,IAAO,kBACPC,IAAO,qBACPC,KAAQ,0EACRC,IAAO,2BACPC,KAAQ,oEACRC,IAAO,gCACPC,KAAQ,4EACRO,IAAO,YACPG,IAAO,aACPC,KAAQ,aACR2B,IAAO,YACPC,IAAO,gBACPpC,IAAO,aACPqC,IAAO,YACPC,IAAO,kBACPC,IAAO,kBACPrC,IAAO,kBACPsC,IAAO,+BACP,KAAM,+BAKUZ,EAAS5C,MAAM,KAAKC,MAAMC,gBACV,0BAAA,EAI7B,SAASuD,EAAiBb,GAEzB,MAAAvC,EAAQuC,EAASvC,MAAM,qBACtB,OAAAA,EAAQA,EAAM,GAAK,IAC5B,CAOa,MAACqD,EAAwBC,IAC5Bb,QAAAc,IAAI,sBAAuBD,EAAK1B,MACpC,IAEF,MAAM4B,EAAW,CACf5B,KAAM0B,EAAK1B,KACX6B,GAAIH,EAAKG,IAAMH,EAAKI,eAGlB,IAACF,EAASC,GAKZ,YAJcE,EAAA,CACZC,MAAO,cACPC,KAAM,SAIV,MAAMC,EAAgBC,mBAAmBC,KAAKC,UAAUT,IAEzCU,EAAA,CACb/B,IAAK,6CAA6C2B,IAClDK,QAAS,KACP1B,QAAQc,IAAI,uBAAsB,EAEpCa,KAAO5B,IACGC,QAAAD,MAAM,wBAAyBA,GACzBmB,EAAA,CACZC,MAAO,SACPC,KAAM,QACP,GASN,OANQrB,GACCC,QAAAD,MAAM,qBAAsBA,GACtBmB,EAAA,CACZC,MAAO,OACPC,KAAM,QAET"}