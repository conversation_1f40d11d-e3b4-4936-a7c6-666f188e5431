// 引用网络请求中间件
import request from '@/utils/request';

//  获取用户信息
export function getUserInfo(data, fileHeaders = {}) {
    return request({
        url: '/api/user/detail',
        method: 'GET',
        data,
        header: fileHeaders // 透传自定义请求头
    });
}

// 保存用户信息
export function saveUserInfo(data) {
    return request({
        url: '/api/user/save',
        method: 'POST',
        data
    });
}

// 查询任务执行结果
export function fetchTaskResults(data, customHeaders = {}) {
    return request({
        url: '/api/task/results',
        method: 'GET',
        data,
        header: customHeaders // 透传自定义请求头
    });
}

// 配置信息-商机阶段获取
export function fetchUserConfig(data) {
    return request({
        url: '/api/user/config',
        method: 'GET',
        data,
    });
}
