/**
 * 阿里云WebOffice文档预览服务
 * 用于集成阿里云智能媒体管理(IMM)的WebOffice功能
 * 
 * 注意：本服务使用本地SDK文件，确保以下文件存在：
 * - /static/js/aliyun-weboffice/aliyun-web-office-sdk.min.js
 */

// WebOffice服务配置
const webOfficeConfig = {
  // 阿里云IMM服务配置
  endpoint: 'https://imm.cn-shenzhen.aliyuncs.com', // 深圳区域
  projectName: 'sale-agent-weboffice', // IMM项目名称
  
  // JS-SDK版本配置
  sdkVersion: '1.1.19', // 本地SDK版本
  sdkPath: '/static/js/aliyun-weboffice/aliyun-web-office-sdk.min.js', // 本地SDK路径
  
  // 预览配置
  // previewConfig: {
  //   // 预览模式：view（只读）、edit（编辑）
  //   mode: 'view',
  //   // 是否显示工具栏
  //   showToolbar: true,
  //   // 是否允许下载
  //   allowDownload: true,
  //   // 是否允许打印
  //   allowPrint: true,
  //   // 是否允许复制
  //   allowCopy: true,
  //   // 预览超时时间（分钟）
  //   timeout: 30
  // },
  
  // 支持的文件类型（除图片外的所有格式都走WebOffice预览）
  supportedTypes: {
    // Office文档
    'doc': 'document',
    'docx': 'document', 
    'xls': 'spreadsheet',
    'xlsx': 'spreadsheet',
    'ppt': 'presentation',
    'pptx': 'presentation',
    
    // PDF文档
    'pdf': 'pdf',
    'ofd': 'pdf',
    
    // 文本文档
    'txt': 'text',
    'rtf': 'text',
    'csv': 'text',
    'json': 'text',
    'xml': 'text',
    
    // 其他常见格式
    'zip': 'archive',
    'rar': 'archive',
    'md': 'text',
    'log': 'text'
  },
  
  // 图片格式（不走WebOffice预览）
  imageTypes: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'],
  
  // // 错误重试配置
  // retryConfig: {
  //   maxRetries: 3,
  //   retryDelay: 1000, // 毫秒
  //   timeoutRetries: 2
  // }
};

/**
 * 检查文件是否为图片格式
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否为图片格式
 */
export function isImageFile(fileName) {
  if (!fileName) return false;
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  return extension && webOfficeConfig.imageTypes.includes(extension);
}

/**
 * 检查文件是否支持WebOffice预览（除图片外的所有格式）
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否支持
 */
export function isSupportedByWebOffice(fileName) {
  if (!fileName) return false;
  
  // 图片格式不走WebOffice预览
  if (isImageFile(fileName)) return false;
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  // 除了图片，其他格式都尝试用WebOffice预览
  return extension && webOfficeConfig.supportedTypes.hasOwnProperty(extension);
}

/**
 * 获取文件类型对应的WebOffice类型
 * @param {string} fileName - 文件名
 * @returns {string} WebOffice文档类型
 */
export function getWebOfficeType(fileName) {
  if (!fileName) return 'document';
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  return webOfficeConfig.supportedTypes[extension] || 'document';
}

/**
 * 加载本地WebOffice SDK
 * @returns {Promise} 加载结果
 */
export function loadWebOfficeSDK() {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.aliyun && window.aliyun.config) {
      console.log('✅ WebOffice SDK已存在');
      resolve(window.aliyun);
      return;
    }
    
    // 检查是否在H5环境
    // #ifdef H5
    // 创建script标签加载本地SDK
    const script = document.createElement('script');
    script.src = webOfficeConfig.sdkPath;
    script.async = true;
    
    script.onload = () => {
      console.log('📦 SDK文件加载完成，检查aliyun对象...');
      
      if (window.aliyun && typeof window.aliyun.config === 'function') {
        console.log('✅ 本地WebOffice SDK加载成功');
        resolve(window.aliyun);
      } else {
        console.error('❌ aliyun.config方法不存在');
        reject(new Error('本地WebOffice SDK加载失败：aliyun.config方法不存在'));
      }
    };
    
    script.onerror = () => {
      reject(new Error('本地WebOffice SDK加载失败：文件不存在或加载错误'));
    };
    
    document.head.appendChild(script);
    // #endif
    
    // #ifndef H5
    // 非H5环境不支持WebOffice
    reject(new Error('当前环境不支持WebOffice SDK'));
    // #endif
  });
}

/**
 * 获取WebOffice预览凭证
 * @param {Object} fileInfo - 文件信息
 * @returns {Promise<Object>} 预览凭证信息
 */
export async function generateWebOfficeToken(fileInfo) {
  try {
    // 调用后端API获取阿里云WebOffice预览凭证
    const { getPreviewToken } = await import('@/http/attachment.js');
    
    console.log('📋 请求WebOffice预览凭证，文件信息:', {
      id: fileInfo.id || fileInfo.attachment_id,
      name: fileInfo.name
    });
    
    // 调用后端API，传入文件ID
    const response = await getPreviewToken({
      id: fileInfo.id || fileInfo.attachment_id
    });
    
    console.log('📋 WebOffice凭证响应:', response);
    
    if (response.code === 0 && response.data) {
      // 转换后端返回的数据格式为WebOffice SDK需要的格式
      const tokenInfo = {
        AccessToken: response.data.token,
        WebofficeURL: response.data.url
      };
      
      console.log('✅ WebOffice凭证获取成功:', tokenInfo);
      return tokenInfo;
    } else {
      throw new Error(response.msg || '获取预览凭证失败');
    }
  } catch (error) {
    console.error('❌ 获取WebOffice凭证失败:', error);
    
    // 如果是网络错误或API错误，提供更友好的错误信息
    if (error.message && error.message.includes('request:fail')) {
      throw new Error('网络连接失败，请检查网络后重试');
    } else if (error.message && error.message.includes('timeout')) {
      throw new Error('请求超时，请稍后重试');
    } else {
      throw new Error(error.message || '获取预览凭证失败，请稍后重试');
    }
  }
}

/**
 * 安全获取DOM容器
 * 处理Vue ref对象、DOM元素等各种情况
 * @param {*} container - 容器参数
 * @returns {HTMLElement|null} 真实的DOM元素
 */
function getSafeContainer(container) {
  if (!container) return null;
  
  // 如果已经是DOM元素
  if (container.nodeType === 1 && typeof container.appendChild === 'function') {
    return container;
  }
  
  // Vue 3 ref对象
  if (container.value && container.value.nodeType === 1) {
    return container.value;
  }
  
  // Vue 2 ref对象
  if (container.$el && container.$el.nodeType === 1) {
    return container.$el;
  }
  
  // 尝试通过ID或类名查找
  if (typeof container === 'string') {
    return document.querySelector(container);
  }
  
  console.warn('⚠️ 无法识别的容器类型:', typeof container, container);
  return null;
}

/**
 * 安全执行querySelector
 * @param {*} container - 容器
 * @param {string} selector - 选择器
 * @returns {Element|null} 查找到的元素
 */
function safeQuerySelector(container, selector) {
  const domContainer = getSafeContainer(container);
  
  if (!domContainer || typeof domContainer.querySelector !== 'function') {
    console.warn('⚠️ 容器不支持querySelector:', typeof domContainer);
    return null;
  }
  
  try {
    return domContainer.querySelector(selector);
  } catch (error) {
    console.error('❌ querySelector执行失败:', error);
    return null;
  }
}

/**
 * 浏览器和设备检测工具
 */
const DeviceDetector = {
  // 检测Safari浏览器
  isSafari() {
    // #ifdef H5
    const userAgent = navigator.userAgent;
    return /Safari/.test(userAgent) && !/Chrome/.test(userAgent) && !/Edge/.test(userAgent);
    // #endif
    // #ifndef H5
    return false;
    // #endif
  },
  
  // 检测移动设备
  isMobile() {
    // #ifdef H5
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    // #endif
    // #ifndef H5
    return true; // uni-app环境默认为移动设备
    // #endif
  },
  
  // 检测是否需要特殊处理
  needsSpecialHandling() {
    return this.isSafari() || this.isMobile();
  }
};

/**
 * iframe样式管理器
 */
const IframeStyleManager = {
  // 基础样式
  getBaseStyles() {
    return `
      width: 100% !important;
      height: 100% !important;
      display: block !important;
      border: none !important;
      min-width: 300px !important;
      min-height: 400px !important;
    `;
  },
  
  // Safari和移动端特殊样式
  getSpecialStyles() {
    return `
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      -webkit-overflow-scrolling: touch !important;
      transform: translateZ(0) !important;
      will-change: transform !important;
      z-index: 1 !important;
    `;
  },
  
  // 应用样式到iframe
  applyStyles(iframe) {
    if (!iframe) return;
    
    let styles = this.getBaseStyles();
    
    if (DeviceDetector.needsSpecialHandling()) {
      styles += this.getSpecialStyles();
    }
    
    iframe.style.cssText = styles;
  },
  
  // 应用容器样式
  applyContainerStyles(container) {
    if (!container) return;
    
    if (DeviceDetector.needsSpecialHandling()) {
      container.style.cssText += `
        position: relative !important;
        overflow: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        transform: translateZ(0) !important;
      `;
    }
  }
};

/**
 * 创建WebOffice预览实例
 * @param {Object} options - 配置选项
 * @param {HTMLElement} options.container - 容器元素
 * @param {Object} options.fileInfo - 文件信息
 * @param {Object} options.tokenInfo - 凭证信息
 * @returns {Promise<Object>} WebOffice实例
 */
export async function createWebOfficeInstance(options) {
  const { container, fileInfo, tokenInfo } = options;
  
  try {
    // 确保SDK已加载
    const aliyun = await loadWebOfficeSDK();
    console.log('📦 创建WebOffice预览实例...');
    
    // 安全获取DOM容器
    const domContainer = getSafeContainer(container);
    
    // 验证DOM容器
    if (!domContainer || typeof domContainer.appendChild !== 'function') {
      throw new Error('容器必须是有效的DOM元素，当前类型: ' + typeof domContainer);
    }
    
    console.log('🔧 DOM容器验证通过:', {
      tagName: domContainer.tagName,
      id: domContainer.id,
      className: domContainer.className,
      needsSpecialHandling: DeviceDetector.needsSpecialHandling()
    });
    
    // 清空容器并应用样式
    domContainer.innerHTML = '';
    IframeStyleManager.applyContainerStyles(domContainer);
    
    console.log('🔧 开始创建WebOffice实例');
    
    // 创建WebOffice配置对象
    const config = {
      mount: domContainer,
      url: tokenInfo.WebofficeURL
    };
    
    // 根据官方文档，使用refreshToken方式设置令牌
    if (tokenInfo.AccessToken) {
      console.log('🔑 添加refreshToken到配置');
      config.refreshToken = () => {
        console.log('🔄 refreshToken被调用');
        return Promise.resolve({
          token: tokenInfo.AccessToken,
          timeout: 25 * 60 * 1000 // 25分钟超时
        });
      };
    }
    
    // 创建WebOffice实例
    console.log('🚀 调用aliyun.config创建实例');
    const instance = aliyun.config(config);
    
    // 检查实例是否创建成功
    if (!instance) {
      throw new Error('WebOffice实例创建失败：aliyun.config返回空值，请检查SDK版本和配置');
    }
    
    // 设置访问令牌（根据官方文档的方式）
    if (tokenInfo.AccessToken && typeof instance.setToken === 'function') {
      console.log('🔑 设置访问令牌');
      try {
        instance.setToken({ 
          token: tokenInfo.AccessToken,
          timeout: 25 * 60 * 1000 // 25分钟超时
        });
        console.log('✅ 访问令牌设置成功');
      } catch (tokenError) {
        console.warn('⚠️ 设置访问令牌失败:', tokenError);
        // 不抛出错误，继续执行
      }
    }
    
    // 等待实例准备就绪（如果有ready方法）
    if (typeof instance.ready === 'function') {
      console.log('⏳ 等待WebOffice实例准备就绪...');
      await instance.ready();
      console.log('✅ WebOffice实例准备就绪');
    }
    
    // 延迟修复iframe样式
    setTimeout(() => {
      const iframe = safeQuerySelector(domContainer, 'iframe');
      if (iframe) {
        console.log('🔧 应用iframe样式');
        IframeStyleManager.applyStyles(iframe);
        
        // 触发resize事件让WebOffice重新计算尺寸
        window.dispatchEvent(new Event('resize'));
        
        console.log('📐 iframe样式应用完成');
      } else {
        console.warn('⚠️ 未找到iframe元素');
      }
    }, 500);
    
    console.log('✅ WebOffice实例创建成功');
    return instance;
    
  } catch (error) {
    console.error('❌ 创建WebOffice实例失败:', error);
    throw error;
  }
}

/**
 * WebOffice预览组件类
 */
export class WebOfficePreview {
  constructor(container) {
    // 安全获取DOM容器
    const domContainer = getSafeContainer(container);
    
    // 验证DOM容器
    if (!domContainer || typeof domContainer.appendChild !== 'function') {
      throw new Error('WebOfficePreview容器必须是有效的DOM元素，当前类型: ' + typeof domContainer);
    }
    
    this.container = domContainer;
    this.instance = null;
    this.isReady = false;
  }
  
  /**
   * 初始化预览
   * @param {Object} fileInfo - 文件信息 { id, name }
   */
  async init(fileInfo) {
    try {
      // 验证必要参数
      if (!fileInfo.id) {
        throw new Error('文件ID不能为空');
      }
      
      if (!fileInfo.name) {
        throw new Error('文件名不能为空');
      }
      
      // 显示加载状态
      this.showLoading('正在准备文档预览...');
      
      // 检查文件是否支持
      if (!isSupportedByWebOffice(fileInfo.name)) {
        throw new Error(`不支持的文件格式: ${fileInfo.name}`);
      }
      
      // 获取预览凭证
      this.showLoading('正在获取预览凭证...');
      const tokenInfo = await generateWebOfficeToken(fileInfo);
      
      // 创建预览实例
      this.showLoading('正在加载文档...');
      this.instance = await createWebOfficeInstance({
        container: this.container,
        fileInfo: fileInfo,
        tokenInfo: tokenInfo
      });
      
      // 设置事件监听
      this.setupEventListeners();
      
      this.isReady = true;
      this.hideLoading();
      
      // 启动iframe尺寸修复
      this.fixIframeSize();
      
      console.log('✅ WebOffice预览初始化完成');
      
    } catch (error) {
      console.error('❌ WebOffice预览初始化失败:', error);
      this.showError(error.message);
      throw error;
    }
  }
  
  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (!this.instance) return;
    
    // 监听文档加载完成事件
    window.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);
        
        switch (data.action) {
          case 'preview.meta':
            console.log('📄 文档元信息:', data.data);
            break;
            
          case 'page.readPage':
            console.log('📖 当前页码:', data.data.pageIndex);
            break;
            
          case 'message.error':
            console.error('❌ WebOffice错误:', data.data);
            this.handleError(data.data);
            break;
        }
      } catch (e) {
        // 忽略非JSON消息
      }
    });
  }
  
  /**
   * 处理错误
   * @param {Object} errorData - 错误数据
   */
  handleError(errorData) {
    const errorMessages = {
      'aliyunOpenFileFail': '文档打开失败',
      'aliyunUnsupportFile': '不支持的文件类型',
      'aliyunRequstFail': '请求失败',
      'aliyunQueryParamInvalid': '参数错误',
      'aliyunRequestTimeout': '请求超时',
      'aliyunPasswordInvalid': '密码错误'
    };
    
    const message = errorMessages[errorData.result] || '预览失败';
    this.showError(message);
  }
  
  /**
   * 修复iframe尺寸
   */
  fixIframeSize(retryCount = 0) {
    const maxRetries = 3;
    
    setTimeout(() => {
      const iframe = safeQuerySelector(this.container, 'iframe');
      
      if (iframe) {
        console.log(`🔍 iframe尺寸检查 (第${retryCount + 1}次)`);
        
        // 应用样式
        IframeStyleManager.applyStyles(iframe);
        
        // 如果尺寸异常且未达到最大重试次数，继续重试
        if (retryCount < maxRetries && iframe.offsetHeight < 100) {
          console.log(`⚠️ iframe尺寸异常，准备第${retryCount + 2}次重试`);
          this.fixIframeSize(retryCount + 1);
        } else {
          console.log('✅ iframe尺寸修复完成');
        }
      } else if (retryCount < maxRetries) {
        console.log(`⚠️ 未找到iframe，准备第${retryCount + 2}次重试`);
        this.fixIframeSize(retryCount + 1);
      }
    }, 500 + retryCount * 300);
  }
  
  /**
   * 显示加载状态
   * @param {string} message - 加载消息
   */
  showLoading(message = '加载中...') {
    this.container.innerHTML = `
      <div class="weboffice-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">${message}</div>
      </div>
    `;
  }
  
  /**
   * 隐藏加载状态
   */
  hideLoading() {
    const loading = safeQuerySelector(this.container, '.weboffice-loading');
    if (loading) {
      loading.remove();
    }
  }
  
  /**
   * 显示错误信息
   * @param {string} message - 错误消息
   */
  showError(message) {
    this.container.innerHTML = `
      <div class="weboffice-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">${message}</div>
        <div class="error-tip">请检查文件格式或网络连接</div>
      </div>
    `;
  }
  
  /**
   * 销毁实例
   */
  destroy() {
    console.error('WebOffice实例销毁失败',this);
    if (this.instance === null) {
      return
    } else {
      this.instance.destroy()
    }
    // this.instance.destroy()
    this.isReady = false;
    
    // 清空容器
    if (this.container) {
      this.container.innerHTML = '';
    }
  }
}

/**
 * 创建WebOffice预览器的便捷方法
 * @param {HTMLElement} container - 容器元素
 * @param {Object} fileInfo - 文件信息
 * @returns {Promise<WebOfficePreview>} 预览器实例
 */
export async function createWebOfficePreview(container, fileInfo) {
  const preview = new WebOfficePreview(container);
  await preview.init(fileInfo);
  return preview;
}

// 导出配置和工具函数供外部使用
export { webOfficeConfig, DeviceDetector, IframeStyleManager };

/**
 * 使用示例：
 * 
 * // 1. 确保本地SDK文件存在
 * // /static/js/aliyun-weboffice/aliyun-web-office-sdk.min.js
 * 
 * // 2. 检查文件是否支持（除图片外的所有格式）
 * if (isSupportedByWebOffice(file.name)) {
 *   // 3. 创建预览器
 *   const preview = await createWebOfficePreview(containerElement, fileInfo);
 * }
 * 
 * // 4. 销毁预览器
 * preview.destroy();
 */ 