/**
 * 文件上传 hooks
 * 使用 xe-upload 组件选择文件，支持 H5 和 APP 端的文件上传到阿里云 OSS
 */
import { ref, nextTick } from 'vue';
import { generateFileUploadSignature } from '@/http/attachment.js';
import { getFileExtension, getMimeType } from '@/utils/fileUtils.js';
import { validateFiles, getExtensionsForUpload } from '@/utils/fileTypeValidator.js';

export function useFileUpload() {
  // 上传进度
  const uploadProgress = ref(0);
  // 是否正在上传
  const uploading = ref(false);
  // xe-upload 组件引用
  const xeUploadRef = ref(null);
  
  /**
   * 生成随机文件名
   * @param {string} originalName - 原始文件名
   * @returns {string} 新的文件名
   */
  const generateFileName = (originalName) => {
    const timestamp = Date.now();
    const random1 = Math.random().toString(36).substring(2, 7);
    const random2 = Math.random().toString(36).substring(2, 7);
    const extension = getFileExtension(originalName);
    return `${timestamp}-${random1}${random2}${extension}`;
  };
  
  /**
   * H5端上传文件到 OSS（使用 PUT 请求）
   * @param {Object} file - 文件对象
   * @param {Object} signData - 签名数据
   * @returns {Promise<Object>} 上传结果
   */
  const uploadFileH5 = async (file, signData) => {
    console.log('H5端开始上传文件:', file.name);
    
    // 创建 File 对象或 Blob
    let fileBlob = file;
    if (file.tempFile) {
      // 从 tempFile 创建 Blob
      fileBlob = file.tempFile;
    }
    
    // 使用 PUT 请求上传到签名 URL
    const uploadResponse = await fetch(signData.sign_url, {
      method: 'PUT',
      body: fileBlob,
      headers: {
        'Content-Type': file.type || 'application/octet-stream'
      }
    });
    
    if (!uploadResponse.ok) {
      throw new Error(`上传失败: ${uploadResponse.status}`);
    }
    
    console.log('H5端上传成功');
    return {
      url: signData.object,
      fileName: file.name,
      cdn: signData.cdn || signData.url,
      fullUrl: `${signData.cdn || signData.url}/${signData.object}`
    };
  };
  
  /**
   * APP端上传文件到 OSS（使用 POST 请求 + FormData）
   * @param {Object} file - 文件对象
   * @param {Object} signData - 签名数据
   * @returns {Promise<Object>} 上传结果
   */
  const uploadFileApp = async (file, signData) => {
    console.log('APP端开始上传文件:', file.name);
    
    return new Promise((resolve, reject) => {
      // 使用 uni.uploadFile 进行上传
      const uploadTask = uni.uploadFile({
        url: signData.url || signData.cdn,
        filePath: file.tempFilePath,
        name: 'file',
        formData: {
          ...signData.params,
          key: signData.object
        },
        success: (res) => {
          console.log('APP端上传响应:', res);
          if (res.statusCode === 200 || res.statusCode === 204) {
            uploadProgress.value = 100;
            resolve({
              url: signData.object,
              fileName: file.name,
              cdn: signData.cdn || signData.url,
              fullUrl: `${signData.cdn || signData.url}/${signData.object}`
            });
          } else {
            reject(new Error(`上传失败: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          console.error('APP端上传失败:', error);
          reject(new Error(error.errMsg || '上传失败'));
        }
      });
      
      // 监听上传进度
      uploadTask.onProgressUpdate((res) => {
        uploadProgress.value = res.progress;
        console.log('上传进度:', res.progress + '%');
      });
    });
  };
  
  /**
   * 处理文件上传
   * @param {Array} files - 文件列表
   * @returns {Promise<Array>} 上传结果列表
   */
  const handleUploadFiles = async (files, allowedTypes = null) => {
    const results = [];

    // 🛡️ 在调用签名接口之前，先验证文件格式（如果指定了允许的格式）
    if (allowedTypes) {
      console.log('开始验证文件格式...', allowedTypes);
      const validation = validateFiles(files, allowedTypes);

      if (!validation.valid) {
        // 显示详细的错误信息
        const errorMessages = validation.errors.map(err =>
          `${err.fileName}: ${err.error}`
        ).join('\n');

        const errorTitle = validation.errors.length === 1 ?
          '文件格式不支持' :
          `${validation.errors.length} 个文件格式不支持`;

        // uni.showModal({
        //   title: errorTitle,
        //   content: errorMessages,
        //   showCancel: false,
        //   confirmText: '知道了'
        // });

        throw new Error(`文件格式验证失败: ${validation.errors[0]?.error || '未知错误'}`);
      }

      console.log('文件格式验证通过，开始上传...');
    } else {
      console.log('未指定文件格式限制，直接开始上传...');
    }

    for (const file of files) {
      try {
        // 生成新文件名
        const fileName = generateFileName(file.name);
        const contentType = file.type || getMimeType(file.name) || 'application/octet-stream';
        
        console.log('准备上传文件:', {
          originalName: file.name,
          newName: fileName,
          contentType: contentType,
          size: file.size
        });
        
        // 获取上传签名（服务端会验证文件格式）
        const signRes = await generateFileUploadSignature({
          file_name: fileName,
          content_type: contentType,
          original_name: file.name, // 传递原始文件名用于服务端格式验证
          file_size: file.size || 0  // 传递文件大小用于服务端大小验证
        });

        if (signRes.code !== 0) {
          // 服务端验证失败，抛出具体错误信息
          const errorMessage = signRes.msg || '获取上传签名失败';
          throw new Error(errorMessage);
        }
        
        console.log('获取签名成功:', signRes.data);
        
        // 根据平台选择上传方式
        let uploadResult;
        // #ifdef H5
        uploadResult = await uploadFileH5(file, signRes.data);
        // #endif
        
        // #ifdef APP-PLUS
        uploadResult = await uploadFileApp(file, signRes.data);
        // #endif
        
        // #ifdef MP-WEIXIN
        // 小程序端也使用类似 APP 的上传逻辑
        uploadResult = await uploadFileApp(file, signRes.data);
        // #endif
        
        results.push({
          success: true,
          data: uploadResult,
          file: {
            name: file.name,
            size: file.size,
            type: file.type
          }
        });
        
      } catch (error) {
        console.error(`文件 ${file.name} 上传失败:`, error);
        results.push({
          success: false,
          error: error.message,
          file: {
            name: file.name,
            size: file.size,
            type: file.type
          }
        });
      }
    }
    
    return results;
  };
  
  /**
   * 选择并上传文件
   * @param {string} type - 文件类型 'image' | 'video' | 'file'
   * @param {Object} options - 配置选项
   * @param {Array} options.allowedTypes - 允许的文件格式数组，如 ['.jpg', '.png', '.pdf']
   * @returns {Promise<Array>} 上传结果
   */
  const chooseAndUpload = async (type = 'file', options = {}) => {
    if (!xeUploadRef.value) {
      throw new Error('请先设置 xe-upload 组件引用');
    }
    // 提取 allowedTypes 参数
    const { allowedTypes = null, ...uploadOptions } = options;

    return new Promise((resolve, reject) => {
      uploading.value = true;
      uploadProgress.value = 0;
      
      // 设置一次性的回调处理器
      const handleCallback = async (e) => {
        console.log('xe-upload 回调:', e);
        
        if (e.type === 'warning') {
          uploading.value = false;
          uni.showToast({
            title: '选择文件失败',
            icon: 'none'
          });
          reject(new Error(e.data?.errMsg || '选择文件失败'));
          return;
        }
        
        if (e.type === 'choose' && e.data && e.data.length > 0) {
          try {
            uni.showLoading({
              title: '上传中...'
            });

            // 处理文件上传（在调用签名接口前验证文件格式）
            const results = await handleUploadFiles(e.data, allowedTypes);
            
            uni.hideLoading();
            uploading.value = false;
            
            // 检查是否有失败的上传
            const failedUploads = results.filter(r => !r.success);
            if (failedUploads.length > 0) {
              uni.showToast({
                title: `${failedUploads.length} 个文件上传失败`,
                icon: 'none'
              });
            }
            
            // 返回成功的上传结果
            const successResults = results.filter(r => r.success).map(r => r.data);
            resolve(successResults);
            
          } catch (error) {
            uni.hideLoading();
            uploading.value = false;
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            });
            reject(error);
          }
        }
      };
      
      // 临时存储回调函数
      xeUploadRef.value._tempCallback = handleCallback;
      
      // 触发文件选择
      nextTick(() => {
        xeUploadRef.value.upload(type, {
          count: uploadOptions.count || 1,
          extension: uploadOptions.extension || '*', // 客户端不限制，由前端验证
          sizeType: uploadOptions.sizeType,
          sourceType: uploadOptions.sourceType,
          ...uploadOptions
        });
      });
    });
  };
  
  /**
   * 设置 xe-upload 组件引用
   * @param {Object} ref - 组件引用
   */
  const setXeUploadRef = (ref) => {
    xeUploadRef.value = ref;
  };
  
  /**
   * 处理 xe-upload 组件回调
   * @param {Object} e - 回调事件对象
   */
  const handleXeUploadCallback = (e) => {
    // 调用临时存储的回调函数
    if (xeUploadRef.value && xeUploadRef.value._tempCallback) {
      xeUploadRef.value._tempCallback(e);
      // 清除临时回调
      delete xeUploadRef.value._tempCallback;
    }
  };
  
  return {
    chooseAndUpload,
    uploading,
    uploadProgress,
    setXeUploadRef,
    handleXeUploadCallback
  };
}
