/* 图标过渡动画 */
.icon-down {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
/* 列表过渡动画 */
.slide-fade-enter-active {
  transition: opacity 0.2s ease-out, transform 0.25s ease-out;
}
.slide-fade-leave-active {
  transition: opacity 0.15s ease-in, transform 0.2s ease-in;
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
.public-icon-img {
  width: 25px;
  height: 25px;
}
.s-p-b {
  justify-content: space-between;
}
.section-box {
  display: flex;
  align-items: center;
  margin: 15px 5px 15px 5px;
  padding: 0px 10px;
  .section-left {
    display: flex;
    align-items: center;
    .icon-down {
      width: 25px;
      height: 25px;
    }
  }
  .section-title {
    color: var(---, #787d86);
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
/* 分割线 */
.item-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin-left: 44px; /* 14px 左外边距 + 24px 缩略图 + 6px 间距 */
  margin-right: 16px;
}
