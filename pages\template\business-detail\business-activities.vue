<template>
  <!-- 商机活动模块 -->
  <view class="business-details-container">
    <!-- 当前 -->
    <view class="section-box s-p-b">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">当前</view>
      </view>
      <uni-icons type="plusempty" size="20" color="#4D5BDE" @click="handeAddActivityClick"></uni-icons>
    </view>
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
        <!-- 添加 v-if 判断 -->
        <template v-if="getCompletedTasks && getCompletedTasks.length > 0">
          <checkbox-group
            @change="handleCheckboxChange($event, item, index)"
            v-for="(item, index) in getCompletedTasks"
            :key="index"
          >
            <label class="todo-item">
              <checkbox
                :value="item.id.toString()"
                :checked="item.is_finished === 1"
                color="#4CAF50"
              />
              <view class="content-wrapper" @click.stop="handeToEdit(item, index)">
                <view class="todo-title">{{ item.title }}</view>
                <view class="title-row">
                  <view v-if="item.due_date" class="business-time">{{ item.due_date }}</view>
                  <view v-if="item.business" class="business-text">{{ item.business.content }}</view>
                </view>
              </view>
            </label>
          </checkbox-group>
        </template>
        <!-- 添加 v-else 用于显示缺省状态 -->
        <template v-else>
          <view class="empty-placeholder" @click="handeAddActivityClick">
            <text class="empty-text">暂无商机活动点击添加</text>
          </view>
        </template>
      </view>
    </transition>
    <!-- 「需求确认」阶段 AI建议-->
    <!-- <view class="section-box">
      <view class="section-left" @click="toggleExpand('finished')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.finished
              ? 'rotate(0deg)'
              : 'rotate(-90deg)',
          }"
        />
        <view class="section-title">「需求确认」阶段 AI建议</view>
      </view>
    </view>
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.finished">
        <checkbox-group @change="handleCheckboxSuggestionsChange">
          <view
            class="todo-item"
            v-for="(item, index) in cfmAISuggestionsList"
            :key="index"
          >
            <checkbox
               :value="item.id.toString()"
              :checked="item.is_finished === 1"
              color="#4CAF50"
            />
            <view class="content-wrapper">
              <view class="todo-title">{{ item.title }}</view>
              <view class="title-row">
                <view v-if="item.due_date">{{ item.due_date }}</view>
                <view v-if="item.business">{{ item.business?.content }}</view>
              </view>
            </view>
          </view>
        </checkbox-group>
      </view>
    </transition> -->
    <!-- 已完成 -->
    <view class="section-box">
      <view class="section-left" @click="toggleExpand('isCompleted')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.isCompleted
              ? 'rotate(0deg)'
              : 'rotate(-90deg)',
          }"
        />
        <view class="section-title">已完成</view>
      </view>
    </view>
    <transition name="slide-fade">
      <view class="todo-card is-finished" v-show="expandStates.isCompleted">
        <checkbox-group
          @change="handleCheckboxChange($event, item, index)"
          v-for="(item, index) in getIncompleteTasks"
          :key="index"
        >
          <view class="todo-item">
            <checkbox
               :value="item.id.toString()"
              :checked="item.is_finished === 1"
              color="#4CAF50"
            />
            <view class="content-wrapper" @click.stop="handeToEdit(item, index)">
              <view class="todo-title">{{ item.title }}</view>
              <view class="title-row">
                <view v-if="item.due_date" class="business-time">{{ item.due_date }}</view>
                <view>{{ item.business?.title || item.business?.content || item.customer?.company_short_name ||'-' }}</view>
              </view>
            </view>
          </view>
        </checkbox-group>
      </view>
    </transition>
  </view>
  <!-- 新建代办表单弹窗 -->
  <TodoFormActionSheet
    v-if="showActiveForm"
    v-model:show="showActiveForm"
    @submit="handleTodoSubmit"
    :initialData="businessObj"
  />
</template>

<script setup>
import { reactive, ref, onMounted, computed, watch } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { addTodoItem, updateTodoItem } from "@/http/todo.js";
import { updateOpportunity } from "@/http/business.js";
import TodoFormActionSheet from "../../tabBar/todo/TodoFormActionSheet.vue";

const emit = defineEmits(["refresh", "cancel"]);
const props = defineProps({
  // 初始表单数据
  dataList: {
    type: Array,
    default: [],
  },
  // 客户详情数据
  userDetails: {
    type: Object,
    default: {},
  },
  // AI需求建议
  cfmAISuggestionsList: {
    type: Array,
    default: [],
  },
});
// 修改状态管理
const expandStates = ref({
  current: true,
  finished: true,
  isCompleted: true,
});
// 是否展示新建客户下拉
const showActiveForm = ref(false)
// 获取已完成任务列表
const getCompletedTasks = ref([]);
// 获取未完成任务列表
const getIncompleteTasks = ref([]);
// 新建的时候默认回显的数据
const businessObj = ref({
  customer_id:undefined,
  bo_id:undefined,
});

// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
  expandStates.value[type] = !expandStates.value[type];
};

// 弹出客户新建文件
const handeAddActivityClick = () => {
  showActiveForm.value = true
};

// 【选中任务】
const handleCheckboxChange = async (e, item, index) => {
  // 未完成 0 已完成1
  // 根据当前状态决定是选中还是取消选中
  const newStatus = item.is_finished === 1 ? false : true;
  const res = await updateTodoItem({
    id: item.id,
    is_finished: newStatus,
  })
  if (res.code === 0) {
    // 更新本地数据状态
    item.is_finished = newStatus ? 1 : 0;
    uni.showToast({
      title: newStatus ? "已完成" : "已取消",
      icon: "success",
    });
  }
  emit("refresh");
};

// 【选中任务】「需求确认」阶段 AI建议
const handleCheckboxSuggestionsChange = async (e) => {
  try {
    // e.detail.value 是一个包含所有选中 checkbox 的 value (即 item.id.toString()) 的数组
    const selectedIds = e.detail.value || []; // 获取选中的ID数组
    const res = await updateOpportunity({
      id: businessObj.value.bo_id, // 商机ID
      suggest_todo_finish_ids: selectedIds, // 选中的AI建议ID数组
    });
    if (res.code === 0) {
      uni.showToast({
        title: "更新成功",
        icon: "success",
      });
      // 触发父组件刷新列表或其他操作
      emit("refresh");
    } else {
      // 接口失败处理
      uni.showToast({ title: res.msg || "更新失败", icon: "none" });
      // 注意：接口失败时，界面上的勾选状态可能与实际不符，
    }
  } catch (error) {
    console.error("更新AI建议状态失败:", error);
    uni.showToast({ title: "操作失败，请稍后重试", icon: "none" });
  }
};

// 任务分类
const splitTasksList = async () => {
  try {
    businessObj.value.customer_id = props.userDetails?.customer_id
    // 清空之前的任务数据
    getIncompleteTasks.value = [];
    getCompletedTasks.value = [];
    // 遍历后端返回的任务数组，根据 is_finished 进行分类
    props.dataList.forEach((task) => {
      if (task.is_finished === 0) {
        // 未完成的任务
        getCompletedTasks.value.push(task);
      } else if (task.is_finished === 1) {
        // 已完成的任务
        getIncompleteTasks.value.push(task);
      }
    });
  } catch (err) {
    console.error("请求失败:", err);
    uni.showToast({ title: "加载失败", icon: "none" });
  }
};
// 点击项时的处理 进入待办详情
const handeToEdit = (item) => {
  uni.navigateTo({
    url: `/pages/template/todo-details/index?id=${item.id}`,
  });
};
// 处理表单提交
const handleTodoSubmit = async (formData) => {
  try {
    const res = await addTodoItem({
      ...formData,
    })
    if (res.code === 0) {
      uni.showToast({
        title: "创建成功",
        icon: "success",
      });
      emit("refresh");
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
}

// 添加数据监听
watch(
  () => props.dataList,
  (newVal) => {
    if (newVal && newVal.length >= 0) {
      splitTasksList();
    }
  },
  { deep: true, immediate: true }
);

// 使用 Uni-app 官方生命周期
onLoad((options) => {
  // 这里 options 包含所有平台的参数
  if (options.business_id) {
    // 设置默认选中
    businessObj.value.bo_id = Number(options.business_id)
  }
});
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.business-details-container {
  box-sizing: border-box;
  margin: 0 18px;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .section-box {
    display: flex;
    align-items: center;
    margin: 15px 0px 15px 0px;
    .section-left {
      display: flex;
      align-items: center;
      .icon-down {
        width: 25px;
        height: 25px;
      }
    }
    .section-title {
      color: var(---, #787d86);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
  .s-p-b {
    justify-content: space-between;
  }
  .todo-card {
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;
    uni-checkbox-group{
      &:last-child {
        .content-wrapper{
          border-bottom: none;
        }
      }
    }
    /* 缺省状态样式 --- */
    .empty-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px; /* 上下留白多一些 */
      text-align: center;
    }

    .empty-text {
      font-size: 14px;
      color: #999;
    }

    .todo-item {
      display: flex;
      align-items: center;
      margin: 10px 12px 5px 12px;
      .content-wrapper {
        flex: 1;
        margin-left: 6px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 2px;
        overflow: hidden;
      }
      .todo-title {
        color: var(---, #000);
        text-overflow: ellipsis;
        font-size: 14px;
        white-space: nowrap; /* 确保文本不换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        max-width: 100%; /* 确保宽度不超过父容器 */
      }
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 4px;
        color: var(---, #adb1ba);
        font-size: 12px;
        .business-text{
          width: 190px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 9px;
          margin-right: 4px;
          text-align: right;
        }
        .business-time{
          min-width: 100px;
          max-width: 150px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
  .is-finished {
    opacity: 0.6;
  }
}
</style>