<template>
    <view class="chat-header-container" :style="appSafeAreaStyle">
      <view class="chat-header">
        <view class="header-left" v-if="isHasToken" @click="headerToggleDrawer">
          <image class="left-bar" src="/static/chatImg/chat-bars.svg" mode="aspectFill"></image>
          <!-- 未读消息红点 -->
          <view v-if="hasUnreadMessages" class="unread-dot"></view>
        </view>
        <view class="avatar-container">
          <block v-if="sessionTitle">
            <image class="avatar" v-if="sessionTitle === '销售助理 Ivy'" src="/static/user/Ivy.png" mode="aspectFill"></image>
            <text class="agent-name">{{ sessionTitle }}</text>
          </block>
          <block v-else>
            <image class="avatar" src="/static/user/Ivy.png" mode="aspectFill"></image>
            <text class="agent-name">销售助理 Ivy</text>
          </block>
        </view>
        <view class="header-right" v-if="isHasToken" @click="navigateToTodoList">
          <view class="actions-right">
            <image src="/static/chatImg/chat-scale.svg" class="chat-scale-icon"></image>
           </view>
        </view>
        <view class="header-right" v-else>
          <button class="header-btn-login cccc-test" size="mini" @click="showLoginPopup">登录/注册</button>
        </view>
      </view>
    </view>
    <!-- 登录验证弹框 -->
    <login-verify-popup 
      v-model="showLoginVerify" 
      @login-success="handleLoginSuccess"
    />
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import LoginVerifyPopup from '@/components/login/LoginVerifyPopup.vue'
import { storeSpeechRecognitionToken } from '@/utils/aiChatUtils.js'
import { isMobile } from '@/utils/utils.js'
import envConfig from '@/utils/env.js' // 导入环境配置

/**
 * 聊天页面顶部导航栏组件
 * 包含用户头像、agent信息、登录状态管理、CRM后台入口等功能
 */

// Props定义
const props = defineProps({
  // 会话标题
  sessionTitle: {
    type: String,
    default: ''
  },
  // 是否有未读消息
  hasUnreadMessages: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['loginSuccess', 'loginStatusChange', 'toggleDrawer'])

// 登录相关状态
const isLogged = ref(false) // 是否已登录
const isHasToken = ref(false) // 是否有token
const showLoginVerify = ref(false) // 显示登录弹框

// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`
  };
  // #endif
  return {};
});

// 弹出左侧抽屉
const headerToggleDrawer = () => {
  // 发出自定义事件，通知父组件显示抽屉
  emit('toggleDrawer');
}

/**
 * 初始化登录状态
 * 从本地存储中获取token和用户信息
 */
const initLoginStatus = () => {
  const token = uni.getStorageSync('token')
  const userInfo = uni.getStorageSync('userInfo') || {}
  // console.log('🔐 初始化登录状态 - token:', !!token, 'userInfo:', userInfo)
  if (token !== undefined && token !== null && token !== '') {
    isHasToken.value = true
  } else {
    isHasToken.value = false
  }
  
  if (userInfo && userInfo.is_activated === 1) {
    isLogged.value = true
  } else {
    isLogged.value = false
  }
  
  // 【修复】初始化时只通知状态变化，不触发登录成功事件
  emit('loginStatusChange', {
    isLogged: isLogged.value,
    isHasToken: isHasToken.value,
    userInfo
  })
}

/**
 * 监听存储变化
 * 当token或userInfo发生变化时自动更新状态
 */
const setupStorageWatcher = () => {
  // 监听token变化
  watch(() => uni.getStorageSync('token'), (newToken) => {
    // console.log('🔐 Token变化:', !!newToken)
    if (newToken !== undefined && newToken !== null && newToken !== '') {
      isHasToken.value = true
    } else {
      isHasToken.value = false
    }
    
    // 通知父组件状态变化
    emit('loginStatusChange', {
      isLogged: isLogged.value,
      isHasToken: isHasToken.value,
      userInfo: uni.getStorageSync('userInfo') || {}
    })
  }, { immediate: true })
  
  // 监听用户信息变化
  watch(() => uni.getStorageSync('userInfo'), (newUserInfo) => {
    console.log('🔐 用户信息变化:', newUserInfo)
    
    if (newUserInfo && newUserInfo.is_activated === 1) {
      isLogged.value = true
    } else {
      isLogged.value = false
    }
    
    // 通知父组件状态变化
    emit('loginStatusChange', {
      isLogged: isLogged.value,
      isHasToken: isHasToken.value,
      userInfo: newUserInfo || {}
    })
  }, { immediate: true })
}

/**
 * 显示登录弹框
 */
const showLoginPopup = () => {
  console.log('🔐 显示登录弹框')
  showLoginVerify.value = true
}

/**
 * 处理登录成功
 * @param {Object} data - 登录返回的数据
 * @param {Boolean} isShowMsg - 是否显示成功消息
 */
const handleLoginSuccess = async (data, isShowMsg = true) => {
  // console.log('🔐 登录成功处理:', data)
  if (isShowMsg) {
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
  }
  
  if (data.userInfo.is_activated === 1) {
    isLogged.value = true
    isHasToken.value = true
    
    try {
      // 获取阿里云语音识别 token
      await storeSpeechRecognitionToken()
      // console.log('✅ 语音识别token获取成功')
    } catch (error) {
      console.warn('⚠️ 语音识别token获取失败:', error)
    }
    
    // 通知父组件登录成功
    emit('loginSuccess', {
      ...data,
      isLogged: isLogged.value,
      isHasToken: isHasToken.value,
      userInfo: data.userInfo
    })
  } else {
    // 账号未激活
    uni.showToast({
      title: '账号未激活，请激活后再使用',
      icon: 'none'
    })
    isLogged.value = false
    isHasToken.value = false
    showLoginVerify.value = false
  }
}

/**
 * 导航到待办列表
 * 根据平台和设备类型进行不同的跳转处理
 */
const navigateToTodoList = () => {
  // #ifdef APP-PLUS
  // plus.nativeUI.confirm('test', function(e){
  //   console.log("Close confirm: " + e);
  // });
  // App端：直接使用switchTab跳转
  uni.switchTab({
    url: '/pages/tabBar/todo/todo',
    fail: () => {
      uni.showToast({ title: '跳转失败', icon: 'none' })
    }
  })
  // #endif
  
  // #ifdef H5
  const deviceIsMobile = isMobile()
  if (deviceIsMobile) {
    // H5移动端：使用switchTab跳转
    uni.switchTab({
      url: '/pages/tabBar/todo/todo',
      fail: () => {
        uni.showToast({ title: '跳转失败', icon: 'none' })
      }
    })
  } else {
    // H5桌面端：打开新窗口
    console.log('全局配置--',envConfig);
    const targetUrl = `${envConfig.webUrl}/pages/tabBar/todo/todo`
    console.error('🔗 打开待办列表链接:', targetUrl);
    window.open(targetUrl, '_blank', 'noopener,noreferrer')
  }
  // #endif
}

/**
 * 检查登录状态
 * 供外部调用，检查用户是否已登录
 * @returns {Boolean} 是否已登录
 */
const checkLoginStatus = () => {
  if (!isLogged.value) {
    uni.showToast({
      title: '请先登录后再使用该功能',
      icon: 'none'
    })
    showLoginVerify.value = true
    return false
  }
  return true
}

/**
 * 强制重新检查登录状态
 * 供外部调用，重新从存储中获取最新状态
 */
const refreshLoginStatus = () => {
  console.log('🔄 强制刷新登录状态')
  initLoginStatus()
}

// 计算属性：返回当前登录状态信息
const loginStatusInfo = computed(() => ({
  isLogged: isLogged.value,
  isHasToken: isHasToken.value,
  userInfo: uni.getStorageSync('userInfo') || {}
}))

// 暴露方法给父组件
defineExpose({
  checkLoginStatus,
  refreshLoginStatus,
  showLoginPopup,
  loginStatusInfo
})

// 组件挂载时初始化
onMounted(() => {
  // console.log('📱 ChatHeader 组件已挂载')
  initLoginStatus()
  setupStorageWatcher()
})
</script>

<style scoped lang="scss">
.chat-header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;

  // 顶部导航栏
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin: 0 auto;
    max-width: 730px;
    .header-left {
      display: flex;
      cursor: pointer;
      position: relative; /* 为红点定位添加相对定位 */
      .left-bar {
        width: 26px;
        height: 26px;
      }
    
      /* 未读消息红点样式 */
      .unread-dot {
        position: absolute;
        top: 0;
        right: 0;
        width: 8px;
        height: 8px;
        background: var(---, #FA5151);
        border-radius: 50%;
        border: 1px solid #ffffff;
        z-index: 1;
      }
    }
    
    .header-right {
      display: flex;
      
      .header-btn-login {
        background-color: rgba(0, 0, 0, 1);
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: rgba(255, 255, 255, 1);
        border-radius: 10px;
      }
      
      .actions-right {
        display: flex;
        height: 30px;
        padding: 0 10px 0 8px;
        align-items: center;
        gap: 4px;
        border-radius: 10px;
        line-height: normal;
        cursor: pointer;

        .chat-scale-icon {
          width: 26px;
          height: 26px;
        }
        
      }
    }
    
    .avatar-container {
      display: flex;
      align-items: center;
      
      .avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        margin-right: 10px;
      }
      
      .agent-name {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }
    }
  }
}
</style>