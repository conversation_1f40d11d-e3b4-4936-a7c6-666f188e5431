// 引用网络请求中间件
import request from '@/utils/request';

// 生成上传签名
export function generateFileUploadSignature(data, fileHeaders = {}) {
    return request({
        url: '/api/upload/genPostSign',
        method: 'GET',
        data,
        header: fileHeaders // 透传自定义请求头
    });
}

// 生成下载签名
export function generateFileDownloadSignature(data) {
    return request({
        url: '/api/upload/genGetSign',
        method: 'GET',
        data
    });
}

// 获取附件列表
export function fetchAttachmentList(data) {
    return request({
        url: '/api/attachment/list',
        method: 'POST',
        data
    });
}

// 新增或编辑附件
export function saveOrEditAttachment(data) {
    return request({
        url: '/api/attachment/save',
        method: 'POST',
        data
    });
}

// 删除附件
export function deleteAttachment(data) {
    return request({
        url: '/api/attachment/del',
        method: 'POST',
        data
    });
}

// 获取预览凭证
export function getPreviewToken(data) {
    return request({
        url: '/api/attachment/previewToken',
        method: 'GET',
        data
    });
}




