/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.debug-page[data-v-c476276d] {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
.page-header[data-v-c476276d] {
  text-align: center;
  margin-bottom: 20px;
}
.page-header .page-title[data-v-c476276d] {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}
.page-header .page-subtitle[data-v-c476276d] {
  font-size: 14px;
  color: #666;
}
.info-card[data-v-c476276d] {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.info-card .card-title[data-v-c476276d] {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}
.info-card .card-header[data-v-c476276d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}
.info-card .card-header .card-title[data-v-c476276d] {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.info-item[data-v-c476276d] {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f9f9f9;
}
.info-item[data-v-c476276d]:last-child {
  border-bottom: none;
}
.info-item .info-label[data-v-c476276d] {
  flex: 0 0 100px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}
.info-item .info-value[data-v-c476276d] {
  flex: 1;
  font-size: 14px;
  color: #333;
  word-break: break-all;
  margin-right: 8px;
}
.info-item .info-value.status-success[data-v-c476276d] {
  color: #52c41a;
  font-weight: 500;
}
.info-item .info-value.status-error[data-v-c476276d] {
  color: #f5222d;
  font-weight: 500;
}
.info-item .info-value.status-pending[data-v-c476276d] {
  color: #faad14;
  font-weight: 500;
}
.info-item .copy-btn[data-v-c476276d] {
  flex: 0 0 auto;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  line-height: 1.2;
}
.log-container[data-v-c476276d] {
  height: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  background-color: #fafafa;
}
.log-item[data-v-c476276d] {
  display: flex;
  margin-bottom: 8px;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
}
.log-item .log-time[data-v-c476276d] {
  flex: 0 0 60px;
  font-size: 12px;
  color: #999;
  margin-right: 8px;
}
.log-item .log-content[data-v-c476276d] {
  flex: 1;
  font-size: 12px;
  word-break: break-all;
}
.log-item .log-content.info[data-v-c476276d] {
  color: #666;
}
.log-item .log-content.success[data-v-c476276d] {
  color: #52c41a;
}
.log-item .log-content.error[data-v-c476276d] {
  color: #f5222d;
}
.log-item .log-content.warn[data-v-c476276d] {
  color: #faad14;
}
.no-logs[data-v-c476276d] {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px;
}
.action-buttons[data-v-c476276d] {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}
.action-buttons .action-btn[data-v-c476276d] {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
}
.clear-btn[data-v-c476276d] {
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  line-height: 1.2;
}