## 1.5.2（2025-05-14）
onOpen 和 onError 返回更多字段
## 1.5.1（2025-04-26）
解决 js map 报错问题。
## 1.5.0（2025-04-01）
返回完整的message、小程序解析逻辑优化，改为使用fetch-event-source的解析逻辑
## 1.4.1（2025-03-28）
onOepn回调方法返回sse请求的response对象、及返回err错误信息
## 1.4.0（2025-03-24）
解析微信小程序返回的数据
## 1.3.2（2025-03-16）
1. 示例项目添加了sse server供调试。
2. 发生了错误接口会无限运行的问题解决，现在发生了错误会调用stop方法停止。
## 1.3.1（2025-03-10）
修复了get请求无法stop的bug
## 1.3.0（2025-03-06）
插件修改为 uni_modules 模式
