import { createTask, getChatHistoryList, fetchSpeechRecognitionToken } from "@/http/ai-chat-task.js";
import { saveOrEditAttachment } from "@/http/attachment.js";

// 文件ID缓存：key是文件object名, value是attachment_id
const fileAttachmentMap = new Map();
// 保存附件并获取attachment_id
export async function saveAttachment(file) {
  try {
    // 检查缓存中是否已有该文件的attachment_id
    if (file.object && fileAttachmentMap.has(file.object)) {
      console.log("附件ID已存在于缓存中:", file.object);
      return fileAttachmentMap.get(file.object);
    }
    const res = await saveOrEditAttachment({
      name: file.name,
      url: file.object,
      type: file.type,
      size: file.size,
    });
    if (res.code === 0) {
      console.log("附件保存成功:", res.data);
      // 保存附件ID到缓存
      if (file.object) {
        fileAttachmentMap.set(file.object, res.data.id);
      }
      return res.data.id; // 返回附件ID
    } else {
      console.error("附件保存失败:", res.message);
      uni.showToast({
        title: res.message || '附件保存失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error("附件保存失败:", error);
    uni.showToast({
      title: '附件保存失败，请稍后重试',
      icon: 'none'
    });
  }
  return null;
}

// 上传后立即保存附件并获取ID
export async function saveAttachmentAfterUpload(file) {
  const attachmentId = await saveAttachment(file);
  if (attachmentId) {
    return { ...file, attachment_id: attachmentId };
  }
  return file;
}

// 从后端获取数据（获取 TaskId）
export async function fetchTaskId(msg, files = [], skillInfo = null, sessionId = null) {
  try {
    // 构建查询参数
    let query;
    
    // 如果有文件，使用新的查询参数结构
    if (files && files.length > 0) {
      query = [];
      // 使用文件，检查是否有缓存的attachment_id
      files.forEach(file => {
        // 从缓存中获取attachment_id
        let attachmentId = file.attachment_id;
        if (!attachmentId && file.object && fileAttachmentMap.has(file.object)) {
          attachmentId = fileAttachmentMap.get(file.object);
        }
        query.push({
          content: file.object,
          content_type: file.type.split('/')[1] || 'file',
          attachment_id: attachmentId,
          attachment_name: file.name,
        });
      });
      // 添加文本参数，如果有技能信息则拼接
      if (msg && msg.trim() !== '') {
        let finalMessage = msg;
        // 如果有技能信息，拼接技能
        if (skillInfo && skillInfo.label) {
          finalMessage = `${skillInfo.label}：${msg}`;
          console.log('拼接技能后的消息:', finalMessage);
        }
        query.push({
          content: finalMessage,
          content_type: 'str'
        });
      }
    } else {
      // 没有文件时使用原来的参数结构，但需要处理技能拼接
      let finalMessage = msg || "ex";
      // 如果有技能信息，拼接技能
      if (skillInfo && skillInfo.label && msg && msg.trim() !== '') {
        finalMessage = `${skillInfo.label}：${msg}`;
        console.log('拼接技能后的消息:', finalMessage);
      }
      query = finalMessage;
    }
    
    const params = { query };
    
    // 在商机会话场景下添加会话ID参数
    if (sessionId && sessionId.trim() !== '') {
      params.session_id = sessionId;
    }
    
    const response = await createTask(params);
    if (response.code === 0) {
      console.log("API Response:", response);
      uni.setStorageSync('task_id', response.data.task_id);
      
      // 发送完成后清空附件ID缓存
      fileAttachmentMap.clear();
      
      return response.data.task_id;
    } else {
      uni.showToast({
        title: response.message || '创建会话失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    uni.showToast({
      title: '创建会话失败，请稍后重试',
      icon: 'none'
    });
  }
  return null;
}

// 清除文件缓存
export function clearFileAttachmentCache() {
  fileAttachmentMap.clear();
}

/**
 * 获取当前的 task_id
 * @returns {string|null} 当前的 task_id 或 null
 */
export function getCurrentTaskId() {
  return uni.getStorageSync('task_id') || null;
}

/**
 * 清除当前的 task_id
 */
export function clearCurrentTaskId() {
  uni.removeStorageSync('task_id');
}

// 从后端获取阿里云 token 并存储到本地
export async function storeSpeechRecognitionToken() {
  try {
    const response = await fetchSpeechRecognitionToken();
    if (response.code === 0) {
      const { token } = response.data;
      // 存储 token 到本地
      uni.setStorageSync('aliyunToken', token);
      // console.log('Token successfully stored in local storage!');
      return token
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    uni.showToast({
      title: '获取 token 失败，请稍后重试',
      icon: 'none'
    });
  }
}

/**
 * 修复Markdown格式问题
 * 将非标准的列表格式转换为标准格式
 * @param {string} content - 原始内容
 * @returns {string} 修复后的内容
 */
export const fixMarkdownFormat = (content) => {
  if (!content || typeof content !== 'string') {
    return content;
  }
  let fixedContent = content;
  // 1. 修复混合列表格式：将 "- 1." 转换为 "1."
  // 这种格式会导致mp-html解析器混乱
  fixedContent = fixedContent.replace(/^(\s*)- (\d+\.\s)/gm, '\$1\$2');
  // // 2. 确保有序列表前有空行（如果前面不是空行或标题）
  // fixedContent = fixedContent.replace(/([^\n])\n(\d+\.\s)/g, '\$1\n\n\$2');
  // // 3. 确保无序列表前有空行（如果前面不是空行或标题）  
  // fixedContent = fixedContent.replace(/([^\n])\n(-\s)/g, '\$1\n\n\$2');
  // // 4. 修复标题后面的列表格式
  // fixedContent = fixedContent.replace(/([：:]\s*)\n(\d+\.\s)/g, '\$1\n\n\$2');
  // fixedContent = fixedContent.replace(/([：:]\s*)\n(-\s)/g, '\$1\n\n\$2');
  // // 5. 清理多余的空行（超过2个连续空行的情况）
  // fixedContent = fixedContent.replace(/\n{3,}/g, '\n\n');
  return fixedContent;
}

/**
 * 计算未读消息的正确索引位置
 * 用于抽屉列表点击后定位到第一条未读AI消息
 *
 * @param {Array} messages - 消息列表数组
 * @param {number} unreadNum - 未读消息数量
 * @returns {number} 返回消息在数组中的索引，-1表示未找到
 *
 * @example
 * // 假设有100条消息，其中50条AI消息，7条未读
 * const messages = [...]; // 消息数组
 * const targetIndex = calculateUnreadMessageIndex(messages, 7);
 * // 返回第一条未读AI消息在原数组中的索引
 */
export function calculateUnreadMessageIndex(messages, unreadNum) {
  // console.log('🔍 计算未读消息位置:', {
  //   unreadNum,
  //   totalMessages: messages?.length || 0
  // });
  // 参数验证
  if (!messages || !Array.isArray(messages)) {
    console.warn('❌ 消息列表无效');
    return -1;
  }
  if (!unreadNum || unreadNum <= 0) {
    console.log('📍 未读数量为0，返回-1');
    return -1;
  }
  // 获取所有AI消息及其在原数组中的索引
  const aiMessages = [];
  messages.forEach((msg, index) => {
    if (msg && msg.role === 'assistant') {
      aiMessages.push({
        message: msg,
        originalIndex: index,
        id: msg.id
      });
    }
  });
  // console.log(`📊 消息统计: 总消息${messages.length}条, AI消息${aiMessages.length}条, 未读${unreadNum}条`);
  // 边界情况处理：未读数量大于等于AI消息总数
  if (unreadNum >= aiMessages.length) {
    console.log('⚠️ 未读数量大于等于AI消息总数，定位到第一条AI消息');
    return aiMessages.length > 0 ? aiMessages[0].originalIndex : 0;
  }
  // 计算第一条未读AI消息的位置
  // 算法：从最新的AI消息开始往前数unreadNum条，找到第一条未读消息
  const firstUnreadAiIndex = aiMessages.length - unreadNum;
  if (firstUnreadAiIndex >= 0 && firstUnreadAiIndex < aiMessages.length) {
    const targetAiMessage = aiMessages[firstUnreadAiIndex];
    const targetIndex = targetAiMessage.originalIndex;
    console.log(`✅ 找到目标未读消息:`, {
      aiMessageIndex: firstUnreadAiIndex + 1, // 显示为第几条AI消息
      messageId: targetAiMessage.id,
      originalIndex: targetIndex,
      content: targetAiMessage.message.content?.substring(0, 50) + '...'
    });
    return targetIndex;
  }
  console.warn('❌ 无法计算未读消息位置');
  return -1;
}