<template>
  <!-- 商机 相关文件模块 -->
  <view class="business-details-container">
    <!-- 面包屑 -->
    <view class="section-box s-p-b" v-if="routes.length>1">
      <uni-breadcrumb separator="/">
        <uni-breadcrumb-item
          v-for="(route, index) in routes"
          :key="index"
          @click="handleBreadcrumbClick(index)"
        >
          {{ route.name }}
        </uni-breadcrumb-item>
      </uni-breadcrumb>
    </view>
    <!-- 当前 -->
    <view class="section-box s-p-b">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">按时间排序</view>
      </view>
      <uni-icons
        type="plusempty"
        size="20"
        color="#4D5BDE"
        @click="handeAddFileClick"
      ></uni-icons>
    </view>
    <!-- 文件列表 -->
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
      <!-- 添加 v-if/v-else 判断-->
      <template v-if="fileList && fileList.length > 0">
        <block v-for="(item, index) in fileList" :key="index">
          <view
            class="todo-item"
            @click.stop="item.type === 'dir' ? handleFileClick(item) : null"
          >
            <!-- 文件图标/图片预览 -->
            <view class="file-thumbnail">
              <!-- 对于图片类型，直接显示缩略图 -->
              <image
                v-if="isImageFile(item.name)"
                class="file-preview"
                :src="item.url"
                mode="aspectFill"
              ></image>
              <!-- 对于非图片类型，显示文件图标 -->
              <image
                v-else
                class="flie-icon"
                :src="item.type === 'dir' ? '/static/file/document.svg' : getFileIconPath(item.name)"
              ></image>
            </view>
            <view class="content-wrapper">
              <uni-easyinput v-if="item.isShowInput" trim="all" v-model="item.name"  @change="handelInputSetName(item)"  focus></uni-easyinput>
              <view class="todo-title" v-else @click="handleFilePreview(item)">{{ item.name }}</view>
              <!-- 添加上传时间显示 -->
              <view class="title-row">
                <view class="upload-time">{{ filterDateTime(item.created_at) }}</view>
                <!-- <view class="file-size" v-if="item.size">{{ formatFileSize(item.size) }}</view> -->
              </view>
            </view>
            <uni-icons
              v-if="item.type !== 'dir'"
              type="more-filled"
              size="20"
              color="#E2E4E9"
              @click="handeOpenClick(item)"
            ></uni-icons>
          </view>
          <!-- 分割线：除最后一项外 -->
          <view v-if="index !== fileList.length - 1" class="item-divider"></view>
        </block>
      </template>
        <!-- --- 添加 v-else 用于显示缺省状态 --- -->
        <template v-else>
          <view class="empty-placeholder" @click="handeAddFileClick">
            <text class="empty-text">暂无文件，点击添加</text>
          </view>
        </template>
      </view>
    </transition>
    <ActionSheet
      v-if="operationFileShow"
      v-model:show="operationFileShow"
      :actions="actions"
      title="文件操作"
      @select="onFileSelect"
    />
    <!-- xe-upload 组件（隐藏，用于文件选择） -->
    <xe-upload 
      ref="xeUpload" 
      @callback="handleXeUploadCallback"
    />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import XeUpload from "@/uni_modules/xe-upload/components/xe-upload/xe-upload.vue";
import {
  generateFileUploadSignature,
  saveOrEditAttachment,
  deleteAttachment,
} from "@/http/attachment.js";
import { filterDateTime } from "@/utils/formatTime.js";
import { 
  isImageFile, 
  formatFileSize,
  openWebOfficePreview,
  getFileIconPath,
  getMimeType,
  getFileExtension,
  downloadFile,
 } from "@/utils/fileUtils.js";
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具
import { useFileUpload } from '@/utils/useFileUpload.js'; // 导入文件上传hooks
// 使用文件上传hooks
  const {
    chooseAndUpload,
    uploading,
    uploadProgress, 
    setXeUploadRef, 
    handleXeUploadCallback 
  } = useFileUpload();

const emit = defineEmits(["refresh", "onAddAttachment"]);
const props = defineProps({
  // 客户文件列表数据
  fileList: {
    type: Array,
    default: [],
  },
  businessId:String | Number,
});
const currentFile = ref(null); // 当前操作的文件
const operationFileShow = ref(false);
// 修改状态管理
const expandStates = ref({
  current: true,
  finished: true,
});
const routes = ref([
  {
    to: "#0",
    name: "全部文件",
  },
]);
// 面包屑导航相关
const pathStack = ref([
  {
    id: 0,
    name: "全部文件",
    data: props.fileList,
  },
]);
const currentData = ref();
// xe-upload 组件引用
const xeUpload = ref(null);

const actions = ref([
  { name: "预览" },
  { name: "下载" },
  { name: "重命名" },
  { name: "删除", color: "danger" },
  // { name: '重命名', subname: '描述信息' },
  // { name: '禁用选项', disabled: true },
  // { name: '加载选项', loading: true },
]);

// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
  expandStates.value[type] = !expandStates.value[type];
};
// 弹出客户文件下拉
const handeOpenClick = (item) => {
  currentFile.value = item;
  operationFileShow.value = true;
};
// 点击文件夹
const handleFileClick = (items) => {
  if (items.type !== "dir") return;
  pathStack.value.push({
    id: items.id,
    name: items.title,
    data: items.children || [],
  });
  updateBreadcrumb();
  currentData.value = items.children || [];
};
// 更新面包屑
const updateBreadcrumb = () => {
  routes.value = pathStack.value.map((item) => ({
    to: `#${item.id}`,
    name: item.name,
  }));
};
// 点击面包屑
const handleBreadcrumbClick = (index) => {
  pathStack.value = pathStack.value.slice(0, index + 1);
  updateBreadcrumb();
  currentData.value =
    index === 0 ? props.fileList : pathStack.value[index].data;
};
/**
 * 选取文件进行上传
 */
const handeAddFileClick = async () => {
  console.log('🚀 开始选择文件上传...')
  try {
    // 使用新的 hooks 选择并上传文件
    const results = await chooseAndUpload('file', {
      count: 9, // 最多选择9个文件
      // APP端支持的文件扩展名
      // #ifdef H5
      extension: [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // 图片
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', // 文档
        '.txt', '.rtf', // 文本
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', // 视频
        '.mp3', '.wav', '.aac', '.flac', // 音频
        '.zip', '.rar', '.7z', '.tar', '.gz' // 压缩包
      ],
      // #endif
      // #ifdef APP
      extension:'*',
      // #endif
    });
    if (results && results.length > 0) {
      console.log('✅ 文件上传成功:', results);
      // 批量保存附件信息到后端
      for (const result of results) {
        try {
          const attachmentData = {
            classify: 2, // 销售物料分类
            name: result.fileName,
            url: result.url,
            size: result.size || 0,
            type: 'file',
            classify_id:props.businessId,
          };
          const saveRes = await saveOrEditAttachment(attachmentData);
          if (saveRes.code === 0) {
            console.log('附件信息保存成功:', result.fileName);
          }
        } catch (saveError) {
          console.error('保存附件信息失败:', result.fileName, saveError);
        }
      }
      // 触发刷新事件，通知父组件刷新文件列表
      emit('refresh');
      uni.showToast({
        title: `上传成功`,
        icon: 'success',
        duration: 2000
      });
    }
  } catch (error) {
    console.error('💥 文件上传过程出错:', error);
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none',
      duration: 3000
    });
  }
};

// 点击选项时触发，禁用或加载状态下不会触发
const onFileSelect = (item, index) => {
  if (!currentFile.value) return;
  switch (item.name) {
    case "预览":
    handleFilePreview(currentFile.value);
    operationFileShow.value = false;
      break;
    case "下载":
    oneClickDownload(currentFile.value);
      operationFileShow.value = false;
      break;
    case "删除":
      deleteFile(currentFile.value);
      operationFileShow.value = false;
      break;
    case "重命名":
      setRenameFile(currentFile.value);
      operationFileShow.value = false;
      break;
  }
};
/**
 * 处理文件预览
 * @param {Object} file - 文件对象
 */
 const handleFilePreview = async (file) => {
  try {
    // 调用预览服务，传入不同平台的预览回调
    await previewFile(file, {
      onWebOfficePreview: openWebOfficePreview,// H5端WebOffice预览
    });
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};

/**
 * 下载文件 - 调用公共下载函数
 * @param {Object} file - 文件对象，包含url和name属性
 */
 const oneClickDownload = async (file) => {
  try {
    await downloadFile(file.url, file.name);
  } catch (error) {
    console.error('下载失败:', error);
    // 公共函数内部已经处理了错误提示，这里不需要重复提示
  }
};
// 删除文件
const deleteFile = async (delItem) => {
  try {
    // 确保在删除操作前就将输入框状态设为false，防止闪现
    if(delItem) {
      delItem.isShowInput = false;
    }
    // 显示加载中，阻止用户操作，防止输入框闪现
    uni.showLoading({
      title: '删除中...'
    });
    const res = await deleteAttachment({
      id: delItem.id,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除文件成功",
        icon: "success",
      });
      emit("refresh");
    }
  } catch (error) {
    console.error("删除失败:", error);
    uni.showToast({
      title: "删除失败",
      icon: "none",
    });
  } finally {
    // 无论成功或失败，都隐藏加载状态
    uni.hideLoading();
  }
};

// 重命名
const setRenameFile = async (nameItem) => {
  nameItem.isShowInput = true;
};
// 输入框修改事件
const handelInputSetName = async (item) => {
  item.isShowInput = false;
  try {
    const res = await saveOrEditAttachment({
      id:item.id,
      classify: 2,
      name: item.name,
    });
    if (res.code === 0) {
      emit("refresh");
    }
  } catch (error) {
    console.error("重命名请求失败:", error);
  }
};

// 组件挂载后设置 xe-upload 引用
onMounted(() => {
  if (xeUpload.value) {
    setXeUploadRef(xeUpload.value);
  }
});
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.business-details-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .section-box {
    display: flex;
    align-items: center;
    margin: 15px 5px 15px 5px;
    padding: 0px 10px;
    .section-left {
      display: flex;
      align-items: center;
      .icon-down {
        width: 25px;
        height: 25px;
      }
    }
    .section-title {
      color: var(---, #787d86);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
  .s-p-b {
    justify-content: space-between;
  }
  .todo-card {
    margin: 15px;
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;
    .empty-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px; /* 上下留白多一些 */
      text-align: center;
      cursor: pointer; /* 添加手型光标提示可点击 */
    }
    .empty-text {
      font-size: 14px;
      color: #999;
    }

    .todo-item {
      display: flex;
      align-items: center;
      margin: 10px 14px 0 14px;
      height: 56px; /* 设置固定高度为56px */
      max-height: 56px;
      /* 文件缩略图/图标区域 */
      .file-thumbnail {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        .flie-icon {
          width: 24px;
          height: 24px;
        }
      }
      .content-wrapper {
        flex: 1;
        margin-left: 6px;
        overflow: hidden;
      }
      .todo-title {
        color: var(---, #000);
        font-size: 14px;
        white-space: nowrap; /* 确保文本不换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        text-overflow: ellipsis; /* 显示省略号 */
        max-width: 100%; /* 确保宽度不超过父容器 */
      }
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2px;
        color: var(---, #adb1ba);
        font-size: 10px;
        .upload-time {
          flex: 1;
        }
        .file-size {
          margin-left: 8px;
        }
      }
    }
    /* 分割线 */
    .item-divider {
      height: 1px;
      background-color: #f0f0f0;
      margin-left: 44px; /* 14px 左外边距 + 24px 缩略图 + 6px 间距 */
      margin-right: 16px;
    }
  }
}
</style>
