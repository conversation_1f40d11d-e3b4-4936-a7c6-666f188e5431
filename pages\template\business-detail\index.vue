<!-- 商机详情 -->
<template>
  <z-paging
    ref="pagingRef"
    v-model="dataList"
    @query="queryCustomerList"
    :refresherEnabled="false"
    :showLoadingMoreNoMoreView="false"
    :defaultPageSize="30"
    :style="appSafeAreaStyle"
    :hideEmptyView="true"
  >
    <template #top>
      <!-- 顶部导航栏 -->
      <uni-nav-bar
        left-icon="left"
        right-icon="more-filled"
        :title="businessTitle"
        title-text-align="center"
        backgroundColor="#fff"
        color="#000"
        height="60px"
        @clickLeft="handerLeftBack"
        @clickRight="handerRightEdit"
      />
      <z-tabs :list="tabList" :current="1" @change="tabsChange" />
    </template>
    <!-- 商机活动 -->
    <businessActivities
      v-if="tabIndex === 0"
      :dataList="dataList"
      :cfmAISuggestionsList="suggestionsAI"
      :userDetails="businessDetails"
      @refresh="queryCustomerList"
    />
    <!-- 商机详情 -->
    <tabsBusDetails
      v-if="tabIndex === 1"
      :userDetails="businessDetails"
      @refresh="queryCustomerList"
      @RefreshList="handeRefreshList"
    />
    <!-- 相关文件 -->
    <businessFile
      v-if="tabIndex === 2"
      :fileList="dataList"
      :businessId="business_id"
      @refresh="queryCustomerList"
    />
    <ActionSheet
      v-if="businessShow"
      v-model:show="businessShow"
      :actions="actions"
      title="操作"
      @select="onFileSelect"
    />
  </z-paging>
  <!-- AI 对话组件 -->
  <CustomerService />
</template>
<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import businessActivities from "./business-activities.vue";
import businessFile from "./business-file.vue";
import tabsBusDetails from "./tabs-bus-details.vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import CustomerService from "@/components/CustomerService.vue";
import { onLoad } from "@dcloudio/uni-app";
import { getTodoList } from "@/http/todo.js";
import { clearLinkTapStatus } from '@/utils/globalState.js';
import { removeOpportunity, fetchOpportunityDetail } from "@/http/business.js";
import {
  fetchAttachmentList,
  saveOrEditAttachment,
} from "@/http/attachment.js";
import { eventBus } from "@/utils/eventBus.js";
// 禁用属性继承，避免URL参数被当作props
defineOptions({
  inheritAttrs: false
})

const pagingRef = ref(null);
// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
const dataList = ref([]);
const businessDetails = ref({});
const tabIndex = ref(1);
const businessTitle = ref("商机详情");
const business_id = ref("");
const suggestionsAI = ref([]); // 需求确认AI建议
const businessShow = ref(false);
const actions = ref([{ name: "删除", color: "danger" }]);
const tabList = ref(["商机活动", "商机详情", "相关文件"]);

// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`,
  };
  // #endif
  return {};
});

// 【顶部导航栏】左侧按钮点击时触发
const handerLeftBack = () => {
  clearLinkTapStatus()
  uni.switchTab({
    url: "/pages/tabBar/business/business",
  });
};
// 【顶部导航栏】右侧按钮点击时触发
const handerRightEdit = () => {
  businessShow.value = true;
};
// tab 切换事件处理方法
const tabsChange = (index) => {
  tabIndex.value = index;
  // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
  pagingRef.value.reload();
};
// 点击选项时触发，禁用或加载状态下不会触发
const onFileSelect = (item) => {
  switch (item.name) {
    case "删除":
      onDeleteBusiness();
      businessShow.value = false;
      break;
  }
};
// 删除商机
const onDeleteBusiness = async () => {
  try {
    const res = await removeOpportunity({
      id: business_id.value,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除成功",
        icon: "success",
      });
      // 触发全局事件，通知列表页刷新
      // eventBus.emit("businessUpdated", business_id.value);
      // 返回上一页（待办列表页）
      uni.switchTab({
        url: "/pages/tabBar/business/business",
      });
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};

// 刷新商机列表
const handeRefreshList = () => {
  // 触发全局事件，通知列表页刷新
  eventBus.emit("businessUpdated", business_id.value);
};

// 获取商机详情
const getBusinessOpportunityDetails = async () => {
  try {
    const res = await fetchOpportunityDetail({
      id: business_id.value,
      type: tabIndex.value + 1,
    });
    if (res.code === 0) {
      businessDetails.value = res.data;
      suggestionsAI.value = res.data.todo_suggest;
      businessTitle.value = res.data.title  || "商机详情"; // 添加可选链和默认值
      console.error("获取商机详情:", res.data);
    }
  } catch (error) {
    // 错误处理代码
    console.error("获取商机详情失败:", err);
  }
};

// 初始化代办列表
const queryCustomerList = async (pageNo, pageSize) => {
  try {
    // 获取待办任务列表数据
    if (tabIndex.value === 0) {
      const res = await getTodoList({
        page: pageNo || 1,
        limit: pageSize || 30,
        bo_id: business_id.value,
        type: tabIndex.value + 1,
      });
      console.log("客户活动:", res);
      const pageData = res.data.list || [];
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
    // 获取商机详情数据
    if (tabIndex.value === 1) {
      getBusinessOpportunityDetails();
    }
    // 获取商机相关文件数据
    if (tabIndex.value === 2) {
      const res = await fetchAttachmentList({
        // 1 客户文件，2 商机文件，3 产品介绍，4 客户资料
        classify: 2,
        classify_id: business_id.value,
        page: pageNo || 1,
        limit: pageSize || 30,
      });
      console.log("相关文件:", res);
      const pageData = res.data.list || [];
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
  } catch (err) {
    console.error("客户请求失败:", err);
  }
};

// 使用 Uni-app 官方生命周期
onLoad(async (options) => {
  // 这里 options 包含所有平台的参数
  console.log("路由参数:", options);
  if (options.business_id) {
    business_id.value = options.business_id;
    // 等待获取商机详情数据完成
    await getBusinessOpportunityDetails();
  }
});

onMounted(() => {});
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/public-layout.scss';
</style>
