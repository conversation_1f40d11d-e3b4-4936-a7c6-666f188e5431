<template>
  <view
    class="message-actions"
    :class="{ 'actions-visible': isVisible }"
    v-if="showActions"
  >
    <!-- 语音播放按钮 -->
    <view class="item-icon" @click.stop="handlePlayVoice">
      <!-- PC端添加tooltip -->
      <uni-tooltip v-if="isPCDevice" content="语音朗读" placement="top">
        <!-- 非播放状态显示静态图片 -->
        <image
          v-if="isPlaying === 0"
          src="/static/chatImg/list-play.svg"
          alt="语音播放"
          class="play-icon"
        />
        <!-- 播放状态显示动画效果 -->
        <view v-else class="voice-playing-animation">
          <view class="voice-bar" v-for="(item, i) in 4" :key="i"></view>
        </view>
      </uni-tooltip>

      <!-- 移动端不显示tooltip -->
      <template v-else>
        <!-- 非播放状态显示静态图片 -->
        <image
          v-if="isPlaying === 0"
          src="/static/chatImg/list-play.svg"
          alt="语音播放"
          class="play-icon"
        />
        <!-- 播放状态显示动画效果 -->
        <view v-else class="voice-playing-animation">
          <view class="voice-bar" v-for="(item, i) in 4" :key="i"></view>
        </view>
      </template>
    </view>

    <!-- 复制按钮 -->
    <view class="item-icon" @click.stop="handleCopy">
      <!-- PC端添加tooltip -->
      <uni-tooltip v-if="isPCDevice" content="复制" placement="top">
        <image
          src="/static/chatImg/copy.svg"
          class="item-icon-img"
          alt="复制"
        />
      </uni-tooltip>

      <!-- 移动端不显示tooltip -->
      <image
        v-else
        src="/static/chatImg/copy.svg"
        class="item-icon-img"
        alt="复制"
      />
    </view>

    <!-- 点赞 -->
    <view class="item-icon" @click.stop="handleThumbsUp">
      <!-- PC端添加tooltip -->
      <uni-tooltip v-if="isPCDevice" :content="likeStatus ? '取消点赞' : '点赞'" placement="top">
        <image
          :src="likeStatus ? '/static/chatImg/chat-thumbs-up-active.svg' : '/static/chatImg/chat-thumbs-up.svg'"
          class="item-icon-img"
          :class="{ 'icon-active': likeStatus }"
          alt="点赞"
        />
      </uni-tooltip>
      <!-- 移动端不显示tooltip -->
      <image
        v-else
        :src="likeStatus ? '/static/chatImg/chat-thumbs-up-active.svg' : '/static/chatImg/chat-thumbs-up.svg'"
        class="item-icon-img"
        :class="{ 'icon-active': likeStatus }"
        alt="点赞"
      />
    </view>

    <!-- 踩 -->
    <view class="item-icon" @click.stop="handleThumbsDown">
      <!-- PC端添加tooltip -->
      <uni-tooltip v-if="isPCDevice" :content="dislikeStatus ? '取消踩' : '踩'" placement="top">
        <image
          :src="dislikeStatus ? '/static/chatImg/chat-thumbs-down-active.svg' : '/static/chatImg/chat-thumbs-down.svg'"
          class="item-icon-img"
          :class="{ 'icon-active': dislikeStatus }"
          alt="踩"
        />
      </uni-tooltip>
      <!-- 移动端不显示tooltip -->
      <image
        v-else
        :src="dislikeStatus ? '/static/chatImg/chat-thumbs-down-active.svg' : '/static/chatImg/chat-thumbs-down.svg'"
        class="item-icon-img"
        :class="{ 'icon-active': dislikeStatus }"
        alt="踩"
      />
    </view>

    <!-- 更多按钮 -->
    <view class="item-icon">
      <!-- #ifdef H5 -->
      <!-- H5端使用 van-popover -->
        <uni-tooltip v-if="isPCDevice" content="更多" placement="top">
          <van-popover 
            v-model:show="showPopover"
            :actions="actions"  
            @select="onSelect"
        >
          <template #reference>
            <image
              src="/static/chatImg/chat-more.svg"
              class="item-icon-img"
              alt="更多"
            />
          </template>
        </van-popover>
      </uni-tooltip>
      <van-popover 
          v-else
          v-model:show="showPopover"
          :actions="actions"  
          @select="onSelect"
      >
        <template #reference>
          <image
            src="/static/chatImg/chat-more.svg"
            class="item-icon-img"
            alt="更多"
          />
        </template>
      </van-popover>
      <!-- #endif -->
    </view>
    
  </view>
</template>

<script>
import { saveChatFeedback } from '@/http/ai-chat-task.js'
import { copyMessage,preprocessTextForVoice} from '@/utils/utils.js'
// #ifdef H5  
import { voicePlayService, playVoice as playVoiceService } from '@/services/voicePlayService.js'
// #endif

export default {
  name: 'AiMessageActions',
  // 组件引入
  components: {
  },
  // Props定义
  props: {
    // 消息内容
    content: {
      type: String,
      required: true,
    },
    // 消息ID（用于评价状态管理）
    messageId: {
      type: [String, Number],
    },
    // 是否显示操作按钮
    isVisible: {
      type: Boolean,
      default: false,
    },
    // 是否显示操作区域
    showActions: {
      type: Boolean,
      default: true,
    },
    // 消息索引（用于删除）
    messageIndex: {
      type: Number,
      default: 0,
    },
    // 新增：播放状态
    playStatus: {
      type: Number,
      default: 0 // 0: 未播放, 1: 播放中
    },
    // 新增：初始点赞状态
    initialLikeStatus: {
      type: Number,
      default: 0 // 0: 未点赞, 1: 已点赞
    },
    // 新增：初始点踩状态
    initialDislikeStatus: {
      type: Number,
      default: 0 // 0: 未踩, 1: 已踩
    },
    // 新增：初始点踩原因
    initialDislikeReason: {
      type: String,
      default: ''
    },
  },
  
  // 响应式数据
  data() {
    return {
      // 检测是否为PC设备
      isPCDevice: false,
      showPopover: false,
      // 语音播放状态
      isPlaying: 0,
      // 点赞状态 (0: 未点赞, 1: 已点赞)
      likeStatus: 0,
      // 踩状态 (0: 未踩, 1: 已踩) 
      dislikeStatus: 0,
      // #ifdef H5
      // H5端使用 van-popover 的 actions 格式
      actions: [
        { text: '删除', icon: 'https://profly-website.oss-cn-shenzhen.aliyuncs.com/static/images/trash.svg', value: 0 },
      ],
      // #endif
      // 窗口大小变化监听器
      resizeListener: null,
      // #ifdef H5
      // 语音播放状态监听定时器
      voicePlayTimer: null,
      // #endif
    }
  },
  
  // 监听器
  watch: {
    // 监听操作按钮的可见性变化
    isVisible: {
      handler(newValue) {
        if (newValue === false) {
          // #ifdef H5
          this.showPopover = false;
          // #endif
          
          // #ifndef H5
          // APP端关闭弹出层
          if (this.$refs.actionSheetPopup) {
            this.$refs.actionSheetPopup.close();
          }
          // #endif
        }
      },
      deep: true // 开启深度监听
    },

    // 监听传入的初始状态变化
    initialLikeStatus: {
      handler(newStatus) {
        if (newStatus !== undefined) {
          this.likeStatus = newStatus;
        }
      },
      immediate: true
    },
    initialDislikeStatus: {
      handler(newStatus) {
        if (newStatus !== undefined) {
          this.dislikeStatus = newStatus;
        }
      },
      immediate: true
    },
    // #ifdef APP-PLUS
    // 监控app 端语音播放状态，然后同步播放动画效果
    playStatus: {
      handler(newPlayStatus) {
        if (newPlayStatus !== undefined) {
          this.isPlaying = newPlayStatus;
        }
      },
      immediate: false
    }
    // #endif
  },
  
  // 生命周期钩子
  mounted() {
    this.initDeviceDetection();
    // #ifdef H5
    // 监听语音播放服务的状态变化
    this.setupVoicePlayListener();
    // #endif
  },
  
  beforeDestroy() {
    // #ifdef H5
    this.cleanupDeviceDetection();
    this.cleanupVoicePlayListener();
    // #endif
  },
  
  // 方法
  methods: {
    /**
     * 提交消息评价到后端接口
     * @param {Object} params - 评价参数
     * @returns {Promise} 返回提交结果
     */
    async submitMsgRating(params) {
      try {
        console.log('📤 提交评价请求参数:', params);
        // 构建请求参数 - 根据 /api/chat/save 接口规范
        const oldParams = params.message_id
        ? { id: params.message_id }
        : { task_id:  params.task_id };
        const requestData = {
          ...oldParams,
          is_like: params.like_status || 0, // 是否点赞：0-未点赞，1-已点赞
          is_dislike: params.dislike_status || 0, // 是否点踩：0-未踩，1-已踩
          dislike_reason: params.dislike_reason || '' // 点踩原因
        };
        // 调用后端接口
        const response = await saveChatFeedback(requestData);
        console.log('📥 后端响应:', response);
        // 如果请求成功，返回结果
        if (response.code === 0) {
          return {
            code: 0,
            message: response.msg || '提交成功',
            data: response.data || {}
          };
        } else {
          return {
            code: response.code || -1,
            message: response.msg || '提交失败',
            data: {}
          };
        }
      } catch (error) {
        console.error('❌ 提交评价失败:', error);
      }
    },

    /**
     * 处理点赞功能
     */
    async handleThumbsUp() {
      try {
        this.showPopover = false;
        // 切换点赞状态
        const newLikeStatus = this.likeStatus === 1 ? 0 : 1;
        // 如果要点赞，先取消踩的状态
        const newDislikeStatus = newLikeStatus === 1 ? 0 : this.dislikeStatus;
        // 点赞直接提交，不需要反馈弹框
        await this.submitRatingChange(newLikeStatus, newDislikeStatus);
        // 显示操作反馈
        const message = newLikeStatus === 1 ? '👍 已点赞' : '已取消点赞';
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 1500
        });
      } catch (error) {
        console.error('❌ 点赞操作失败:', error);
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    /**
     * 处理踩功能
     */
    async handleThumbsDown() {
      try {
        this.showPopover = false;
        // 切换踩状态
        const newDislikeStatus = this.dislikeStatus === 1 ? 0 : 1;
        // 如果要踩，先取消点赞状态
        const newLikeStatus = newDislikeStatus === 1 ? 0 : this.likeStatus;
        // 如果是要踩（从0到1），通知父组件打开反馈弹框
        if (newDislikeStatus === 1) {
          // 通知父组件打开反馈弹框
          this.$emit('openFeedback', this.messageId, {
            like_status: newLikeStatus,
            dislike_status: newDislikeStatus
          });
          return; // 不立即提交，等待反馈弹框操作
        }
        // 如果是取消踩（从1到0），直接提交
        await this.submitRatingChange(newLikeStatus, newDislikeStatus);
      } catch (error) {
        console.error('❌ 踩操作失败:', error);
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    /**
     * 提交评价状态变更
     * @param {number} newLikeStatus - 新的点赞状态
     * @param {number} newDislikeStatus - 新的踩状态  
     * @param {string} dislikeReason - 踩的原因（可选）
     */
    async submitRatingChange(newLikeStatus, newDislikeStatus, dislikeReason = '') {
      try {
        // 提交到服务端
        const response = await this.submitMsgRating({
          message_id: this.messageId,
          task_id: uni.getStorageSync('task_id') || 0,
          like_status: newLikeStatus,
          dislike_status: newDislikeStatus,
          dislike_reason: dislikeReason
        });
        if (response.code === 0) {
          // 保存旧状态用于判断
          const oldDislikeStatus = this.dislikeStatus;
          // 更新本地状态
          this.likeStatus = newLikeStatus;
          this.dislikeStatus = newDislikeStatus;
          // 显示操作反馈（仅在取消踩时显示toast，其他操作在调用方显示）
          if (newDislikeStatus === 0 && oldDislikeStatus === 1) {
            uni.showToast({
              title: '已取消踩',
              icon: 'none',
              duration: 1500
            });
          }
        } else {
          throw new Error(response.message || '提交失败');
        }
      } catch (error) {
        console.error('❌ 提交评价变更失败:', error);
        uni.showToast({
          title: error.message || '操作失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 更新点赞/点踩状态（供父组件调用）
     * @param {number} likeStatus - 点赞状态
     * @param {number} dislikeStatus - 点踩状态
     */
    updateRatingStatus(likeStatus, dislikeStatus) {
      this.likeStatus = likeStatus;
      this.dislikeStatus = dislikeStatus;
      console.log('🔄 更新评价状态:', { likeStatus, dislikeStatus });
    },

    /**
     * 检测设备类型
     * 基于窗口宽度判断是否为PC端
     */
    detectDeviceType() {
      // #ifdef H5
      if (typeof window !== 'undefined') {
        // PC端判断条件：窗口宽度大于768px
        const isPC = window.innerWidth > 768;
        this.isPCDevice = isPC;
        // console.log('🖥️ 设备类型检测:', isPC ? 'PC端' : '移动端', '窗口宽度:', window.innerWidth);
      }
      // #endif
      
      // #ifndef H5
      // 在非H5平台，默认为移动端
      this.isPCDevice = false;
      // #endif
    },
    
    /**
     * 初始化设备检测和窗口监听
     */
    initDeviceDetection() {
      // #ifdef H5
      if (typeof window !== 'undefined') {
        // 初始检测
        this.detectDeviceType();

        // 添加窗口大小变化监听
        this.resizeListener = () => {
          this.detectDeviceType();
        };

        window.addEventListener('resize', this.resizeListener);
        // console.log('🔄 已启动窗口大小变化监听');
      }
      // #endif
      
      // #ifndef H5
      // 在非H5平台（如APP端），只需要检测设备类型
      this.detectDeviceType();
      // #endif
    },
    
    /**
     * 清理窗口监听器
     */
    cleanupDeviceDetection() {
      // #ifdef H5
      if (typeof window !== 'undefined' && this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
        this.resizeListener = null;
        console.log('🧹 已清理窗口大小变化监听');
      }
      // #endif
    },
    
    /**
     * 设置语音播放状态监听
     */
    setupVoicePlayListener() {
      // #ifdef H5
      // 定时检查语音播放状态
      this.voicePlayTimer = setInterval(() => {
        if (voicePlayService && voicePlayService.isPlaying) {
          this.isPlaying = voicePlayService.isPlaying.value || 0;
        }
      }, 100);
      // #endif
    },
    
    /**
     * 清理语音播放状态监听
     */
    cleanupVoicePlayListener() {
      // #ifdef H5
      if (this.voicePlayTimer) {
        clearInterval(this.voicePlayTimer);
        this.voicePlayTimer = null;
      }
      // #endif
    },
    
    /**
     * 处理语音播放
     */
    handlePlayVoice() {
      // 预处理文本内容，过滤URL链接
      const processedContent = preprocessTextForVoice(this.content);
      // 检查处理后的内容是否为空或过短
      if (!processedContent.trim()) {
        console.warn('语音播放内容为空，可能全部为链接内容');
        uni.showToast({
          title: '该消息主要包含链接，暂无文本可播放',
          icon: 'none',
          duration: 2500
        });
        return;
      }
      // #ifdef H5
      // 调用服务中的方法，使用处理后的内容
      playVoiceService(processedContent, this.isPlaying);
      // #endif
      // #ifdef APP-PLUS
      this.$emit("playAppVoice", processedContent, this.isPlaying);
      // #endif
    },
    
    /**
     * 处理复制
     */
    handleCopy() {
      copyMessage(this.content);
    },
    /**
     * H5端 van-popover 选择事件处理
     */
    // #ifdef H5
    onSelect(item) {
      if (item.value === 0) {
        this.$emit("delete", this.messageIndex);
      }
    },
    // #endif
    
    /**
     * APP端 action-sheet 选择事件处理
     */
    onActionSheetSelect(item) {
      if (item.value === 0) {
        this.$emit("delete", this.messageIndex);
      }
      // 关闭弹出层
      this.closeActionSheet();
    },
   
    /**
     * 关闭 action sheet
     */
    closeActionSheet() {
      // #ifdef H5
      if (this.$refs.actionSheetPopup) {
        this.$refs.actionSheetPopup.close();
      }
      // #endif
    },
    
    /**
     * 关闭所有popover弹窗
     * 供父组件调用的方法
     */
    closePopover() {
      // #ifdef H5
      this.showPopover = false;
      // #endif
      
      // #ifndef H5
      // APP端关闭弹出层
      if (this.$refs.actionSheetPopup) {
        this.$refs.actionSheetPopup.close();
      }
      // #endif
    }
  }
}
</script>

<style scoped lang="scss">
.message-actions {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-left: 4px;
  height: 20px;
  opacity: 0;
  width: max-content;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;

  &.actions-visible {
    opacity: 1;
    visibility: visible;
  }

  .uni-icons {
    margin-right: 10px;
  }

  :deep(.uni-tooltip-popup) {
    width: max-content;
  }
}

// 消息项
.item-icon {
  height: 20px;
  width: 20px;
  margin-right: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  .item-icon-img {
    height: 20px;
    width: 20px;
    transition: all 0.2s ease;
    
    &.icon-active {
      transform: scale(1.05);
    }
  }
}

.play-icon {
  width: 20px;
  height: 20px;
}

// 语音播放动画样式
.voice-playing-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;

  .voice-bar {
    width: 2px;
    height: 10px;
    background-color: #007aff; // 使用默认主色调
    margin: 0 1px;
    border-radius: 1px;
    animation: sound-wave 1s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
      height: 13px;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
      height: 16px;
    }

    &:nth-child(4) {
      animation-delay: 0.6s;
      height: 10px;
    }
  }
}

// 声波动画关键帧
@keyframes sound-wave {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(0.3);
  }
}
</style>
