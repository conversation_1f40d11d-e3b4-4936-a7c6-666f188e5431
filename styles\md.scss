/* 解决 passive event listener 报错的全局样式 */
* {
  //   touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}
h1 {
 margin: 0 0 16px !important;
}

h2 {
  margin: 24px 0 8px !important;
}
h3 {
  margin: 24px 0 8px !important;
}
h4 {
  margin: 24px 0 8px;
}
h5 {
  margin: 24px 0 8px;
}

ol {
  display: block !important;
  list-style-type: decimal !important;
  margin-block-start: 0.5em !important;
  margin-block-end: 0.5em !important;
  padding-inline-start: 25px !important;
  unicode-bidi: isolate !important;
}

ul {
  display: block !important;
  list-style-type: disc !important;
  margin-block-start: 0.5em !important;
  margin-block-end: 0.5em !important;
  padding-inline-start: 25px !important;
  unicode-bidi: isolate !important;
}

li {
  display: list-item !important;
  text-align: -webkit-match-parent !important;
  unicode-bidi: isolate !important;
  margin: 7px 0px;
}

// export const tableTagStyle = {
//     ol: 'padding-left: 21px;',
//     ul: 'padding-left: 16px;margin:-15px 0px;',
//     li: 'margin: 0px 2px;',
//     code: 'color: #000000d9;font-size: 12px;',
//     h1: 'margin: 10px 0 10px 0;',
//     h2: 'margin: 10px 0 10px 0;',
//     h3: 'margin: 10px 0 10px 0;',
//     h4: 'margin: 10px 0 10px 0;',
//     h5: 'margin: 10px 0 10px 0;',
//     h6: 'margin: 10px 0 10px 0;',
//   };

hr {
  display: block;
  margin-block-start: 1em !important; /* 7.5px 改为 15px */
  margin-block-end: 1em !important;   /* 7.5px 改为 15px */
  margin-inline-start: auto;
  margin-inline-end: auto;
  unicode-bidi: isolate;
  border-width: 1px;
  color: rgba(0, 0, 0, 0.08); /* 颜色加上透明度 0.08 */
}

p {
  display: block;
  margin-block-start: 0.5em;
  margin-block-end: 0.5;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
}

.md-p {
  // -webkit-margin-before: 1em;
  // -webkit-margin-after: 1em;
  margin-block-start: 0.5em !important;
  margin-block-end: 0.5em !important;
}
._img {
  max-height: 500px;
  // #ifdef APP-PLUS
  max-width: 110px !important;
  // #endif
}
._a {
  display: inline-block !important;
  color: #4d5bde;
  font-weight: 600;
  cursor: pointer;
  margin: 4px 0;
}
._ul {
  padding-left: 25px !important;
  margin: 7px 0 !important;
}

.hl-pre {
  background: #f9fafb !important;
  border-radius: 10px;
  .hl-code {
    color: #000000d9 !important;
    font-size: 12px !important;
  }
}
