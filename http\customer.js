// 引用网络请求中间件
import request from '@/utils/request';

// 查询客户接口
export function getCustomerList(data) {
	return request({
		url: '/api/customer/list',
		method: 'POST',
		data
	})
}

// 新增客户接口
export function addCustomer(data) {
	return request({
		url: '/api/customer/save',
		method: 'POST',
		data
	})
}

// 删除客户接口
export function deleteCustomer(data) {
	return request({
		url: '/api/customer/del',
		method: 'POST',
		data
	})
}

// 修改客户接口
export function updateCustomer(data) {
	return request({
		url: '/api/customer/save',
		method: 'POST',
		data
	})
}

// 查看客户接口
export function getCustomerDetail(data) {
	return request({
		url: '/api/customer/detail',
		method: 'GET',
		data
	})
}

// 保存客户联系人
export function saveCustomerContact(data) {
	return request({
		url: '/api/customerContact/save',
		method: 'POST',
		data
	})
}

// 删除客户联系人
export function deleteCustomerContact(data) {
	return request({
		url: '/api/customerContact/del',
		method: 'POST',
		data
	})
}

