{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__E060347", "name": "Ivy", "version": {"name": "1.2.1", "code": 21}, "description": "销售代理Web应用，用于管理销售工作。", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Camera": {}, "Speech": {}, "Push": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "permissions": {"READ_EXTERNAL_STORAGE": {"description": "读取外部存储"}, "WRITE_EXTERNAL_STORAGE": {"description": "写入外部存储"}, "MANAGE_EXTERNAL_STORAGE": {"description": "管理外部存储"}}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "allowHosts": ["*.profly.com.cn", "apitest.profly.com.cn"], "distribute": {"icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.MANAGE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>", "<uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\"/>", "<uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\"/>", "<uses-permission android:name=\"android.permission.READ_MEDIA_AUDIO\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>"], "minSdkVersion": 21, "targetSdkVersion": 30}, "apple": {"dSYMs": false, "privacyDescription": {"NSMicrophoneUsageDescription": "此应用需要使用麦克风进行语音输入功能", "NSUserNotificationsUsageDescription": "此应用需要推送通知权限以向您发送重要消息和提醒"}}, "plugins": {"push": {"unipush": {"version": "2", "offline": true, "icons": {"push": {"ldpi": "unpackage/res/push/large/48.png", "mdpi": "unpackage/res/push/large/64.png", "hdpi": "unpackage/res/push/large/96.png", "xhdpi": "unpackage/res/push/large/128.png", "xxhdpi": "unpackage/res/push/large/192.png"}, "small": {"ldpi": "unpackage/res/push/small/18.png", "mdpi": "unpackage/res/push/small/24.png", "hdpi": "unpackage/res/push/small/36.png", "xhdpi": "unpackage/res/push/small/48.png", "xxhdpi": "unpackage/res/push/small/72.png", "xxxhdpi": "unpackage/res/push/small/96.png"}}}}, "statics": {}, "speech": {"baidu": {"appid": "119382824", "apikey": "lG47VJBl43NJnOZq2Ozf6hWx", "secretkey": "i3kCw1NYd9q1REZBthH9WQVZJZYI4vLo"}}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "uniStatistics": {"enable": true, "version": "2", "reportInterval": "10", "uniCloud": {"provider": "<PERSON><PERSON><PERSON>", "spaceId": "mp-a7dd4948-ad0b-43b1-8471-b9c97dd12c84", "clientSecret": "bs5Sh9M9yL4nHQkyMKn4xA==", "endpoint": "https://api.next.bspapp.com"}}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#000000"}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#F8F9FA", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.75", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#7A7E83", "selectedColor": "black", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "50px", "list": [{"pagePath": "pages/tabBar/statistics/statistics", "iconPath": "/static/tabBar/chart-pie.png", "selectedIconPath": "/static/tabBar/chart-pie-active.png", "text": "统计"}, {"pagePath": "pages/tabBar/todo/todo", "iconPath": "/static/tabBar/todo.png", "selectedIconPath": "/static/tabBar/todo-active.png", "text": "待办"}, {"pagePath": "pages/tabBar/customer/customer", "iconPath": "/static/tabBar/customer.png", "selectedIconPath": "/static/tabBar/customer-active.png", "text": "客户"}, {"pagePath": "pages/tabBar/business/business", "iconPath": "/static/tabBar/business.png", "selectedIconPath": "/static/tabBar/business-active.png", "text": "商机"}, {"pagePath": "pages/tabBar/more/more", "iconPath": "/static/tabBar/more.png", "selectedIconPath": "/static/tabBar/more-active.png", "text": "更多"}], "backgroundColor": "#F8F9FA", "selectedIndex": 0, "shown": true}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false, "version": "2", "reportInterval": "10"}, "safearea": {"background": "#F8F9FA", "bottom": {"offset": "auto"}}}, "locale": "zh-Hans", "launch_path": "__uniappview.html"}