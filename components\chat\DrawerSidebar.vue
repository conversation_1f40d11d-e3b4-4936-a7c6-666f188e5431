<template>
  <uni-popup ref="drawerPopup" type="left" @maskClick="close" class="drawer-popup"
    background-color="#F8F7F6">
    <view class="drawer-container" :style="appSafeAreaStyle">
      <!-- 顶部区域 -->
      <view class="drawer-header">
        <!-- 对话标签 -->
        <view class="tab-item" @click="backToChat">
          <text class="tab-text">对话</text>
        </view>
        <!-- 搜索图标 -->
        <!-- <view class="search-icon" @click="handleSearch">
          <uni-icons type="search" size="24" color="#666"></uni-icons>
        </view> -->
      </view>

      <!-- 销售助理区域 -->
      <view class="assistant-section">
        <view class="assistant-item" :class="{
          active: !currentSessionId || currentSessionId === '',
          unread: assistantInfo.unread_num > 0
        }" @click="handleAssistantClick">
          <image class="assistant-avatar" src="/static/user/Ivy.png" mode="aspectFill"></image>
          <view class="assistant-info">
            <text class="assistant-name">{{ assistantInfo.name }}</text>
            <text class="assistant-message">{{ cleanMarkdownTextCleanText(assistantInfo.content) || '暂无消息' }}</text>
          </view>
          <!-- 未读红点 -->
          <view v-if="assistantInfo.unread_num > 0" class="unread-dot-ivy"></view>
        </view>
      </view>

      <!-- 商机标题 -->
      <view class="section-title">
        <text class="title-text">商机</text>
      </view>

      <!-- 商机列表区域 -->
      <view class="drawer-content">
        <!-- 有数据时显示滚动列表 -->
        <scroll-view v-if="opportunityList.length > 0" scroll-y class="opportunity-list" :enhanced="true">
          <!-- 商机列表容器 -->
          <view class="list-container">
            <view v-for="item in opportunityList" :key="item.session_id" class="list-item" :class="{
              unread: item.unread_num > 0,
              active: currentSessionId === item.session_id
            }" @click="handleItemClick(item)">
              <!-- 内容区域 -->
              <view class="item-content">
                <view class="item-title">{{ item.title }}</view>
                <view v-if="item.unread_num > 0" class="item-description">
                  {{ item.content || '暂无消息' }}
                </view>
              </view>
              <!-- 未读红点 - 移到右侧 -->
              <view v-if="item.unread_num > 0" class="unread-dot"></view>
            </view>
          </view>
        </scroll-view>
        <!-- 空状态 -->
        <view v-else class="empty-state">
          <text class="empty-text">暂无商机记录</text>
        </view>
      </view>

      <!-- 底部用户区域 -->
      <view class="drawer-footer">
        <view class="user-info" @click="handleUserInfo">
          <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
          <text class="user-name">{{ userInfo.name }}</text>
        </view>
        <view class="footer-actions">
          <view class="action-icon" @click="handleSettings">
            <image class="setting-icon" src="/static/global/is-setting.svg" mode="aspectFill"></image>
          </view>
          <!-- <view class="action-icon notification-icon" @click="handleNotifications">
            <uni-icons type="notification" size="18" color="#666"></uni-icons>
            <view v-if="hasUnreadMessages" class="notification-dot"></view>
          </view> -->
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { getSessionList, setSessionRead } from "@/http/ai-chat-task.js";
import { cleanMarkdownTextCleanText} from '@/utils/utils.js'

// 定义组件事件
const emit = defineEmits(["itemClick"]);

// 当前选中的会话ID
const currentSessionId = ref('');
// 组件引用
const drawerPopup = ref(null);

// 用户信息数据
const userInfo = reactive({
  name: 'User', // 默认值
  avatar: '/static/user/avatar.png' // 默认头像
});

// 用户信息缓存 Map，用于存储上次获取的用户信息
const userInfoCache = new Map();
// 商机列表缓存 Map，用于存储上次获取的商机列表数据
const opportunityListCache = new Map();

// 销售助理信息响应式数据
const assistantInfo = reactive({
  session_id: '',
  unread_num: 0,
  last_msg_time: null,
  content: '',
  name: ''
});

// 商机列表数据
const opportunityList = reactive([]);

// 计算属性：判断是否有未读消息
const hasUnreadMessages = computed(() => {
  return assistantInfo.unread_num > 0 || opportunityList.some(item => item.unread_num > 0);
});

// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`
  };
  // #endif
  return {};
});

/**
 * 比较两个商机列表是否相同
 * @param {Array} list1 - 第一个列表
 * @param {Array} list2 - 第二个列表
 * @returns {Boolean} - 是否相同
 */
const isOpportunityListEqual = (list1, list2) => {
  if (!Array.isArray(list1) || !Array.isArray(list2)) {
    return false;
  }
  if (list1.length !== list2.length) {
    return false;
  }
  // 按 session_id 排序后比较
  const sortedList1 = [...list1].sort((a, b) => (a.session_id || '').localeCompare(b.session_id || ''));
  const sortedList2 = [...list2].sort((a, b) => (a.session_id || '').localeCompare(b.session_id || ''));
  return sortedList1.every((item1, index) => {
    const item2 = sortedList2[index];
    return item1.session_id === item2.session_id &&
           item1.unread_num === item2.unread_num &&
           item1.last_msg_time === item2.last_msg_time &&
           item1.bo_id === item2.bo_id &&
           item1.title === item2.title &&
           item1.content === item2.content;
  });
};

/**
 * 更新商机列表数据（带缓存机制）
 * @param {Array} newList - 新的商机列表数据
 */
const updateOpportunityList = (newList) => {
  if (!Array.isArray(newList)) {
    console.log('新商机列表数据无效，跳过更新');
    return;
  }
  // 格式化新数据
  const formattedNewList = newList.map(item => ({
    session_id: item.session_id,
    unread_num: item.unread_num || 0,
    last_msg_time: item.last_msg_time,
    bo_id: item.bo_id,
    title: item.title,
    content: item.content || '暂无消息内容',
  }));
  // 生成缓存键，基于列表长度和关键字段的哈希
  const cacheKey = `list_${formattedNewList.length}_${JSON.stringify(formattedNewList.map(item => ({
    id: item.session_id,
    unread: item.unread_num,
    time: item.last_msg_time,
    title: item.title
  })))}`;
  // 检查缓存中是否已存在相同的数据
  if (opportunityListCache.has(cacheKey)) {
    console.log('商机列表数据未变化，使用缓存数据');
    return;
  }
  // 比较当前列表和新列表是否相同
  if (isOpportunityListEqual(opportunityList, formattedNewList)) {
    console.log('商机列表内容未变化，跳过更新');
    // 更新缓存键但不更新列表
    opportunityListCache.clear();
    opportunityListCache.set(cacheKey, {
      data: [...formattedNewList],
      timestamp: Date.now()
    });
    return;
  }
  // 清空现有数据
  opportunityList.splice(0, opportunityList.length);
  // 添加新数据
  formattedNewList.forEach(item => {
    opportunityList.push(item);
  });
  // 更新缓存
  opportunityListCache.clear();
  opportunityListCache.set(cacheKey, {
    data: [...formattedNewList],
    timestamp: Date.now()
  });
  console.log('商机列表已更新并缓存:', formattedNewList.length, '条记录');
};

/**
 * 获取会话列表数据
 */
const fetchSessionList = async () => {
  try {
    const params = {
      page:1,
      limit: 500,
    };
    const response = await getSessionList(params);
    // if (response.code === 0 && response.data) {
    if (response.code === 0) {
      // 更新销售助理信息
      if (response.data.main_session) {
        Object.assign(assistantInfo, {
          ...response.data.main_session,
          name: '销售助理 Ivy',
        });
      }
      // 更新商机列表
      if (response.data.list && Array.isArray(response.data.list)) {
        // 使用缓存机制更新商机列表
        updateOpportunityList(response.data.list);
      }
      console.log('会话列表加载成功:', response.data);
    } else {
      console.error('获取会话列表失败:', response.msg || '未知错误');
      uni.showToast({
        title: response.msg || '获取会话列表失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('获取会话列表异常:', error);
    uni.showToast({
      title: '网络异常，请稍后重试111',
      icon: 'none'
    });
  } finally {}
};

/**
 * 设置会话为已读
 */
const markSessionAsRead = async (sessionId) => {
  try {
    const response = await setSessionRead({ session_id: sessionId });
    if (response.code === 0) {
      // console.log('会话已标记为已读:', sessionId);
      // 更新本地缓存中的未读状态
      const targetItem = opportunityList.find(item => item.session_id === sessionId);
      if (targetItem && targetItem.unread_num > 0) {
        targetItem.unread_num = 0;
        // 清空缓存，强制下次重新比较数据
        opportunityListCache.clear();
        console.log('本地缓存已更新，会话已标记为已读:', sessionId);
      }
    } else {
      console.error('标记已读失败:', response.msg || '未知错误');
    }
  } catch (error) {
    console.error('标记已读异常:', error);
  }
};

/**
 * 从本地存储获取用户信息（带缓存机制）
 */
const getUserInfoFromStorage = () => {
  try {
    // 获取本地存储的用户信息，如果没有则返回空对象
    const storedUserInfo = uni.getStorageSync('userInfo') || {};
    // 生成缓存键，基于用户信息的关键字段
    const cacheKey = `${storedUserInfo.name || ''}_${storedUserInfo.portrait || ''}`;
    // 检查缓存中是否已存在相同的用户信息
    if (userInfoCache.has(cacheKey)) {
      // console.log('用户信息未变化，使用缓存数据');
      return;
    }
    // 使用解构赋值更新用户信息
    const { name, portrait } = storedUserInfo;
    let hasChanged = false;
    // 更新用户名（仅在有变化时更新）
    if (name && userInfo.name !== name) {
      userInfo.name = name;
      hasChanged = true;
    }
    // 更新用户头像（仅在有变化时更新）
    if (portrait && userInfo.avatar !== portrait) {
      userInfo.avatar = portrait;
      hasChanged = true;
    }
    // 如果有变化，更新缓存
    if (hasChanged) {
      // 清空旧缓存
      userInfoCache.clear();
      // 设置新缓存
      userInfoCache.set(cacheKey, {
        name: userInfo.name,
        avatar: userInfo.avatar,
        timestamp: Date.now()
      });
      // console.log('用户信息已更新并缓存:', { name: userInfo.name, avatar: userInfo.avatar });
    } else {
      console.log('用户信息无变化，跳过更新');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error.message || error);
  }
};

/**
 * 打开抽屉
 */
const open = () => {
  if (drawerPopup.value) {
    // 每次打开抽屉时检查用户信息和加载会话列表
    getUserInfoFromStorage();
    fetchSessionList();
    drawerPopup.value.open();
  }
};

/**
 * 关闭抽屉
 */
const close = () => {
  if (drawerPopup.value) {
    drawerPopup.value.close();
  }
};

/**
 * 返回对话页面 - 组件内部处理，直接关闭抽屉
 */
const backToChat = () => {
  close();
};

/**
 * 处理列表项点击
 * @param {Object} item - 商机对象
 */
const handleItemClick = (item) => {
  // console.log('处理列表项点击', item);
  // 设置当前选中的会话ID
  currentSessionId.value = item.session_id;
  // 发出点击事件，传递会话信息
  emit("itemClick", item);
  // 关闭抽屉
  close();
};

// /**
//  * 处理搜索事件 - 组件内部处理
//  */
// const handleSearch = () => {
//   // 这里可以添加搜索逻辑
//   console.log("执行搜索功能");
// };

/**
 * 处理设置事件 - 跳转到个人中心页面
 */
const handleUserInfo = () => {
  uni.switchTab({
    url: "/pages/tabBar/more/more",
    success: () => {
      console.log("跳转到个人中心成功");
      close(); // 跳转成功后关闭抽屉
    },
    fail: (err) => {
      console.error("跳转到待办页面失败:", err);
    },
  });
};

/**
 * 处理设置事件 - 跳转到待办页面
 */
const handleSettings = () => {
  uni.switchTab({
    url: "/pages/tabBar/more/more",
    success: () => {
      console.log("跳转到待办页面成功");
      close(); // 跳转成功后关闭抽屉
    },
    fail: (err) => {
      console.error("跳转到待办页面失败:", err);
    },
  });
};

/**
 * 处理销售助理点击
 */
const handleAssistantClick = () => {
  // 清空当前选中的会话ID，回到销售助理对话
  currentSessionId.value = '';
  // 后端触发标记为已读
  // 如果是未读状态，标记为已读
  // if (assistantInfo.unread_num > 0) {
  //   assistantInfo.unread_num = 0;
  //   // 调用接口标记为已读
  //   markSessionAsRead(assistantInfo.session_id);
  // }
  // 发出点击事件，传递销售助理信息
  emit("itemClick", {
    ...assistantInfo,
    session_id: '',
  });
  // 关闭抽屉
  close();
};

/**
 * 设置当前会话ID
 * @param {String} sessionId - 会话ID
 */
const setCurrentSession = (sessionId) => {
  currentSessionId.value = sessionId;
};

// 暴露方法给父组件
defineExpose({
  open,
  close,
  setCurrentSession,
  markSessionAsRead,
  hasUnreadMessages,
});

// 组件挂载时获取用户信息和会话列表
onMounted(() => {
  getUserInfoFromStorage();
  fetchSessionList();
});
</script>

<style scoped lang="scss">
.drawer-popup {
  z-index: 9999;

  .drawer-container {
    display: flex;
    flex-direction: column;
    width: 286px; // 默认移动端和H5宽度
    // #ifdef H5
    height: 100vh;
    // #endif
    // #ifdef APP-PLUS
    height: calc(100vh - 46px);
    background: #F8F7F6;
    // #endif

    .drawer-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 12px 12px 0px 12px;

      .tab-item {
        display: flex;
        align-items: center;
        padding: 0 0 12px 0;
        border-radius: 8px;
        cursor: pointer;

        .tab-text {
          color: var(---, #1a1a1a);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }

      .search-icon {
        padding: 8px;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        // #ifdef H5
        &:hover {
          background-color: #f5f5f5;
        }
        // #endif
      }
    }

    .assistant-section {
      margin: 0 10px;
      // border-bottom: 1px solid #eeeeee;

      .assistant-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 10px;
        border: 1px solid var(---normal, #E2E4E9);
        cursor: pointer;
        transition: background-color 0.2s ease;

        // #ifdef H5
        &:hover {
          background-color: #eeedec;
        }
        // #endif

        &.active {
          background-color: #EEEDEC;
        }

        &.unread {
          .assistant-name {
            font-weight: 600;
          }
        }

        .unread-dot-ivy {
          width: 7px;
          height: 7px;
          background: var(---, #FA5151);
          border-radius: 50%;
          margin-top:6px;
        }

        .assistant-avatar {
          width: 28px;
          height: 28px;
          border-radius: 10px;
          margin-right: 6px;
          flex-shrink: 0;
        }

        .assistant-info {
          flex: 1;
          min-width: 0;
          font-size: 12px;
          /* 确保 flex 子元素能够收缩 */

          .assistant-name {
            color: var(---, #1A1A1A);
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            display: block;
          }

          .assistant-message {
            color: var(---, #787D86);
            font-family: "PingFang SC";
            font-style: normal;
            font-weight: 300;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            display: block;
          }
        }
      }
    }

    .section-title {
      padding: 20px 12px 10px 12px;
      .title-text {
        color: var(---, #787D86);
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
    }

    .drawer-content {
      flex: 1;
      overflow: hidden;
      display: flex;

      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 53px;
        margin: 0px 8px;
        border-radius: 10px;
        border: 1px dashed var(---normal, #E2E4E9);
        width: 100%;

        .empty-text {
          font-size: 14px;
          color: #999999;
          text-align: center;
        }
      }

      .opportunity-list {

        // 自定义滚动条样式
        :deep(.uni-scroll-view) {

          // #ifdef H5
          &::-webkit-scrollbar {
            width: 4px;
            background: transparent;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 2px;
            transition: background-color 0.2s;

            &:hover {
              background-color: rgba(0, 0, 0, 0.2);
            }
          }

          &::-webkit-scrollbar-corner {
            background: transparent;
          }

          // #endif
        }

        .list-container {
          /* 列表容器不设置固定高度，让内容自然撑开 */
          min-height: 100%;
          /* 确保至少占满父容器 */
          display: flex;
          flex-direction: column;
        }

        .list-item {
          display: flex;
          align-items: center;
          cursor: pointer;
          transition: background-color 0.2s ease;
          margin: 5px 12px 5px 12px;
          padding: 8px;
          border-radius: 10px;

          // #ifdef H5
          &:hover {
            background-color: #eeedec;
          }
          // #endif

          .item-content {
            flex: 1;
            min-width: 0;
            /* 确保 flex 子元素能够收缩 */

            .item-title {
              color: var(---, #1A1A1A);
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              margin-bottom: 1px;
            }

            .item-description {
              color: var(---, #787D86);
              font-family: "PingFang SC";
              font-size: 12px;
              font-style: normal;
              font-weight: 300;
              line-height: normal;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              transition: opacity 0.3s ease;
            }
          }

          .unread-dot {
            width: 7px;
            height: 7px;
            background: var(---, #FA5151);
            border-radius: 50%;
            margin-top:-16px;
          }

          &.unread {
            .item-title {
              font-weight: 500;
            }
          }

          &.active {
            background-color: #EEEDEC;
          }
        }
      }
    }

    .drawer-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 14px 33px 14px;
      border-top: 1px solid var(---normal, #E2E4E9);

      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 8px;
          border: 1px solid var(---normal, #E2E4E9);
        }

        .user-name {
          color: var(---, #1A1A1A);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }

      .footer-actions {
        display: flex;
        align-items: center;

        .action-icon {
          cursor: pointer;
          .setting-icon{
            width: 26px;
            height: 26px;
            background-size: 100% 100%;
          }
        }

        .notification-icon {
          position: relative;
        }

        .notification-dot {
          position: absolute;
          top: 2px;
          right: 2px;
          width: 8px;
          height: 8px;
          background: var(---, #FA5151);
          border-radius: 50%;
          border: 1px solid #ffffff;
        }
      }
    }
  }
}
</style>
