{"version": 3, "file": "formatTime.Ctr0wG6D.js", "sources": ["../../../../../utils/utils.js", "../../../../../utils/formatTime.js"], "sourcesContent": null, "names": ["copyMessage", "content", "cleanContent", "replace", "trim", "cleanMarkdownText", "setClipboardData", "data", "success", "showToast", "title", "icon", "fail", "detectBrowser", "userAgent", "navigator", "indexOf", "detectDeviceType", "screenWidth", "window", "screen", "width", "screenHeight", "height", "maxScreenSize", "Math", "max", "test", "maxTouchPoints", "isMobile", "isPC", "formatLastContactTime", "timeString", "today", "Date", "lastContactDate", "setHours", "isNaN", "getTime", "console", "warn", "contactDay", "diffTime", "diffDays", "floor", "error", "filterDateTime", "dateTime", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "getSeconds", "getCurrentTime", "now", "toString", "getCurrentDay", "getDay", "getNotifyCycle", "notifyCycle", "dueDate", "cycleMap", "timeOnly", "getTimeOnly", "includes", "weekday", "getOverdueText", "due", "ceil"], "mappings": "gDA6BO,MAwCMA,EAAeC,IAEpB,MAAAC,EA1CyB,CAACD,GAC3BA,GAA8B,iBAAZA,EAGhBA,EAEJE,QAAQ,kBAAmB,IAE3BA,QAAQ,aAAc,MAEtBA,QAAQ,sCAAuC,WAC/CA,QAAQ,yBAA0B,MAElCA,QAAQ,0BAA2B,MAEnCA,QAAQ,mBAAoB,MAC5BA,QAAQ,eAAgB,MACxBA,QAAQ,eAAgB,MACxBA,QAAQ,aAAc,MAEtBA,QAAQ,eAAgB,MAExBA,QAAQ,eAAgB,IAExBA,QAAQ,UAAW,IAEnBA,QAAQ,mBAAoB,IAC5BA,QAAQ,mBAAoB,IAE5BA,QAAQ,gBAAiB,IAEzBA,QAAQ,eAAgB,MACxBA,QAAQ,WAAY,IAEpBA,QAAQ,UAAW,QAEnBC,OAlCM,GAwCYC,CAAkBJ,GAClBK,EAAA,CACnBC,KAAML,EACNM,QAAS,KACOC,EAAA,CACZC,MAAO,OACPC,KAAM,WACP,EAEHC,KAAM,KACUH,EAAA,CACZC,MAAO,OACPC,KAAM,QACP,GAEJ,EAeI,SAASE,IACd,MAAMC,EAAYC,UAAUD,UAC5B,WAAIA,EAAUE,QAAQ,YAAyD,IAAtCF,EAAUE,QAAQ,YAAqD,IAAjCF,EAAUE,QAAQ,OACxF,iBACuC,IAArCF,EAAUE,QAAQ,WACpB,mBACwC,IAAtCF,EAAUE,QAAQ,YAA0D,IAAtCF,EAAUE,QAAQ,YAAsD,IAAlCF,EAAUE,QAAQ,QAChG,gBACmC,IAAjCF,EAAUE,QAAQ,OACpB,iBAEA,iBAEX,CAKO,SAASC,IAEd,MAAMH,EAAYC,UAAUD,UAMtBI,EAAcC,OAAOC,OAAOC,MAC5BC,EAAeH,OAAOC,OAAOG,OAC7BC,EAAgBC,KAAKC,IAAIR,EAAaI,GAExC,MARgB,qFAQJK,KAAKb,IAND,wEAUJa,KAAKb,IAIjBU,GAAiB,IAPZ,SAWL,iBAAkBL,QAAUJ,UAAUa,eAAiB,EAErDJ,EAAgB,KACX,KAEA,SAGJ,IAET,CAMO,SAASK,IACd,MAA8B,WAAvBZ,GACT,CAMO,SAASa,IACd,MAA8B,OAAvBb,GACT,CCpKO,SAASc,EAAsBC,GACpC,IAAKA,EACI,MAAA,IAGL,IAEI,MAAAC,MAAYC,KAId,IAAAC,EACA,GAJJF,EAAMG,SAAS,EAAG,EAAG,EAAG,GAIE,iBAAfJ,EACSG,EAAA,IAAID,KAAKF,OACjC,IAAqC,iBAAfA,EAIT,MAAA,IAFPG,EAAkB,IAAID,KAAKF,EAAW7B,QAAQ,KAAM,KAGrD,CAGD,GAAIkC,MAAMF,EAAgBG,WAEjB,OADCC,QAAAC,KAAK,WAAYR,GAClB,IAIH,MAAAS,EAAa,IAAIP,KAAKC,GAC5BM,EAAWL,SAAS,EAAG,EAAG,EAAG,GAG7B,MAAMM,EAAWT,EAAMK,UAAYG,EAAWH,UAG9C,GAAII,EAAW,EACN,MAAA,IAIT,MAAMC,EAAWlB,KAAKmB,MAAMF,SAG5B,GAAiB,IAAbC,EACK,MAAA,OACb,GAA4B,IAAbA,EACF,MAAA,OACE,GAAAA,EAAW,GAAKA,GAAY,EACrC,MAAO,GAAGA,QACD,GAAAA,EAAW,GAAKA,GAAY,GAAI,CAEzC,MAAO,GADOlB,KAAKmB,MAAMD,EAAW,QAErC,CAAU,GAAAA,EAAW,IAAMA,GAAY,IAAK,CAE3C,MAAO,GADQlB,KAAKmB,MAAMD,EAAW,UAE3C,CAEM,MAAO,GADOlB,KAAKmB,MAAMD,EAAW,UAMvC,OAHQE,GAEA,OADCN,QAAAM,MAAM,aAAcA,EAAOb,GAC5B,GACR,CACH,CA+BO,SAASc,EAAeC,GAC7B,IAAKA,EACI,MAAA,IAEH,MAAAC,EAAO,IAAId,KAAKa,GACtB,GAAIV,MAAMW,EAAKV,WACN,MAAA,IAEH,MAAAW,EAAOD,EAAKE,cACZC,EAAQC,OAAOJ,EAAKK,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOJ,EAAKQ,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOJ,EAAKU,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOJ,EAAKY,cAAcN,SAAS,EAAG,KAElD,OAAoB,IAApBN,EAAKU,YAA0C,IAAtBV,EAAKY,cAA4C,IAAtBZ,EAAKa,aACpD,GAAGZ,KAAQE,KAASI,IAEtB,GAAGN,KAAQE,KAASI,KAAOE,KAASE,GAC7C,CAqBY,MAACG,EAAiB,KACtB,MAAAC,MAAU7B,KAChB,MAAO,GAAG6B,EAAIL,WAAWM,WAAWV,SAAS,EAAG,QAAQS,EAAIH,aAAaI,WAAWV,SAAS,EAAG,MAAI,EAIzFW,EAAgB,IAEV,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,YAD5C/B,MAEIgC,UASTC,EAAiB,CAACC,EAAaC,KAE1C,MAAMC,EAAW,CACf,EAAG,IACH,EAAG,KACH,EAAG,MACH,EAAG,KACH,EAAG,KACH,EAAG,MACH,EAAG,KACH,EAAG,OACH,EAAG,OACH,EAAG,MAIL,IAAKD,EACI,OAAAC,EAASF,IAAgB,IAG9B,IACI,MAAApB,EAAO,IAAId,KAAKmC,GACtB,GAAIhC,MAAMW,EAAKV,WACN,OAAAgC,EAASF,IAAgB,IAG5B,MAAAG,EA1DH,SAAqBxB,GACtB,GAAa,OAAbA,GAAkC,KAAbA,EACd,MAAA,IAEL,MAAAC,EAAO,IAAId,KAAKa,GACtB,GAAIV,MAAMW,EAAKV,WACJ,MAAA,IAEL,MAAAmB,EAAQT,EAAKU,WACbC,EAAUX,EAAKY,aACrB,MAAO,GAAGR,OAAOK,GAAOH,SAAS,EAAG,QAAQF,OAAOO,GAASL,SAAS,EAAG,MAC1E,CA+CqBkB,CAAYH,GAG7B,GAAI,CAAC,EAAG,EAAG,GAAGI,SAASL,GACrB,MAAO,GAAGE,EAASF,MAAgBG,IAIrC,GAAI,CAAC,EAAG,GAAGE,SAASL,GAAc,CAC1B,MACAM,EADW,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvB1B,EAAKkB,UAC9B,MAAO,GAAGI,EAASF,KAAeM,KAAWH,GAC9C,CAGD,GAAI,CAAC,EAAG,EAAG,GAAGE,SAASL,GAAc,CAC7B,MAAAb,EAAMP,EAAKQ,UACjB,MAAO,GAAGc,EAASF,KAAeb,MAAQgB,GAC3C,CAGD,GAAoB,IAAhBH,EAAmB,CACf,MAAAjB,EAAQH,EAAKK,WAAa,EAC1BE,EAAMP,EAAKQ,UACV,MAAA,GAAGc,EAASF,KAAejB,KAASI,MAAQgB,GACpD,CAGM,OAAAD,EAASF,IAAgB,GAKjC,OAHQvB,GAEA,OADCN,QAAAM,MAAM,YAAaA,GACpByB,EAASF,IAAgB,GACjC,GAgBI,SAASO,EAAeN,GAC7B,IAAKA,EACI,MAAA,GAGL,IAEF,MAAMO,EAAM,IAAI1C,KAAKmC,EAAQlE,QAAQ,KAAM,MAG3C,GAAIkC,MAAMuC,EAAItC,WACL,MAAA,GAIH,MAGAI,OAHUR,MAGKI,UAAYsC,EAAItC,UAGrC,GAAII,GAAY,EACP,MAAA,GAIT,MAAO,KADajB,KAAKoD,KAAKnC,WAK/B,OAHQG,GAEA,OADCN,QAAAM,MAAM,YAAaA,GACpB,EACR,CACH"}