// 引用网络请求中间件
import request from '@/utils/request';

// 查询待办事项接口
export function getTodoList(data) {
	return request({
		url: '/api/todo/list',
		method: 'POST',
		data
	})
}

// 新增待办事项接口
export function addTodoItem(data) {
	return request({
		url: '/api/todo/save',
		method: 'POST',
		data
	})
}

// 删除待办事项接口
export function deleteTodoItem(data) {
	return request({
		url: '/api/todo/del',
		method: 'POST',
		data
	})
}

// 修改待办事项接口
export function updateTodoItem(data) {
	return request({
		url: '/api/todo/save',
		method: 'POST',
		data
	})
}

// 查看待办事项详情接口
export function getTodoDetail(data) {
	return request({
		url: '/api/todo/detail',
		method: 'GET',
		data
	})
}

// 待办排序
export function sortTodos(data) {
	return request({
	  url: '/api/todo/sort',
	  method: 'POST',
	  data
	})
  }
