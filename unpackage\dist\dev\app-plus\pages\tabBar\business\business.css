/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-d31e1c47]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-d31e1c47]:before {
  content: "\e6c4";
}
.uniui-color[data-v-d31e1c47]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-d31e1c47]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-d31e1c47]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-d31e1c47]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-d31e1c47]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-d31e1c47]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-d31e1c47]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-d31e1c47]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-d31e1c47]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-d31e1c47]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-d31e1c47]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-d31e1c47]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-d31e1c47]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-d31e1c47]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-d31e1c47]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-d31e1c47]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-d31e1c47]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-d31e1c47]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-d31e1c47]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-d31e1c47]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-d31e1c47]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-d31e1c47]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-d31e1c47]:before {
  content: "\e6d4";
}
.uniui-back[data-v-d31e1c47]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-d31e1c47]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-d31e1c47]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-d31e1c47]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-d31e1c47]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-d31e1c47]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-d31e1c47]:before {
  content: "\e6d1";
}
.uniui-down[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-d31e1c47]:before {
  content: "\e6d5";
}
.uniui-right[data-v-d31e1c47]:before {
  content: "\e6b5";
}
.uniui-up[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-top[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-left[data-v-d31e1c47]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-d31e1c47]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-d31e1c47]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-d31e1c47]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-d31e1c47]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-d31e1c47]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-d31e1c47]:before {
  content: "\e649";
}
.uniui-reload[data-v-d31e1c47]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-d31e1c47]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-d31e1c47]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-d31e1c47]:before {
  content: "\e6ad";
}
.uniui-location[data-v-d31e1c47]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-d31e1c47]:before {
  content: "\e683";
}
.uniui-star[data-v-d31e1c47]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-d31e1c47]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-d31e1c47]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-d31e1c47]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-d31e1c47]:before {
  content: "\e6a2";
}
.uniui-font[data-v-d31e1c47]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-d31e1c47]:before {
  content: "\e6a4";
}
.uniui-link[data-v-d31e1c47]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-d31e1c47]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-d31e1c47]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-d31e1c47]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-d31e1c47]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-d31e1c47]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-d31e1c47]:before {
  content: "\e6ab";
}
.uniui-person[data-v-d31e1c47]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-d31e1c47]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-d31e1c47]:before {
  content: "\e69b";
}
.uniui-phone[data-v-d31e1c47]:before {
  content: "\e69c";
}
.uniui-email[data-v-d31e1c47]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-d31e1c47]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-d31e1c47]:before {
  content: "\e692";
}
.uniui-contact[data-v-d31e1c47]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-d31e1c47]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-d31e1c47]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-d31e1c47]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-d31e1c47]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-d31e1c47]:before {
  content: "\e68e";
}
.uniui-upload[data-v-d31e1c47]:before {
  content: "\e690";
}
.uniui-weixin[data-v-d31e1c47]:before {
  content: "\e691";
}
.uniui-compose[data-v-d31e1c47]:before {
  content: "\e67f";
}
.uniui-qq[data-v-d31e1c47]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-d31e1c47]:before {
  content: "\e681";
}
.uniui-pyq[data-v-d31e1c47]:before {
  content: "\e682";
}
.uniui-sound[data-v-d31e1c47]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-d31e1c47]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-d31e1c47]:before {
  content: "\e686";
}
.uniui-trash[data-v-d31e1c47]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-d31e1c47]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-d31e1c47]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-d31e1c47]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-d31e1c47]:before {
  content: "\e68c";
}
.uniui-download[data-v-d31e1c47]:before {
  content: "\e68d";
}
.uniui-help[data-v-d31e1c47]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-d31e1c47]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-d31e1c47]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-d31e1c47]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-d31e1c47]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-d31e1c47]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-d31e1c47]:before {
  content: "\e66c";
}
.uniui-clear[data-v-d31e1c47]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-d31e1c47]:before {
  content: "\e66e";
}
.uniui-minus[data-v-d31e1c47]:before {
  content: "\e66f";
}
.uniui-image[data-v-d31e1c47]:before {
  content: "\e670";
}
.uniui-mic[data-v-d31e1c47]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-d31e1c47]:before {
  content: "\e672";
}
.uniui-close[data-v-d31e1c47]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-d31e1c47]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-d31e1c47]:before {
  content: "\e675";
}
.uniui-plus[data-v-d31e1c47]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-d31e1c47]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-d31e1c47]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-d31e1c47]:before {
  content: "\e668";
}
.uniui-info[data-v-d31e1c47]:before {
  content: "\e669";
}
.uniui-locked[data-v-d31e1c47]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-d31e1c47]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-d31e1c47]:before {
  content: "\e659";
}
.uniui-camera[data-v-d31e1c47]:before {
  content: "\e65a";
}
.uniui-circle[data-v-d31e1c47]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-d31e1c47]:before {
  content: "\e65c";
}
.uniui-chat[data-v-d31e1c47]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-d31e1c47]:before {
  content: "\e65e";
}
.uniui-flag[data-v-d31e1c47]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-d31e1c47]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-d31e1c47]:before {
  content: "\e661";
}
.uniui-home[data-v-d31e1c47]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-d31e1c47]:before {
  content: "\e663";
}
.uniui-gear[data-v-d31e1c47]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-d31e1c47]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-d31e1c47]:before {
  content: "\e666";
}
.uniui-map[data-v-d31e1c47]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-d31e1c47]:before {
  content: "\e656";
}
.uniui-refresh[data-v-d31e1c47]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-d31e1c47]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-d31e1c47]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-d31e1c47]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-d31e1c47]:before {
  content: "\e648";
}
.uniui-redo[data-v-d31e1c47]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-d31e1c47]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-d31e1c47]:before {
  content: "\e64c";
}
.uniui-more[data-v-d31e1c47]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-d31e1c47]:before {
  content: "\e64e";
}
.uniui-undo[data-v-d31e1c47]:before {
  content: "\e64f";
}
.uniui-images[data-v-d31e1c47]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-d31e1c47]:before {
  content: "\e652";
}
.uniui-settings[data-v-d31e1c47]:before {
  content: "\e653";
}
.uniui-search[data-v-d31e1c47]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-d31e1c47]:before {
  content: "\e655";
}
.uniui-list[data-v-d31e1c47]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-d31e1c47]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-d31e1c47]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-d31e1c47]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-d31e1c47]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-d31e1c47]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-d31e1c47]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-d31e1c47]:before {
  content: "\e643";
}
.uniui-heart[data-v-d31e1c47]:before {
  content: "\e639";
}
.uniui-loop[data-v-d31e1c47]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-d31e1c47]:before {
  content: "\e632";
}
.uniui-scan[data-v-d31e1c47]:before {
  content: "\e62a";
}
.uniui-bars[data-v-d31e1c47]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-d31e1c47]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-d31e1c47]:before {
  content: "\e62c";
}
.uniui-shop[data-v-d31e1c47]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-d31e1c47]:before {
  content: "\e630";
}
.uniui-cart[data-v-d31e1c47]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-d31e1c47] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}

.zp-container[data-v-b7999e14]{

		display: flex;

		align-items: center;
		justify-content: center;
}
.zp-container-fixed[data-v-b7999e14] {

		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
}
.zp-main[data-v-b7999e14]{

		display: flex;

		flex-direction: column;
		align-items: center;
        padding: 1.5625rem 0;
}
.zp-main-image-rpx[data-v-b7999e14] {
		width: 7.5rem;
		height: 7.5rem;
}
.zp-main-image-px[data-v-b7999e14] {
		width: 120px;
		height: 120px;
}
.zp-main-title[data-v-b7999e14] {
		color: #aaaaaa;
		text-align: center;
}
.zp-main-title-rpx[data-v-b7999e14] {
		font-size: 0.875rem;
		margin-top: 0.3125rem;
		padding: 0 0.625rem;
}
.zp-main-title-px[data-v-b7999e14] {
		font-size: 14px;
		margin-top: 5px;
		padding: 0px 10px;
}
.zp-main-error-btn[data-v-b7999e14] {
		border: solid 1px #dddddd;
		color: #aaaaaa;
}
.zp-main-error-btn-rpx[data-v-b7999e14] {
		font-size: 0.875rem;
		padding: 0.25rem 0.75rem;
		border-radius: 0.1875rem;
		margin-top: 1.5625rem;
}
.zp-main-error-btn-px[data-v-b7999e14] {
		font-size: 14px;
		padding: 4px 12px;
		border-radius: 3px;
		margin-top: 25px;
}

/* [z-paging]公用的静态css资源 */
.zp-line-loading-image[data-v-00a16504] {

	animation: loading-flower-00a16504 1s steps(12) infinite;

	color: #666666;
}
.zp-line-loading-image-rpx[data-v-00a16504] {
	margin-right: 0.25rem;
	width: 1.0625rem;
	height: 1.0625rem;
}
.zp-line-loading-image-px[data-v-00a16504] {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}
.zp-loading-image-ios-rpx[data-v-00a16504] {
	width: 1.25rem;
	height: 1.25rem;
}
.zp-loading-image-ios-px[data-v-00a16504] {
	width: 20px;
	height: 20px;
}
.zp-loading-image-android-rpx[data-v-00a16504] {
	width: 1.0625rem;
	height: 1.0625rem;
}
.zp-loading-image-android-px[data-v-00a16504] {
	width: 17px;
	height: 17px;
}
@keyframes loading-flower-00a16504 {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}
.zp-r-container[data-v-00a16504] {

		display: flex;
		height: 100%;

		flex-direction: row;
		justify-content: center;
		align-items: center;
}
.zp-r-container-padding[data-v-00a16504] {
}
.zp-r-left[data-v-00a16504] {

		display: flex;

		flex-direction: row;
		align-items: center;
		overflow: hidden;
}
.zp-r-left-image[data-v-00a16504] {
		transition-duration: .2s;
		transition-property: transform;
		color: #666666;
}
.zp-r-left-image-pre-size-rpx[data-v-00a16504] {

		width: 1.0625rem;
		height: 1.0625rem;
		overflow: hidden;
}
.zp-r-left-image-pre-size-px[data-v-00a16504] {

		width: 17px;
		height: 17px;
		overflow: hidden;
}
.zp-r-arrow-top[data-v-00a16504] {
		transform: rotate(0deg);
}
.zp-r-arrow-down[data-v-00a16504] {
		transform: rotate(180deg);
}
.zp-r-right[data-v-00a16504] {

		display: flex;

		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.zp-r-right-time-text-rpx[data-v-00a16504] {
		margin-top: 0.3125rem;
		font-size: 0.8125rem;
}
.zp-r-right-time-text-px[data-v-00a16504] {
		margin-top: 5px;
		font-size: 13px;
}

/* [z-paging]公用的静态css资源 */
.zp-line-loading-image[data-v-8cc5c400] {

	animation: loading-flower-8cc5c400 1s steps(12) infinite;

	color: #666666;
}
.zp-line-loading-image-rpx[data-v-8cc5c400] {
	margin-right: 0.25rem;
	width: 1.0625rem;
	height: 1.0625rem;
}
.zp-line-loading-image-px[data-v-8cc5c400] {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}
.zp-loading-image-ios-rpx[data-v-8cc5c400] {
	width: 1.25rem;
	height: 1.25rem;
}
.zp-loading-image-ios-px[data-v-8cc5c400] {
	width: 20px;
	height: 20px;
}
.zp-loading-image-android-rpx[data-v-8cc5c400] {
	width: 1.0625rem;
	height: 1.0625rem;
}
.zp-loading-image-android-px[data-v-8cc5c400] {
	width: 17px;
	height: 17px;
}
@keyframes loading-flower-8cc5c400 {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}
.zp-l-container[data-v-8cc5c400] {

		clear: both;
		display: flex;

		flex-direction: row;
		align-items: center;
		justify-content: center;
}
.zp-l-container-rpx[data-v-8cc5c400] {
		height: 2.5rem;
		font-size: 0.84375rem;
}
.zp-l-container-px[data-v-8cc5c400] {
		height: 40px;
		font-size: 14px;
}
.zp-l-line-loading-custom-image[data-v-8cc5c400] {
		color: #a4a4a4;
}
.zp-l-line-loading-custom-image-rpx[data-v-8cc5c400] {
		margin-right: 0.25rem;
		width: 0.875rem;
		height: 0.875rem;
}
.zp-l-line-loading-custom-image-px[data-v-8cc5c400] {
		margin-right: 4px;
		width: 14px;
		height: 14px;
}
.zp-l-line-loading-custom-image-animated[data-v-8cc5c400]{

		animation: loading-circle-8cc5c400 1s linear infinite;
}
.zp-l-circle-loading-view[data-v-8cc5c400] {
		border: 0.09375rem solid #dddddd;
		border-radius: 50%;

		animation: loading-circle-8cc5c400 1s linear infinite;
}
.zp-l-circle-loading-view-rpx[data-v-8cc5c400] {
		margin-right: 0.25rem;
		width: 0.71875rem;
		height: 0.71875rem;
}
.zp-l-circle-loading-view-px[data-v-8cc5c400] {
		margin-right: 4px;
		width: 12px;
		height: 12px;
}
.zp-l-text-rpx[data-v-8cc5c400] {
		font-size: 0.9375rem;
		margin: 0 0.1875rem;
}
.zp-l-text-px[data-v-8cc5c400] {
		font-size: 15px;
		margin: 0px 3px;
}
.zp-l-line-rpx[data-v-8cc5c400] {
		height: 1px;
		width: 3.125rem;
		margin: 0 0.3125rem;
}
.zp-l-line-px[data-v-8cc5c400] {
		height: 1px;
		width: 50px;
		margin: 0 5px;
}
@keyframes loading-circle-8cc5c400 {
0% {
			transform: rotate(0deg);
}
100% {
			transform: rotate(360deg);
}
}


/* [z-paging]公共css*/
.z-paging-content[data-v-1aa372d7] {
	position: relative;
	flex-direction: column;

	overflow: hidden;
}
.z-paging-content-full[data-v-1aa372d7] {

	display: flex;
	width: 100%;
	height: 100%;
}
.z-paging-content-fixed[data-v-1aa372d7], .zp-loading-fixed[data-v-1aa372d7] {
	position: fixed;

	height: auto;
	width: auto;

	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}
.zp-f2-content[data-v-1aa372d7] {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	background-color: white;
}
.zp-page-top[data-v-1aa372d7], .zp-page-bottom[data-v-1aa372d7] {

	width: auto;

	position: fixed;
	left: 0;
	right: 0;
	z-index: 999;
}
.zp-page-left[data-v-1aa372d7], .zp-page-right[data-v-1aa372d7] {

	height: 100%;
}
.zp-scroll-view-super[data-v-1aa372d7] {
	flex: 1;
	overflow: hidden;
	position: relative;
}
.zp-view-super[data-v-1aa372d7] {

	display: flex;

	flex-direction: row;
}
.zp-scroll-view-container[data-v-1aa372d7], .zp-scroll-view[data-v-1aa372d7] {
	position: relative;

	height: 100%;
	width: 100%;
}
.zp-absoulte[data-v-1aa372d7] {

	position: absolute;
	top: 0;
	width: auto;
}
.zp-scroll-view-absolute[data-v-1aa372d7] {
	position: absolute;
	top: 0;
	left: 0;
}
.zp-scroll-view-hide-scrollbar[data-v-1aa372d7] ::-webkit-scrollbar {
	display: none;
	-webkit-appearance: none;
	width: 0 !important;
	height: 0 !important;
	background: transparent;
}
.zp-paging-touch-view[data-v-1aa372d7] {
	width: 100%;
	height: 100%;
	position: relative;
}
.zp-fixed-bac-view[data-v-1aa372d7] {
	position: absolute;
	width: 100%;
	top: 0;
	left: 0;
	height: 200px;
}
.zp-paging-main[data-v-1aa372d7] {
	height: 100%;

	display: flex;

	flex-direction: column;
}
.zp-paging-container[data-v-1aa372d7] {
	flex: 1;
	position: relative;

	display: flex;

	flex-direction: column;
}
.zp-chat-record-loading-custom-image[data-v-1aa372d7] {
	width: 1.09375rem;
	height: 1.09375rem;

	animation: loading-flower-1aa372d7 1s linear infinite;
}
.zp-page-bottom-keyboard-placeholder-animate[data-v-1aa372d7] {
	transition-property: height;
	transition-duration: 0.15s;

	will-change: height;
}
.zp-custom-refresher-container[data-v-1aa372d7] {
	overflow: hidden;
}
.zp-custom-refresher-refresh[data-v-1aa372d7] {

	display: block;
}
.zp-back-to-top[data-v-1aa372d7] {
	z-index: 999;
	position: absolute;
	bottom: 0;
	transition-duration: .3s;
	transition-property: opacity;
}
.zp-back-to-top-rpx[data-v-1aa372d7] {
	width: 2.375rem;
	height: 2.375rem;
	bottom: 0;
	right: 0.78125rem;
}
.zp-back-to-top-px[data-v-1aa372d7] {
	width: 38px;
	height: 38px;
	bottom: 0px;
	right: 13px;
}
.zp-back-to-top-show[data-v-1aa372d7] {
	opacity: 1;
}
.zp-back-to-top-hide[data-v-1aa372d7] {
	opacity: 0;
}
.zp-back-to-top-img[data-v-1aa372d7] {

	width: 100%;
	height: 100%;




	z-index: 999;
}
.zp-back-to-top-img-inversion[data-v-1aa372d7] {
	transform: rotate(180deg);
}
.zp-empty-view[data-v-1aa372d7] {



	flex: 1;
}
.zp-empty-view-center[data-v-1aa372d7] {

	display: flex;

	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.zp-loading-fixed[data-v-1aa372d7] {
	z-index: 9999;
}
.zp-safe-area-inset-bottom[data-v-1aa372d7] {
	position: absolute;
}
.zp-n-refresh-container[data-v-1aa372d7] {

	display: flex;

	justify-content: center;
	width: 23.4375rem;
}
.zp-n-list-container[data-v-1aa372d7]{

	display: flex;

	flex-direction: row;
	flex: 1;
}

/* [z-paging]公用的静态css资源 */
.zp-line-loading-image[data-v-1aa372d7] {

	animation: loading-flower-1aa372d7 1s steps(12) infinite;

	color: #666666;
}
.zp-line-loading-image-rpx[data-v-1aa372d7] {
	margin-right: 0.25rem;
	width: 1.0625rem;
	height: 1.0625rem;
}
.zp-line-loading-image-px[data-v-1aa372d7] {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}
.zp-loading-image-ios-rpx[data-v-1aa372d7] {
	width: 1.25rem;
	height: 1.25rem;
}
.zp-loading-image-ios-px[data-v-1aa372d7] {
	width: 20px;
	height: 20px;
}
.zp-loading-image-android-rpx[data-v-1aa372d7] {
	width: 1.0625rem;
	height: 1.0625rem;
}
.zp-loading-image-android-px[data-v-1aa372d7] {
	width: 17px;
	height: 17px;
}
@keyframes loading-flower-1aa372d7 {
0% {
		transform: rotate(0deg);
}
to {
		transform: rotate(1turn);
}
}



/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-easyinput[data-v-09fd5285] {
  width: 100%;
  flex: 1;
  position: relative;
  text-align: left;
  color: #333;
  font-size: 14px;
}
.uni-easyinput__content[data-v-09fd5285] {
  flex: 1;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  border-color: #fff;
  transition-property: border-color;
  transition-duration: 0.3s;
}
.uni-easyinput__content-input[data-v-09fd5285] {
  width: auto;
  position: relative;
  overflow: hidden;
  flex: 1;
  line-height: 1;
  font-size: 14px;
  height: 35px;
  /*ifdef H5*/
  /*endif*/
}
.uni-easyinput__content-input[data-v-09fd5285] ::-ms-reveal {
  display: none;
}
.uni-easyinput__content-input[data-v-09fd5285] ::-ms-clear {
  display: none;
}
.uni-easyinput__content-input[data-v-09fd5285] ::-o-clear {
  display: none;
}
.uni-easyinput__placeholder-class[data-v-09fd5285] {
  color: #999;
  font-size: 12px;
}
.is-textarea[data-v-09fd5285] {
  align-items: flex-start;
}
.is-textarea-icon[data-v-09fd5285] {
  margin-top: 5px;
}
.uni-easyinput__content-textarea[data-v-09fd5285] {
  position: relative;
  overflow: hidden;
  flex: 1;
  line-height: 1.5;
  font-size: 14px;
  margin: 6px;
  margin-left: 0;
  height: 80px;
  min-height: 80px;
  min-height: 80px;
  width: auto;
}
.input-padding[data-v-09fd5285] {
  padding-left: 10px;
}
.content-clear-icon[data-v-09fd5285] {
  padding: 0 5px;
}
.label-icon[data-v-09fd5285] {
  margin-right: 5px;
  margin-top: -1px;
}
.is-input-border[data-v-09fd5285] {
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.uni-error-message[data-v-09fd5285] {
  position: absolute;
  bottom: -17px;
  left: 0;
  line-height: 12px;
  color: #e43d33;
  font-size: 12px;
  text-align: left;
}
.uni-error-msg--boeder[data-v-09fd5285] {
  position: relative;
  bottom: 0;
  line-height: 22px;
}
.is-input-error-border[data-v-09fd5285] {
  border-color: #e43d33;
}
.is-input-error-border .uni-easyinput__placeholder-class[data-v-09fd5285] {
  color: #f29e99;
}
.uni-easyinput--border[data-v-09fd5285] {
  margin-bottom: 0;
  padding: 10px 15px;
  border-top: 1px #eee solid;
}
.uni-easyinput-error[data-v-09fd5285] {
  padding-bottom: 0;
}
.is-first-border[data-v-09fd5285] {
  border: none;
}
.is-disabled[data-v-09fd5285] {
  background-color: #f7f6f6;
  color: #d5d5d5;
}
.is-disabled .uni-easyinput__placeholder-class[data-v-09fd5285] {
  color: #d5d5d5;
  font-size: 12px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-forms-item[data-v-462874dd] {
  position: relative;
  display: flex;
  margin-bottom: 22px;
  flex-direction: row;
}
.uni-forms-item__label[data-v-462874dd] {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  font-size: 14px;
  color: #606266;
  height: 36px;
  padding: 0 12px 0 0;
  vertical-align: middle;
  flex-shrink: 0;
  box-sizing: border-box;
}
.uni-forms-item__label.no-label[data-v-462874dd] {
  padding: 0;
}
.uni-forms-item__content[data-v-462874dd] {
  position: relative;
  font-size: 14px;
  flex: 1;
  box-sizing: border-box;
  flex-direction: row;
}
.uni-forms-item .uni-forms-item__nuve-content[data-v-462874dd] {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.uni-forms-item__error[data-v-462874dd] {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
  transition: transform 0.3s;
  transform: translateY(-100%);
  opacity: 0;
}
.uni-forms-item__error .error-text[data-v-462874dd] {
  color: #f56c6c;
  font-size: 12px;
}
.uni-forms-item__error.msg--active[data-v-462874dd] {
  opacity: 1;
  transform: translateY(0%);
}
.uni-forms-item.is-direction-left[data-v-462874dd] {
  flex-direction: row;
}
.uni-forms-item.is-direction-top[data-v-462874dd] {
  flex-direction: column;
}
.uni-forms-item.is-direction-top .uni-forms-item__label[data-v-462874dd] {
  padding: 0 0 8px;
  line-height: 1.5715;
  text-align: left;
  white-space: initial;
}
.uni-forms-item .is-required[data-v-462874dd] {
  color: #dd524d;
  font-weight: bold;
}
.uni-forms-item--border[data-v-462874dd] {
  margin-bottom: 0;
  padding: 10px 0;
  border-top: 1px #eee solid;
}
.uni-forms-item--border .uni-forms-item__content[data-v-462874dd] {
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.uni-forms-item--border .uni-forms-item__content .uni-forms-item__error[data-v-462874dd] {
  position: relative;
  top: 5px;
  left: 0;
  padding-top: 0;
}
.is-first-border[data-v-462874dd] {
  border: none;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@media screen and (max-width: 500px) {
.hide-on-phone[data-v-ddf9e0a2] {
    display: none;
}
}
.uni-stat__select[data-v-ddf9e0a2] {
  display: flex;
  align-items: center;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
}
.uni-stat-box[data-v-ddf9e0a2] {
  width: 100%;
  flex: 1;
}
.uni-stat__actived[data-v-ddf9e0a2] {
  width: 100%;
  flex: 1;
}
.uni-label-text[data-v-ddf9e0a2] {
  font-size: 14px;
  font-weight: bold;
  color: #6a6a6a;
  margin: auto 0;
  margin-right: 5px;
}
.uni-select[data-v-ddf9e0a2] {
  font-size: 14px;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  border-radius: 4px;
  padding: 0 5px;
  padding-left: 10px;
  position: relative;
  display: flex;
  -webkit-user-select: none;
          user-select: none;
  flex-direction: row;
  align-items: center;
  border-bottom: solid 1px #e5e5e5;
  width: 100%;
  flex: 1;
  height: 35px;
}
.uni-select--disabled[data-v-ddf9e0a2] {
  background-color: #f5f7fa;
  cursor: not-allowed;
}
.uni-select__label[data-v-ddf9e0a2] {
  font-size: 16px;
  height: 35px;
  padding-right: 10px;
  color: #909399;
}
.uni-select__input-box[data-v-ddf9e0a2] {
  height: 35px;
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
}
.uni-select__input[data-v-ddf9e0a2] {
  flex: 1;
  font-size: 14px;
  height: 22px;
  line-height: 22px;
}
.uni-select__input-plac[data-v-ddf9e0a2] {
  font-size: 14px;
  color: #909399;
}
.uni-select__selector[data-v-ddf9e0a2] {
  box-sizing: border-box;
  position: absolute;
  left: 0;
  width: 100%;
  background-color: #FFFFFF;
  border: 1px solid #EBEEF5;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 3;
  padding: 4px 0;
}
.uni-select__selector-scroll[data-v-ddf9e0a2] {
  max-height: 200px;
  box-sizing: border-box;
}
.uni-select__selector-empty[data-v-ddf9e0a2],
.uni-select__selector-item[data-v-ddf9e0a2] {
  display: flex;
  cursor: pointer;
  line-height: 35px;
  font-size: 14px;
  text-align: center;
  /* border-bottom: solid 1px $uni-border-3; */
  padding: 0px 10px;
}
.uni-select__selector-item[data-v-ddf9e0a2]:hover {
  background-color: #f9f9f9;
}
.uni-select__selector-empty[data-v-ddf9e0a2]:last-child,
.uni-select__selector-item[data-v-ddf9e0a2]:last-child {
  border-bottom: none;
}
.uni-select__selector__disabled[data-v-ddf9e0a2] {
  opacity: 0.4;
  cursor: default;
}

/* picker 弹出层通用的指示小三角 */
.uni-popper__arrow_bottom[data-v-ddf9e0a2],
.uni-popper__arrow_bottom[data-v-ddf9e0a2]::after,
.uni-popper__arrow_top[data-v-ddf9e0a2],
.uni-popper__arrow_top[data-v-ddf9e0a2]::after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
}
.uni-popper__arrow_bottom[data-v-ddf9e0a2] {
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: -6px;
  left: 10%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #EBEEF5;
}
.uni-popper__arrow_bottom[data-v-ddf9e0a2]::after {
  content: " ";
  top: 1px;
  margin-left: -6px;
  border-top-width: 0;
  border-bottom-color: #fff;
}
.uni-popper__arrow_top[data-v-ddf9e0a2] {
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  bottom: -6px;
  left: 10%;
  margin-right: 3px;
  border-bottom-width: 0;
  border-top-color: #EBEEF5;
}
.uni-popper__arrow_top[data-v-ddf9e0a2]::after {
  content: " ";
  bottom: 1px;
  margin-left: -6px;
  border-bottom-width: 0;
  border-top-color: #fff;
}
.uni-select__input-text[data-v-ddf9e0a2] {
  width: 100%;
  color: #333;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}
.uni-select__input-placeholder[data-v-ddf9e0a2] {
  color: #6a6a6a;
  font-size: 12px;
}
.uni-select--mask[data-v-ddf9e0a2] {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 2;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 遮罩层 */
.action-sheet-mask[data-v-d53be547] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
}
.action-sheet-mask--transparent[data-v-d53be547] {
  background-color: transparent;
}

/* 容器 */
.action-sheet-container[data-v-d53be547] {
  position: fixed;
  left: 50%;
  /* 水平居中 */
  top: 50%;
  /* 垂直居中 */
  transform: translate(-50%, -50%);
  /* 通过平移调整容器位置 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  border-radius: 10px;
  z-index: 1000;
  box-sizing: border-box;
  max-width: 750px;
  width: 100%;
}

/* 标题 */
.action-sheet-title[data-v-d53be547] {
  padding: 0.875rem 1rem;
  color: #969799;
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: center;
}

/* 描述 */
.action-sheet-description[data-v-d53be547] {
  padding: 0.625rem 1rem;
  color: #969799;
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: center;
}

/* 选项列表 */
.action-sheet-options[data-v-d53be547] {
  position: relative;
  max-height: 70vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 选项项 */
.action-sheet-item[data-v-d53be547] {
  position: relative;
  padding: 0.875rem 1rem;
  text-align: center;
  background-color: #fff;
}
.action-sheet-item[data-v-d53be547]:active {
  background-color: #f2f3f5;
}
.action-sheet-item--disabled[data-v-d53be547] {
  opacity: 0.6;
}
.action-sheet-item--disabled[data-v-d53be547]:active {
  background-color: #fff;
}
.action-sheet-item--loading .action-sheet-item__text[data-v-d53be547] {
  opacity: 0;
}
.action-sheet-item[data-v-d53be547]::after {
  position: absolute;
  content: "";
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 1rem;
  border-bottom: 0.03125rem solid #ebedf0;
  transform: scaleY(0.5);
}
.action-sheet-item[data-v-d53be547]:last-child::after {
  display: none;
}
.action-sheet-item__content[data-v-d53be547] {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.action-sheet-item__text[data-v-d53be547] {
  color: #323233;
  font-size: 1rem;
  line-height: 1.4;
}
.action-sheet-item__text--danger[data-v-d53be547] {
  color: #ee0a24;
}
.action-sheet-item__subname[data-v-d53be547] {
  margin-top: 0.25rem;
  color: #969799;
  font-size: 0.75rem;
  line-height: 1.4;
}

/* 加载状态 */
.action-sheet-item__loading[data-v-d53be547] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.action-sheet-item__loading-spinner[data-v-d53be547] {
  width: 1.25rem;
  height: 1.25rem;
  border: 0.125rem solid transparent;
  border-top-color: #c8c9cc;
  border-radius: 50%;
  animation: action-sheet-spinner-d53be547 0.8s linear infinite;
}

/* 取消按钮 */
.action-sheet-cancel[data-v-d53be547] {
  margin-top: 0.375rem;
  padding: 0.875rem 1rem;
  text-align: center;
}
.action-sheet-cancel[data-v-d53be547]:active {
  background-color: #f2f3f5;
}
.action-sheet-cancel__text[data-v-d53be547] {
  color: #323233;
  font-size: 1rem;
  line-height: 1.4;
}

/* 动画 */
.fade-enter-active[data-v-d53be547],
.fade-leave-active[data-v-d53be547] {
  transition: opacity 0.1s;
}
.fade-enter-from[data-v-d53be547],
.fade-leave-to[data-v-d53be547] {
  opacity: 0;
}
.slide-up-enter-active[data-v-d53be547],
.slide-up-leave-active[data-v-d53be547] {
  transition: transform 0.1s;
}
.slide-up-enter-from[data-v-d53be547],
.slide-up-leave-to[data-v-d53be547] {
  transform: translate3d(0, 100%, 0);
}
@keyframes action-sheet-spinner-d53be547 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.form-header[data-v-37007b00] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.form-header-title[data-v-37007b00] {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.form-header-btn[data-v-37007b00] {
  font-size: 14px;
  color: #666;
  padding: 5px 10px;
}
.confirm-btn[data-v-37007b00] {
  color: #007aff;
}
.confirm-btn--disabled[data-v-37007b00] {
  opacity: 0.5;
}
.form-content[data-v-37007b00] {
  max-height: 80vh;
}
.form-content .task-form[data-v-37007b00] {
  padding: 10px 15px;
}
.form-content .task-form[data-v-37007b00] .uni-forms-item__label {
  width: 80px !important;
  height: 35px;
  color: var(---, #787d86);
  text-overflow: ellipsis;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 35px;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.customer-service[data-v-fec1fb0d] {
  position: fixed;
  z-index: 999;
  border-radius: 50%;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}
.customer-service.is-dragging[data-v-fec1fb0d] {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}
.customer-service.is-dragging .customer-icon[data-v-fec1fb0d] {
  opacity: 0.9;
}
.customer-service.is-pc[data-v-fec1fb0d]:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.2);
}
.customer-service .customer-icon[data-v-fec1fb0d] {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.customer-service .drag-indicator[data-v-fec1fb0d] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: pulse-fec1fb0d 1s infinite;
  pointer-events: none;
}
@keyframes pulse-fec1fb0d {
0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}
100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
}
}
@media (max-width: 768px) {
.customer-service[data-v-fec1fb0d]:active {
    transform: scale(0.95);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@media (max-width: 768px) {
.statistics[data-v-ef2ace18] {
    margin: 0 !important;
}
}
.z-paging-content[data-v-ef2ace18] {
  max-width: 750px;
  margin: 0 auto;
  /* 添加底部占位空间样式 */
}
.z-paging-content .styles-img[data-v-ef2ace18] {
  width: 25px;
  height: 25px;
}
.z-paging-content .bottom-space[data-v-ef2ace18] {
  height: 20px;
  width: 100%;
}
.z-paging-content .customer-img[data-v-ef2ace18] {
  width: 24px;
  height: 24px;
}
.task-detail-container[data-v-ef2ace18] {
  max-width: 750px;
  margin: 0 auto;
}
.uni-navbar[data-v-ef2ace18] .uni-navbar--border {
  border: none;
}
.uni-navbar[data-v-ef2ace18] .uni-nav-bar-text {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.business-container[data-v-ef2ace18] {
  padding: 20px 20px 0px 20px;
}
.business-header[data-v-ef2ace18] {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.business-header .item-header[data-v-ef2ace18] {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e2e4e9;
  border-radius: 8px;
  background: #fff;
}
.business-header .item-header uni-text[data-v-ef2ace18] {
  margin-left: 4px;
  font-size: 14px;
  color: #333;
}
.section-box[data-v-ef2ace18] {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.section-box .section-title[data-v-ef2ace18] {
  color: var(---, #787d86);
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.business-item[data-v-ef2ace18] {
  display: flex;
  align-items: center;
  margin: 10px 12px 5px 12px;
}
.business-item .content-wrapper[data-v-ef2ace18] {
  flex: 1;
  margin-left: 6px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 2px;
  overflow: hidden;
}
.business-item .business-title[data-v-ef2ace18] {
  color: var(---, #000);
  text-overflow: ellipsis;
  font-size: 14px;
  white-space: nowrap;
  /* 确保文本不换行 */
  overflow: hidden;
  /* 隐藏溢出内容 */
  max-width: 100%;
  /* 确保宽度不超过父容器 */
}
.business-item .title-row[data-v-ef2ace18] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
  color: var(---, #adb1ba);
  font-size: 12px;
}
.business-item .title-row .todo-row[data-v-ef2ace18] {
  display: flex;
  align-items: center;
  max-width: 62vw;
  max-height: 20px;
}
.business-item .title-row .todo-row .business-time[data-v-ef2ace18] {
  min-width: 95px;
  margin-right: 4px;
  max-width: 100%;
}
.business-item .title-row .todo-row .todo-text[data-v-ef2ace18] {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
}
.sortable-list[data-v-ef2ace18] {
  border-radius: 10px;
  border: 1px solid #e2e4e9;
  margin: 0 18px;
  background: white;
}
.sortable-item[data-v-ef2ace18] {
  position: relative;
  transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.15s ease, background-color 0.15s ease;
  will-change: transform, opacity;
  transform: translateZ(0);
  /* 强制GPU加速 */
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  /* 优化渲染性能 */
}
.sortable-item[data-v-ef2ace18] .uni-swipe_box {
  max-height: 56px;
}
.sortable-item:last-child .content-wrapper[data-v-ef2ace18] {
  border-bottom: none;
}
.sortable-item.dragging[data-v-ef2ace18] {
  opacity: 0.9;
  transform: scale(1.02);
  background-color: #e6f7ff;
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.15);
  z-index: 1000;
  border-left: 4px solid #007aff;
  pointer-events: none;
}
.sortable-item.drag-feedback[data-v-ef2ace18] {
  opacity: 0.4;
  transform: translateZ(0) scale(0.98);
  background-color: #e6f7ff;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  z-index: 999;
  border-left: 4px solid #007aff;
  position: relative;
  transition: all 0.1s ease;
}
.sortable-item.out-of-bounds[data-v-ef2ace18] {
  border: 1px dashed #ff4d4f !important;
  background-color: rgba(255, 77, 79, 0.1) !important;
  opacity: 0.6 !important;
  transform: translateZ(0) scale(0.95) !important;
  box-shadow: 0 0 8px rgba(255, 77, 79, 0.5) !important;
  filter: grayscale(0.3);
}
.sortable-item.placeholder[data-v-ef2ace18] {
  opacity: 0.4;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  transform: translateZ(0);
}
/* 拖拽克隆元素样式 */
.drag-clone[data-v-ef2ace18] {
  position: fixed;
  z-index: 9999;
  transition: transform 0.08s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  pointer-events: none;
  transform: translateZ(0);
  /* 强制GPU加速 */
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}