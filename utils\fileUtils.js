// 检查是否是图片文件
export const isImageFile = (filename) => {
  if (!filename) return false;
  const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "svg+xml"];
  // 处理content_type参数的情况
  if (filename.includes("/")) {
    // 可能是MIME类型，如image/png, image/jpeg等
    return filename.startsWith("image/");
  }
  // 处理文件名的情况
  const ext = filename.split(".").pop().toLowerCase();
  return imageExtensions.includes(ext);
};

// 获取文件图标路径
export const getFileIconPath = (filename) => {
  if (!filename) return "/static/file/file-default.png";
  // 使用正则表达式匹配 URL 中的文件名，获取问号前的部分
  const fileNameMatch = filename.match(/([^/]+)(?=\?)/); 
  const fileName = fileNameMatch ? fileNameMatch[0] : filename;
  // 获取文件扩展名
  const ext = fileName.split(".").pop().toLowerCase();
  // 文件扩展名映射
  const iconMap = {
    pdf: "/static/file/pdf.svg",
    doc: "/static/file/doc.svg",
    docx: "/static/file/doc.svg",
    xls: "/static/file/xls.svg",
    xlsx: "/static/file/xls.svg",
    ppt: "/static/file/ppt.svg",
    pptx: "/static/file/pptx.svg",
    txt: "/static/file/txt.svg",
    rvt: "/static/file/rvt.svg",
    // mp3: "/static/file/mp3.svg",
    mp3: "/static/file/record.svg",
    psd: "/static/file/psd.svg",
    zip: "/static/file/zip.svg",
    html: "/static/file/html.svg",
    png: "/static/file/png.svg",
    xmind: "/static/file/xmind.svg", 
    exe: "/static/file/exe.svg",
    jpg: "/static/file/jpg.svg",
    jpeg: "/static/file/jpg.svg",
    png: "/static/file/jpg.svg",
    webp: "/static/file/jpg.svg",
    wav: "/static/file/record.svg",
    m4a: "/static/file/record.svg",
    aac: "/static/file/record.svg",
    default: "/static/file/unknown.svg",
    // "md"
    // "CSV",
    // "json"
    // 'xml"
    // "pcm",
    // "ogg-opus",
    // "speex"
    // "silk",
    //   amr
  };
  // 返回文件图标路径，如果没有匹配扩展名，返回默认图标
  return iconMap[ext] || iconMap['default'];
};

// 获取短文件名（如果太长则截断）
export const getShortFileName = (filename) => {
  if (!filename) return "未命名文件";
  // 最大显示长度
  const maxLength = 20;
  if (filename.length <= maxLength) return filename;
  // 获取文件扩展名
  const parts = filename.split(".");
  const ext = parts.length > 1 ? `.${parts.pop()}` : "";
  const name = parts.join(".");
  // 截断文件名并添加省略号
  if (name.length <= maxLength - ext.length - 3) return filename;
  return `${name.substring(0, maxLength - ext.length - 3)}...${ext}`;
};

// 格式化文件大小
export const formatFileSize = (size) => {
  if (!size || size === 0) return "";
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / (1024 * 1024)).toFixed(1)} MB`;
};

// 从URL中提取文件名
export const getFileNameFromUrl = (url) => {
  if (!url) return "未知文件";
  
  try {
    // 解码URL以处理中文等特殊字符
    const decodedUrl = decodeURIComponent(url);
    
    // 移除查询参数
    const urlWithoutParams = decodedUrl.split('?')[0];
    
    // 获取路径的最后一部分作为文件名
    const pathParts = urlWithoutParams.split('/');
    let fileName = pathParts[pathParts.length - 1];
    
    // 如果文件名为空，返回默认名称
    if (!fileName) return "未知文件";
    
    return fileName;
  } catch (error) {
    console.error("从URL提取文件名时出错:", error);
    return "未知文件";
  }
};

// 获取MIME类型的辅助函数
export const getMimeType = (fileName) => {
  const extensionMap = {
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'json': 'application/json',
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'mp3': 'audio/mpeg',
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed'
  };
  
  if (!fileName) return 'application/octet-stream';
  
  const extension = fileName.split('.').pop().toLowerCase();
  return extensionMap[extension] || 'application/octet-stream';
};

// 获取文件扩展名的方法,传入文件名
export function getFileExtension(fileName) {
  if (!fileName) return null;
  // 正则匹配文件名中的后缀
  const match = fileName.match(/\.([a-zA-Z0-9]+)$/); // 匹配文件扩展名（字母和数字后缀）
  return match ? match[0] : null;  // 返回完整后缀，如 .pdf
}


/**
 * 打开WebOffice预览（页面跳转）
 * @param {Object} file - 文件对象
 */
export  const openWebOfficePreview = (file) => {
  console.log('📄 打开WebOffice预览页面:', file.name);
  try {
    // 只传递必要的文件信息（name和id）
    const fileInfo = {
      name: file.name,
      id: file.id || file.attachment_id
    };
    // 检查必要参数
    if (!fileInfo.id) {
      uni.showToast({
        title: '文件ID缺失，无法预览',
        icon: 'none'
      });
      return;
    }
    const fileInfoParam = encodeURIComponent(JSON.stringify(fileInfo));
    // 跳转到预览页面
    uni.navigateTo({
      url: `/pages/preview/weboffice-preview?fileInfo=${fileInfoParam}`,
      success: () => {
        console.log('✅ 成功跳转到WebOffice预览页面');
      },
      fail: (error) => {
        console.error('❌ 跳转到WebOffice预览页面失败:', error);
        uni.showToast({
          title: '打开预览失败',
          icon: 'none'
        });
      }
    });
  } catch (error) {
    console.error('❌ 打开WebOffice预览失败:', error);
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};

/**
 * 创建文件夹（Android专用）
 * @param {string} path - 文件夹路径
 * @param {Function} callback - 创建成功回调
 */
const createDir = async (path, callback) => {
  // #ifdef APP-PLUS
  // 申请本地存储读写权限
  plus.android.requestPermissions([
    'android.permission.WRITE_EXTERNAL_STORAGE',
    'android.permission.READ_EXTERNAL_STORAGE',
    'android.permission.INTERNET',
    'android.permission.ACCESS_WIFI_STATE'
  ], () => {
    const File = plus.android.importClass('java.io.File');
    let file = new File(path);
    // 文件夹不存在即创建
    if (!file.exists()) {
      file.mkdirs();
      callback && callback();
      return false;
    }
    callback && callback();
    return false;
  }, () => {
    uni.showToast({
      title: '无法获取权限，文件下载将出错',
      icon: 'none'
    });
  });
  // #endif
};

/**
 * 通用文件下载函数 - 真正下载到设备存储
 * @param {string} url - 文件下载地址
 * @param {string} fileName - 文件名（可选，如果不传则从URL中提取）
 * @returns {Promise} 下载结果
 */
export const downloadFile = async (url, fileName = null) => {
  if (!url) {
    throw new Error('下载地址不能为空');
  }

  // 如果没有提供文件名，从URL中提取
  const finalFileName = fileName || getFileNameFromUrl(url);

  // #ifdef H5
  try {
    // 显示下载开始提示
    uni.showToast({
      title: '下载已开始',
      icon: 'success',
      duration: 2000
    });

    // 使用fetch + Blob强制下载（支持所有文件格式）
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Disposition': 'attachment', // 强制下载
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    console.log('📦 获取到文件Blob:', blob.type, blob.size);

    // 获取正确的MIME类型，但强制设置为下载类型
    let mimeType = getMimeType(finalFileName);

    // 对于容易被浏览器预览的文件类型，强制设置为下载类型
    const previewTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'text/plain', 'text/html'];
    if (previewTypes.includes(mimeType) || previewTypes.includes(blob.type)) {
      mimeType = 'application/octet-stream'; // 强制下载
    }

    // 创建新的Blob对象，使用强制下载的MIME类型
    const downloadBlob = new Blob([blob], { type: mimeType });

    // 创建下载链接
    const blobUrl = URL.createObjectURL(downloadBlob);
    const link = document.createElement('a');

    // 设置下载属性 - 关键是这些属性的组合
    link.href = blobUrl;
    link.download = finalFileName || '下载文件'; // 强制指定文件名
    link.style.display = 'none';
    link.target = '_self'; // 在当前窗口下载
    link.rel = 'noopener noreferrer';

    // 添加到DOM
    document.body.appendChild(link);

    // 触发下载 - 针对iOS Safari优化，避免重复触发
    setTimeout(() => {
      // 检测是否为iOS Safari
      const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

      if (isIOSSafari) {
        // iOS Safari：只使用click方法，避免重复触发
        link.click();
        console.log('✅ iOS Safari下载链接已触发');
      } else {
        // 其他浏览器：使用click方法，如果失败则尝试事件触发
        try {
          link.click();
          console.log('✅ 下载链接已触发 - click方法');
        } catch (clickError) {
          console.warn('⚠️ click方法失败，尝试事件触发:', clickError);
          // 只有在click失败时才使用事件触发
          setTimeout(() => {
            const event = new MouseEvent('click', {
              view: window,
              bubbles: true,
              cancelable: true
            });
            link.dispatchEvent(event);
            console.log('✅ 下载链接已触发 - 事件方法');
          }, 50);
        }
      }

      // 清理资源
      setTimeout(() => {
        if (document.body.contains(link)) {
          document.body.removeChild(link);
        }
        URL.revokeObjectURL(blobUrl);
        console.log('🧹 下载资源已清理');
      }, 200);
    }, 10);

    return { success: true, message: '下载成功' };
  } catch (error) {
    console.error('H5下载失败:', error);
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    });
    throw error;
  }
  // #endif

  // #ifdef APP-PLUS
  // APP端下载逻辑
  return new Promise((resolve, reject) => {
    let osName = plus.os.name;

    if (osName === 'Android') {
      // Android：下载到自定义文件夹
      let mkdirsName = '/SaleAgent'; // 自定义文件夹名称
      let path = '/storage/emulated/0' + mkdirsName;

      // 创建文件夹
      createDir(path, () => {
        uni.showLoading({
          title: '正在下载'
        });

        // 使用plus.downloader下载到指定路径
        let dtask = plus.downloader.createDownload(url, {
          filename: 'file://' + path + '/' + finalFileName
        }, function(d, status) {
          uni.hideLoading();

          if (status == 200) {
            let fileSaveUrl = plus.io.convertLocalFileSystemURL(d.filename);
            console.log('✅ Android文件下载成功:', fileSaveUrl);

            uni.showToast({
              icon: 'none',
              mask: true,
              title: '文件已保存：' + mkdirsName + '/' + finalFileName,
              duration: 3000
            });

            resolve({
              success: true,
              message: '下载成功',
              filePath: fileSaveUrl,
              savedPath: mkdirsName + '/' + finalFileName
            });
          } else {
            plus.downloader.clear();
            reject(new Error(`下载失败，状态码: ${status}`));
          }
        });

        // 监听下载进度
        dtask.addEventListener('statechanged', function(download) {
          switch (download.state) {
            case 1: // 开始下载
              console.log('开始下载...');
              break;
            case 2: // 下载中
              let progress = parseInt(download.downloadedSize / download.totalSize * 100);
              console.log('下载进度: ' + progress + '%');
              break;
            case 3: // 下载完成
              console.log('下载完成');
              break;
            case 4: // 下载失败
              console.log('下载失败');
              uni.hideLoading();
              reject(new Error('下载失败'));
              break;
          }
        });

        dtask.start();
      });
    } else {
      // iOS：下载后提示用户手动保存
      uni.showLoading({
        title: '正在下载'
      });

      let dtask = plus.downloader.createDownload(url, {
        // iOS不指定路径，使用系统默认路径
      }, function(d, status) {
        uni.hideLoading();

        if (status == 200) {
          let fileSaveUrl = plus.io.convertLocalFileSystemURL(d.filename);
          console.log('✅ iOS文件下载成功:', fileSaveUrl);

          uni.showModal({
            title: '下载完成',
            content: '文件下载成功！如需保存到本地，请点击"打开文件"后选择"存储到文件"',
            confirmText: '打开文件',
            cancelText: '我知道了',
            success: function(res) {
              if (res.confirm) {
                uni.openDocument({
                  filePath: d.filename,
                  success: () => {
                    console.log('✅ 成功打开文件');
                    resolve({
                      success: true,
                      message: '下载并打开成功',
                      filePath: fileSaveUrl
                    });
                  },
                  fail: (err) => {
                    console.error('❌ 打开文件失败:', err);
                    resolve({
                      success: true,
                      message: '下载成功，但打开失败',
                      filePath: fileSaveUrl
                    });
                  }
                });
              } else {
                resolve({
                  success: true,
                  message: '下载成功',
                  filePath: fileSaveUrl
                });
              }
            }
          });
        } else {
          plus.downloader.clear();
          reject(new Error(`下载失败，状态码: ${status}`));
        }
      });

      // 监听下载进度
      dtask.addEventListener('statechanged', function(download) {
        switch (download.state) {
          case 1: // 开始下载
            console.log('开始下载...');
            break;
          case 2: // 下载中
            let progress = parseInt(download.downloadedSize / download.totalSize * 100);
            console.log('下载进度: ' + progress + '%');
            break;
          case 3: // 下载完成
            console.log('下载完成');
            break;
          case 4: // 下载失败
            console.log('下载失败');
            uni.hideLoading();
            reject(new Error('下载失败'));
            break;
        }
      });

      dtask.start();
    }
  });
  // #endif

  // #ifdef MP
  // 小程序：复制链接到剪贴板
  return new Promise((resolve, reject) => {
    uni.setClipboardData({
      data: url,
      success: () => {
        uni.showToast({
          title: '链接已复制，请在浏览器打开',
          icon: 'none',
          duration: 3000
        });
        resolve({ success: true, message: '链接已复制到剪贴板' });
      },
      fail: () => {
        reject(new Error('复制链接失败'));
      }
    });
  });
  // #endif
};
