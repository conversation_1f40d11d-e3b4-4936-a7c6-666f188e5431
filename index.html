<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title>销售助理Ivy</title>
    <!--preload-links-->
    <!--app-context-->
		<link rel="icon" href="./static/user/Ivy.png"/>
		<meta name="apple-mobile-web-app-title" content="销售助理Ivy">
		<link rel="apple-touch-icon" href="./static/user/Ivy.png">
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/main.js"></script>
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
    <script>
      // VConsole 默认会挂载到 `window.VConsole` 上
      var vConsole = new window.VConsole();
      // 接下来即可照常使用 `console` 等方法
        console.log('Hello world');
        
        // 结束调试后，可移除掉
        vConsole.destroy();
    </script> -->
  </body>
</html>
