<template>
  <!-- 全局搜索 页面 -->
    <view class="search-container">
    <!-- 搜索头部 -->
    <view class="search-header" :style="appSafeAreaStyle">
      <uni-icons type="left" size="24" color="#333333" @click="handerLeftBack" />
      <uni-easyinput
        v-model="searchKeyword"
        prefixIcon="search"
        :maxlength="50"
        placeholder="请输入搜索内容"
        :clearable="true"
        @blur="handleSearch"
        @confirm="handleSearch"
        @clear="handleClear"
        class="search-input"
      >
        <template #prefix>
          <uni-icons type="search" size="18" color="#999999" />
        </template>
      </uni-easyinput>
    </view>
    <!-- 内容区域 -->
    <view class="search-content">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-state">
        <uni-load-more status="loading" :content-text="{
          contentdown: '搜索中...',
          contentrefresh: '搜索中...',
          contentnomore: '搜索中...'
        }" />
      </view>
      <!-- 空状态 -->
      <SearchEmptyState v-else-if="showEmptyResult" />
      <!-- 搜索结果列表 -->
      <block v-else-if="!isLoading && hasSearched">
        <!-- 搜索结果项 -->
        <!-- 1、搜索到的代办类型 -->
        <SearchResultSection
          title="待办"
          :list="state.todoList"
          :state="1"
          v-if="state.todoList.length"
        />
        <!-- 2、搜索到的客户类型 -->
        <SearchResultSection
          title="客户"
          :list="state.customerList"
          :state="2"
          v-if="state.customerList.length"
        />
        <!-- 3、搜索到的商机类型 -->
        <SearchResultSection
          title="商机"
          :list="state.businessList"
          :state="3"
          v-if="state.businessList.length"
        />
        <!-- 4、搜索到的文件类型 -->
        <SearchResultSection
          title="文件"
          :list="state.fileList"
          :state="4"
          v-if="state.fileList.length"
        />
      </block>
    </view>
    <!-- AI 对话组件 -->
    <CustomerService />
  </view>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import SearchEmptyState from "./SearchEmptyState.vue";
import SearchResultSection from "./SearchResultSection.vue";
import CustomerService from "@/components/CustomerService.vue";
// 导入接口
import { getTodoList } from "@/http/todo";
import { fetchOpportunityList } from "@/http/business";
import request from "@/utils/request";

const searchKeyword = ref("");
const hasSearched = ref(false); // 是否已执行搜索
const isLoading = ref(false); // 搜索加载状态
const searchTimer = ref(null); // 防抖定时器

const state = reactive({
  todoList: [], // 待办
  customerList: [], // 客户
  businessList: [], // 商机
  fileList: [], // 文件
});

// 删除解构赋值
// const { todoList, customerList, businessList, fileList } = state;
// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`,
  };
  // #endif
  return {};
});

// 修改计算属性，默认显示空状态，搜索后根据结果显示
const showEmptyResult = computed(() => {
  // 如果正在加载，不显示空状态
  if (isLoading.value) return false;

  // 如果没有搜索过，显示默认空状态
  if (!hasSearched.value) return true;

  // 如果搜索过但没有结果，显示空状态
  return (
    state.todoList.length === 0 &&
    state.customerList.length === 0 &&
    state.businessList.length === 0 &&
    state.fileList.length === 0
  );
});

// 计算搜索结果总数
const totalResults = computed(() => {
  return state.todoList.length +
         state.customerList.length +
         state.businessList.length +
         state.fileList.length;
});

// 返回上一页
const handerLeftBack = () => {
  uni.switchTab({
    url: '/pages/tabBar/more/more',
  });
};

/**
 * 搜索文件
 * @param {string} keyword 搜索关键词
 * @returns {Promise<Array>} 文件列表
 */
const searchFiles = async (keyword) => {
  try {
    const { data } = await request({
      url: "/api/attachment/list",
      method: "POST",
      data: {
        page: 1,
        limit: 100, // 减少单次请求数量
        name: `%${keyword}%`,
      },
    });
    return data?.list || [];
  } catch (error) {
    console.error("文件搜索失败:", error);
    throw new Error("文件搜索失败");
  }
};

/**
 * 搜索客户
 * @param {string} keyword 搜索关键词
 * @returns {Promise<Array>} 客户列表
 */
const searchCustomers = async (keyword) => {
  try {
    const { data } = await request({
      url: "/api/customer/list",
      method: "POST",
      data: {
        page: 1,
        limit: 100, // 减少单次请求数量
        company_name: `%${keyword}%`,
      },
    });
    return data?.list || [];
  } catch (error) {
    console.error("客户搜索失败:", error);
    throw new Error("客户搜索失败");
  }
};

/**
 * 防抖搜索函数
 * @param {string} keyword 搜索关键词
 * @param {number} delay 延迟时间（毫秒）
 */
const debouncedSearch = (keyword, delay = 300) => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
  }

  searchTimer.value = setTimeout(() => {
    if (keyword.trim()) {
      performSearch(keyword.trim());
    }
  }, delay);
};

/**
 * 执行实际搜索
 * @param {string} keyword 搜索关键词
 */
const performSearch = async (keyword) => {
  if (isLoading.value) return; // 防止重复搜索

  isLoading.value = true;
  hasSearched.value = true;

  // 显示加载提示
  uni.showLoading({
    title: '搜索中...',
    mask: true
  });

  const searchParams = {
    page: 1,
    limit: 100, // 减少单次请求数量
    title: `%${keyword}%`,
  };

  try {
    // 清空之前的搜索结果
    state.todoList = [];
    state.customerList = [];
    state.businessList = [];
    state.fileList = [];

    // 并发请求所有数据
    const [todos, business, files, customers] = await Promise.allSettled([
      getTodoList(searchParams),
      fetchOpportunityList(searchParams),
      searchFiles(keyword),
      searchCustomers(keyword),
    ]);

    // 处理待办搜索结果
    if (todos.status === 'fulfilled') {
      state.todoList = todos.value?.data?.list || [];
    } else {
      console.error("待办搜索失败:", todos.reason);
    }

    // 处理商机搜索结果
    if (business.status === 'fulfilled') {
      state.businessList = business.value?.data?.list || [];
    } else {
      console.error("商机搜索失败:", business.reason);
    }

    // 处理文件搜索结果
    if (files.status === 'fulfilled') {
      state.fileList = files.value || [];
    } else {
      console.error("文件搜索失败:", files.reason);
    }

    // 处理客户搜索结果
    if (customers.status === 'fulfilled') {
      state.customerList = customers.value || [];
    } else {
      console.error("客户搜索失败:", customers.reason);
    }

    // 检查是否有任何搜索失败
    const failedCount = [todos, business, files, customers].filter(
      result => result.status === 'rejected'
    ).length;

    if (failedCount > 0 && totalResults.value === 0) {
      uni.showToast({
        title: "搜索服务异常，请重试",
        icon: "none",
        duration: 2000
      });
    }

  } catch (error) {
    console.error("搜索失败:", error);
    uni.showToast({
      title: "搜索失败，请重试",
      icon: "none",
      duration: 2000
    });
  } finally {
    isLoading.value = false;
    uni.hideLoading();
  }
};

/**
 * 处理搜索输入
 */
const handleSearch = () => {
  const keyword = searchKeyword.value.trim();
  if (!keyword) {
    handleClear();
    return;
  }

  // 使用防抖搜索
  debouncedSearch(keyword);
};

/**
 * 清除搜索内容
 */
const handleClear = () => {
  // 清除防抖定时器
  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
    searchTimer.value = null;
  }

  // 重置搜索状态
  searchKeyword.value = "";
  hasSearched.value = false;
  isLoading.value = false;

  // 清空所有搜索结果
  state.todoList = [];
  state.customerList = [];
  state.businessList = [];
  state.fileList = [];

  // 隐藏可能存在的加载提示
  uni.hideLoading();
};
</script>

<style scoped lang="scss">
// 导入通用样式
@import "/styles/common-styles.scss";
@import "/styles/search-common.scss";

.search-container {
  @extend .search-container-base;

  /* 搜索头部 */
  .search-header {
    @extend .search-header-base;
  }

  /* 内容区域 */
  .search-content {
    @extend .search-content-base;
  }
}
</style>
