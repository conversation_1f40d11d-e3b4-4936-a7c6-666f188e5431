/**
 * 语音识别服务
 * 实现将用户语音转换为文本的功能
 */

// 默认语音识别配置
const defaultVoiceRecognitionConfig = {
  appkey: 'ZsmxFv9inn9RVE3N',
  token: uni.getStorageSync("aliyunToken"),
  url: 'wss://nls-gateway-cn-shenzhen.aliyuncs.com/ws/v1',
  // 语音识别参数
  format: "pcm",
  sample_rate: 16000,
  enable_intermediate_result: true,
  enable_punctuation_prediction: true,
  enable_inverse_text_normalization: true,
  enable_voice_detection: true,
  max_start_silence: 10000,  // 开始前的最大静音时长（毫秒）
  max_end_silence: 800,     // 结束的最大静音时长（毫秒）
  speech_noise_threshold: 5.0, // 语音噪音阈值
  speech_volume_threshold: 5.0,  // 语音音量阈值
	vocabulary_id: "85f56a7e747e428d94ae828a78adf9fa", //热词id
};

// 生成UUID函数
const generateUUID = () => {
  try {
    return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
      (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    ).replace(/-/g, '');
  } catch (error) {
    // 兼容不支持 crypto API 的环境（如某些APP）
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
};

class VoiceRecognitionService {
  /**
   * 构造函数
   * @param {Object} customConfig - 自定义配置，可覆盖默认配置
   */
  constructor(customConfig = {}) {
    // 合并默认配置和自定义配置
    this.config = { ...defaultVoiceRecognitionConfig, ...customConfig };
    // 状态变量
    this.isActive = false;
    this.recognizedText = '';
    this.previousText = ''; // 存储之前识别文本的变量
    this.hasNewSession = true; // 标记是否是新的语音会话
    this.accumulatedResults = []; // 存储连续语音识别结果的数组
    this.currentSegment = ''; // 当前语音段识别的临时结果
    this.isListening = false; // 是否正在听（检测到声音）
    
    // 标记是否因为静音超时而停止
    this.stoppedDueToSilence = false;
    
    // WebSocket和音频处理变量
    this.websocket = null;
    this.audioContext = null;
    this.scriptProcessor = null;
    this.audioInput = null;
    this.audioStream = null;
    
    // 回调函数
    this.onTextRecognized = null;
    this.onStatusChange = null;
    
    // 沉默检测
    this.silenceTimeout = null;
    this.lastSpeechTime = 0;
    this.silenceThreshold = 2000; // 默认2秒沉默视为一段语音结束，停顿检测更准确
    // 语句处理
    this.sentenceBuffer = []; // 存储完整的句子
    this.autoCompleteTimeout = null; // 自动补全定时器
    this.vadStart = false; // 语音活动检测状态
    this.lastSentenceTime = 0; // 上一句结束时间
    // 存储开始识别时的初始文本，用于追加新识别的内容
    this.initialInputText = '';
    
    // 预连接策略：音频缓存机制
    this.audioBuffer = []; // 存储等待发送的音频数据
    this.maxBufferSize = 50; // 最大缓存数量，防止内存溢出
    
    // 🚀 新增：预初始化标志
    this.isPreInitialized = false; // 是否已预初始化
    this.preInitPromise = null; // 预初始化Promise
  }
  
  /**
   * 初始化语音识别服务
   * @param {Function} onTextRecognized - 文本识别回调
   * @param {Function} onStatusChange - 状态变化回调
   * @param {String} initialText - 开始识别前输入框中已有的文本
   * @param {Object} customConfig - 自定义配置，可在初始化时覆盖构造函数中的配置
   */
  init(onTextRecognized, onStatusChange, initialText = '', customConfig = {}) {
    this.onTextRecognized = onTextRecognized;
    this.onStatusChange = onStatusChange;
    // 保存初始文本
    this.initialInputText = initialText || '';
    // 将初始文本设置为基础文本
    if (this.initialInputText) {
      this.recognizedText = this.initialInputText;
    }
    // 更新配置（如果有提供）
    if (Object.keys(customConfig).length > 0) {
      this.config = { ...this.config, ...customConfig };
    }
  }
  
  /**
   * 更新输入框的当前内容
   * 当输入框内容手动变更时调用此方法同步状态
   * @param {String} currentText - 输入框当前内容
   */
  updateInputText(currentText) {
    // console.log('同步输入框内容到语音识别服务:', currentText);
    // 更新初始输入文本
    this.initialInputText = currentText || '';
    // 同时更新当前识别的文本，以便在语音识别继续时以此为基础
    this.recognizedText = this.initialInputText;
    // 清空句子缓冲区，确保新的识别从当前输入框内容开始
    this.sentenceBuffer = [];
    this.currentSegment = '';
  }
  
  /**
   * 🚀 检查麦克风权限状态（仅检查，不请求）
   * @returns {Promise<boolean>} 是否已获得权限
   */
  checkPermissionStatus = async () => {
    // #ifdef H5
    try {
      // 仅检查权限状态，不主动请求
      if (navigator.permissions && navigator.permissions.query) {
        const result = await navigator.permissions.query({ name: 'microphone' });
        console.log('🎤 当前麦克风权限状态:', result.state);
        return result.state === 'granted';
      }
      return false;
    } catch (error) {
      console.log('❌ 麦克风权限状态检查失败:', error);
      return false;
    }
    // #endif
    
    // #ifndef H5
    // Native环境暂不支持权限状态检查
    return false;
    // #endif
  }

  /**
   * 切换语音输入状态
   */
  toggleVoiceInput = async () => {
    if (this.isActive) {
      await this.stopRecognition();
      return;
    }
    
    // 在启动新的语音识别前，确保完全清理之前的资源
    await this.ensureResourcesCleared();
    
    this.isActive = true;
    this.currentSegment = '';
    // 确保不清空sentenceBuffer，保留之前的识别结果
    // 如果是第一次启动语音识别，初始化sentenceBuffer
    if (!this.sentenceBuffer) {
      this.sentenceBuffer = [];
    }
    this.lastSentenceTime = 0;
    this.vadStart = false;
    // 清空音频缓存
    this.audioBuffer = [];
    
    if (this.onStatusChange) {
      this.onStatusChange(this.isActive, this.recognizedText);
    }
    
    try {
      // 🚀 核心优化：预连接策略 - 立即开始连接WebSocket，不等权限获取
      console.log('🔗 立即开始预连接WebSocket...');
      const websocketPromise = this.connectWebSocket();
      
      // 🎯 新增：预初始化音频上下文（不需要权限）
      const audioContextPromise = this.preInitializeAudioContext();
      
      // 请求录音权限（与WebSocket连接和音频上下文初始化并行进行）
      // #ifdef H5
      try {
        // 🎤 检查是否已有权限（仅检查，不请求）
        let hasPermission = false;
        try {
          hasPermission = await this.checkPermissionStatus();
        } catch (e) {
          console.log('权限状态检查失败，假定没有权限');
          hasPermission = false;
        }
        
        // 🚀 不显示任何提示，静默处理
        
        // 🚀 等待音频上下文预初始化完成（在请求权限前完成）
        await audioContextPromise;
        
        // 检查浏览器是否支持媒体设备API
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          // 兼容旧版浏览器
          const getUserMedia = navigator.getUserMedia ||
                          navigator.webkitGetUserMedia ||
                          navigator.mozGetUserMedia ||
                          navigator.msGetUserMedia;
          
          if (!getUserMedia) {
            throw new Error('您的浏览器不支持录音功能，请使用移动应用或现代浏览器');
          }
          
          // 使用旧版API获取麦克风权限
          await new Promise((resolve, reject) => {
            getUserMedia({audio: true}, (stream) => {
              this.audioStream = stream;
              // 🎯 获取到流立即开始录音，不显示提示
              console.log('🎤 获取到权限，立即开始录音...');
              resolve(stream);
            }, reject);
          });
        } else {
          // 🎯 关键优化：创建一个Promise来监听权限请求
          const permissionPromise = new Promise(async (resolve, reject) => {
            try {
              // 请求麦克风权限
              const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                  echoCancellation: true,
                  noiseSuppression: true,
                  autoGainControl: true,
                  sampleRate: 16000,
                  channelCount: 1
                }
              });
              
              // 🎯 获取到流的瞬间立即开始录音，不显示任何提示
              console.log('🎤 获取到权限，立即开始录音...');
              
              this.audioStream = stream;
              
              // 🚀 立即开始录音，不等待resolve
              this.startRecordingWithPreInit().catch(console.error);
              
              resolve(stream);
            } catch (error) {
              reject(error);
            }
          });
          
          // 等待权限获取
          await permissionPromise;
        }
        
        // 录音已经在上面开始了，这里只是确保完成
        console.log('🎤 确保录音已开始...');
        if (!this.audioInput) {
          await this.startRecordingWithPreInit();
        }
        
      } catch (permissionError) {
        console.error('麦克风权限错误:', permissionError);
        
        // 根据错误类型提供更具体的错误信息
        if (permissionError.name === 'NotAllowedError' || permissionError.name === 'PermissionDeniedError') {
          throw new Error('麦克风权限被拒绝，请允许访问麦克风后重试');
        } else if (permissionError.name === 'NotFoundError' || permissionError.name === 'DevicesNotFoundError') {
          throw new Error('未检测到麦克风设备，请检查设备连接');
        } else if (permissionError.name === 'NotReadableError' || permissionError.name === 'TrackStartError') {
          throw new Error('麦克风设备正在被其他应用使用，请关闭其他录音应用后重试');
        } else if (permissionError.name === 'OverconstrainedError' || permissionError.name === 'ConstraintNotSatisfiedError') {
          throw new Error('设备不满足音频要求，请尝试使用其他设备');
        } else if (permissionError.name === 'TypeError') {
          throw new Error('音频参数有误，请刷新页面后重试');
        } else if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
          throw new Error('非安全连接下无法使用麦克风，请使用HTTPS连接');
        } else {
          throw new Error('未能获取麦克风权限: ' + (permissionError.message || '未知错误'));
        }
      }
      // #endif
      
      // #ifndef H5
      try {
        // Native环境：权限获取成功后立即开始录音，不显示提示
        // 立即开始录音（WebSocket已经在预连接中）
        await this.startRecording();
        
      } catch (permissionError) {
        throw new Error('未获得录音权限');
      }
      // #endif
      
      // 等待WebSocket连接完成（在后台并行进行）
      console.log('等待语音识别服务连接...');
      await websocketPromise;
      console.log('语音识别服务连接成功');
      
      // 发送缓存的音频数据（如果有）
      this.flushAudioBuffer();
      
    } catch (error) {
      console.error('语音识别启动失败:', error);
      this.isActive = false;
      
      if (this.onStatusChange) {
        this.onStatusChange(this.isActive, this.recognizedText);
      }
      
      // 只在出错时显示提示
      uni.showToast({
        title: error.message || '语音识别启动失败',
        icon: 'none'
      });
    }
  }
  
  /**
   * 🚀 预初始化音频上下文（不需要权限）
   */
  preInitializeAudioContext = async () => {
    // #ifdef H5
    try {
      if (!this.isPreInitialized) {
        console.log('🎵 开始预初始化音频上下文...');
        
        // 创建音频上下文
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (!AudioContext) {
          throw new Error('您的浏览器不支持 AudioContext，无法进行录音');
        }
        
        this.audioContext = new AudioContext({
          sampleRate: 16000
        });
        
        // 创建处理节点
        this.scriptProcessor = this.audioContext.createScriptProcessor(2048, 1, 1);
        
        // 预设置音频处理事件
        this.scriptProcessor.onaudioprocess = (event) => {
          // 检查是否已停止
          if (!this.isActive) {
            console.log('🛑 检测到非活动状态，停止音频处理');
            return;
          }
          
          // 获取音频数据
          const inputData = event.inputBuffer.getChannelData(0);
          const inputData16 = new Int16Array(inputData.length);
          
          // 转换为PCM 16-bit
          for (let i = 0; i < inputData.length; ++i) {
            inputData16[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7FFF;
          }
          
          // 检查是否有有效音频（非全静音）
          let hasAudio = false;
          for (let i = 0; i < inputData16.length; i++) {
            if (Math.abs(inputData16[i]) > 500) {
              hasAudio = true;
              break;
            }
          }
          
          // 更新是否正在听的状态
          if (hasAudio && !this.isListening) {
            this.isListening = true;
          }
          
          // 发送音频数据 - 使用音频缓存机制
          this.sendAudioData(inputData16.buffer);
        };
        
        this.isPreInitialized = true;
        console.log('✅ 音频上下文预初始化完成');
      }
    } catch (error) {
      console.error('❌ 音频上下文预初始化失败:', error);
      throw error;
    }
    // #endif
  }

  /**
   * 🚀 使用预初始化的音频上下文开始录音
   */
  startRecordingWithPreInit = async () => {
    // #ifdef H5
    try {
      // 防止重复执行
      if (this.audioInput) {
        console.log('📌 录音已经开始，跳过重复初始化');
        return;
      }
      
      console.log('🎤 使用预初始化的音频上下文开始录音...');
      
      // 验证音频流是否已准备好
      if (!this.audioStream) {
        throw new Error('音频流未准备好');
      }
      
      // 创建音频输入源并立即连接（不等待验证）
      this.audioInput = this.audioContext.createMediaStreamSource(this.audioStream);
      
      // 立即连接节点
      this.audioInput.connect(this.scriptProcessor);
      this.scriptProcessor.connect(this.audioContext.destination);
      
      console.log('✅ 音频处理节点连接完成，开始实时录音');
      
      // 后续验证（不阻塞录音）
      setTimeout(() => {
        const audioTracks = this.audioStream.getAudioTracks();
        audioTracks.forEach((track, index) => {
          console.log(`音频轨道 ${index}:`, {
            kind: track.kind,
            label: track.label,
            readyState: track.readyState,
            enabled: track.enabled
          });
        });
      }, 100);
      
    } catch (error) {
      console.error('❌ 快速录音启动失败:', error);
      // 如果快速启动失败，回退到普通启动
      await this.startRecording();
    }
    // #endif
  }
  
  /**
   * 确保所有资源都已清理
   */
  ensureResourcesCleared = async () => {
    try {
      // 如果还有之前的资源，强制清理
      if (this.audioContext || this.audioStream || this.websocket) {
        console.log('发现残留资源，强制清理...');
        await this.stopAudioResources();
        this.closeWebSocketConnection();
      }
      
      // 清除所有定时器
      if (this.silenceTimeout) {
        clearTimeout(this.silenceTimeout);
        this.silenceTimeout = null;
      }
      if (this.autoCompleteTimeout) {
        clearTimeout(this.autoCompleteTimeout);
        this.autoCompleteTimeout = null;
      }
      if (this.noSpeechTimeout) {
        clearTimeout(this.noSpeechTimeout);
        this.noSpeechTimeout = null;
      }
      
      // 重置关键状态
      this.isListening = false;
      this.vadStart = false;
      this.stoppedDueToSilence = false;
      
      console.log('资源清理完成');
    } catch (error) {
      console.error('资源清理失败:', error);
    }
  }
  
  /**
   * 连接WebSocket
   */
  connectWebSocket = () => {
    return new Promise((resolve, reject) => {
      try {
        // 构建WebSocket URL
        const socketUrl = `${this.config.url}?token=${this.config.token}`;
        console.log('连接WebSocket:', socketUrl);
        
        // 创建WebSocket连接
        this.websocket = new WebSocket(socketUrl);
        
        // WebSocket连接成功
        this.websocket.onopen = () => {
          console.log('WebSocket已连接');
          
          // 发送开始转写消息
          const startTranscriptionMessage = {
            header: {
              appkey: this.config.appkey,
              namespace: "SpeechTranscriber",
              name: "StartTranscription",
              task_id: generateUUID(),
              message_id: generateUUID()
            },
            payload: {
              format: this.config.format,
              sample_rate: this.config.sample_rate,
              enable_intermediate_result: this.config.enable_intermediate_result,
              enable_punctuation_prediction: this.config.enable_punctuation_prediction,
              enable_inverse_text_normalization: this.config.enable_inverse_text_normalization,
              enable_voice_detection: this.config.enable_voice_detection,
              max_start_silence: this.config.max_start_silence,
              max_end_silence: this.config.max_end_silence,
              speech_noise_threshold: this.config.speech_noise_threshold,
              speech_volume_threshold: this.config.speech_volume_threshold
            }
          };
          
          console.log('发送开始转写消息:', JSON.stringify(startTranscriptionMessage));
          this.websocket.send(JSON.stringify(startTranscriptionMessage));
        };
        
        // 接收WebSocket消息
        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('接收到消息:', message);
            
            // 处理不同类型的消息
            if (message.header && message.header.name) {
              switch (message.header.name) {
                case "TranscriptionStarted":
                  console.log('识别任务开始');
                  break;
                
                case "SentenceBegin":
                  // 句子开始事件
                  console.log('句子开始');
                  this.vadStart = true;
                  this.isListening = true;
                  // 是否与上一句有较长停顿
                  const now = Date.now();
                  if (this.lastSentenceTime > 0 && (now - this.lastSentenceTime > this.silenceThreshold)) {
                    // 如果停顿超过阈值，清空当前文本，开始新的语句
                    this.resetCurrentSegment();
                  }
                  break;
                
                case "SentenceEnd":
                  // 句子结束事件
                  console.log('句子结束');
                  this.vadStart = false;
                  this.isListening = false;
                  this.lastSentenceTime = Date.now();
                  
                  // 检查payload中是否有结果，优先使用payload中的result
                  if (message.payload && message.payload.result && message.payload.result.trim()) {
                    // 使用SentenceEnd事件中的最终结果
                    this.currentSegment = message.payload.result;
                    console.log('使用SentenceEnd中的结果:', this.currentSegment);
                  }
                  
                  // 如果当前段有内容，添加到句子缓冲区
                  if (this.currentSegment && this.currentSegment.trim()) {
                    this.finalizeCurrentSegment();
                  }
                  break;
                
                case "TranscriptionResultChanged":
                  // 中间识别结果
                  if (message.payload && message.payload.result) {
                    // 更新当前语音段的临时结果
                    this.currentSegment = message.payload.result;
                    // 更新最后说话时间
                    this.lastSpeechTime = Date.now();
                    
                    // 重启沉默检测
                    this.restartSilenceDetection();
                    
                    // 构建完整结果
                    this.updateRecognizedText(false);
                  }
                  break;
                  
                case "TranscriptionCompleted":
                  // 最终识别结果
                  if (message.payload && message.payload.result) {
                    // 确认当前语音段已经完成
                    const finalSegment = message.payload.result;
                    
                    // 如果有内容，添加到句子缓冲区
                    if (finalSegment.trim()) {
                      this.currentSegment = finalSegment;
                      // finalizeCurrentSegment内部会调用updateRecognizedText
                      this.finalizeCurrentSegment();
                      
                      // 更新最后说话时间
                      this.lastSpeechTime = Date.now();
                      this.lastSentenceTime = Date.now();
                      
                      // 清空当前段已在finalizeCurrentSegment中完成
                      // 不需要重复调用updateRecognizedText，因为finalizeCurrentSegment已经调用了
                    }
                  }
                  break;
                  
                case "TaskFailed":
                  // 任务失败
                  const errorMsg = message.payload && message.payload.error_message 
                    ? message.payload.error_message 
                    : '语音识别失败';
                  
                  console.error('识别任务失败:', errorMsg);
                  // 判断是否是静音超时导致的失败
                  const isNoSpeechTimeout = message.payload && 
                    (message.payload.status_text === 'MAX_START_SILENCE_TIMEOUT' ||
                     errorMsg.includes('silence') || 
                     errorMsg.includes('静音'));
                  // 根据错误类型显示不同的提示
                  // uni.showToast({
                  //   title: isNoSpeechTimeout ? '未检测到语音，已自动停止' : errorMsg,
                  //   icon: 'none'
                  // });
                  console.error(isNoSpeechTimeout ? '未检测到语音，已自动停止' : errorMsg);
                  // 标记是否因为静音超时而停止
                  this.stoppedDueToSilence = isNoSpeechTimeout;
                  
                  // 停止录音
                  this.stopRecognition();
                  break;
                  
                default:
                  // 其他消息
                  console.log('收到其他类型消息:', message.header.name);
              }
            }
          } catch (error) {
            console.error('解析消息失败:', error);
          }
        };
        
        // WebSocket连接错误
        this.websocket.onerror = (event) => {
          console.error('WebSocket错误:', event);
          reject(new Error('WebSocket连接错误'));
        };
        
        // WebSocket连接关闭
        this.websocket.onclose = () => {
          console.log('WebSocket已关闭');
          // 如果还在录音状态，停止录音
          if (this.isActive) {
            this.stopRecognition();
          }
        };
        
        // 设置超时
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'));
        }, 8000); // 8秒超时
        
        // 等待TranscriptionStarted消息
        const messageHandler = (event) => {
          try {
            const message = JSON.parse(event.data);
            if (message.header && message.header.name === "TranscriptionStarted") {
              clearTimeout(timeout);
              this.websocket.removeEventListener('message', messageHandler);
              resolve();
            }
          } catch (error) {
            console.error('解析消息失败:', error);
          }
        };
        
        this.websocket.addEventListener('message', messageHandler);
        
      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }
  
  /**
   * 重启沉默检测
   * 如果用户停止说话一段时间，将当前识别结果固定下来
   */
  restartSilenceDetection() {
    // 清除之前的定时器
    if (this.silenceTimeout) {
      clearTimeout(this.silenceTimeout);
    }
    
    // 设置新的沉默检测
    this.silenceTimeout = setTimeout(() => {
      // 只有在没有检测到VAD结束事件时才处理
      if (this.vadStart && this.currentSegment.trim()) {
        console.log('沉默检测：固定当前段落');
        this.finalizeCurrentSegment();
        // 不需要再次调用updateRecognizedText，因为finalizeCurrentSegment已经调用了
      } else if (!this.vadStart && this.currentSegment.trim()) {
        // 如果VAD已结束但仍有未处理的文本，确保它被添加到结果中
        console.log('沉默检测：处理VAD结束后的剩余文本');
        this.finalizeCurrentSegment();
      }
    }, this.silenceThreshold);
  }
  
  /**
   * 完成当前语音段并添加到句子缓冲区
   */
  finalizeCurrentSegment() {
    if (!this.currentSegment || !this.currentSegment.trim()) {
      return;
    }
    
    // 添加到句子缓冲区
    this.sentenceBuffer.push(this.currentSegment.trim());
    
    // 不再限制句子缓冲区的大小，确保所有识别的文本都被保留
    // 如果需要优化性能，可以考虑在达到一定长度后将早期内容合并为一个字符串
    // // 如果句子过多，保留最近的几句
    // if (this.sentenceBuffer.length > 5) {
    //   this.sentenceBuffer = this.sentenceBuffer.slice(-5);
    // }
    
    // 清空当前段
    this.currentSegment = '';
    
    // 立即更新识别文本，确保最新结果被显示
    this.updateRecognizedText(true);
  }
  
  /**
   * 重置当前语音段
   */
  resetCurrentSegment() {
    this.currentSegment = '';
    // 不清空句子缓冲区，但记录当前是新的语音输入
    this.isNewSpeech = true;
  }
  
  /**
   * 更新完整识别文本
   * @param {boolean} isFinal - 是否为最终结果
   */
  updateRecognizedText(isFinal = false) {
    // 构建完整文本
    let fullText = '';
    // 如果有初始文本，从初始文本开始
    if (this.initialInputText) {
      fullText = this.initialInputText;
      // 如果初始文本不是以空格结尾，并且不为空，添加一个空格作为分隔
      if (fullText.trim() && !fullText.endsWith(' ')) {
        fullText += ' ';
      }
    }
    
    // 从句子缓冲区构建已确认的文本
    if (this.sentenceBuffer.length > 0) {
      fullText += this.sentenceBuffer.join('');
    }
    
    // 如果当前段有内容，添加到完整文本
    if (this.currentSegment && this.currentSegment.trim()) {
      fullText += this.currentSegment;
    }
    
    // 记录更新前后的文本，便于调试
    const oldText = this.recognizedText;
    this.recognizedText = fullText;
    
    // 如果文本有变化，记录日志
    if (oldText !== fullText) {
      console.log(`文本已更新 [${isFinal ? '最终' : '中间'}]：`, fullText);
      console.log('初始文本:', this.initialInputText);
      console.log('句子缓冲区长度:', this.sentenceBuffer.length);
      console.log('句子缓冲区最后一项:', this.sentenceBuffer.length > 0 ? this.sentenceBuffer[this.sentenceBuffer.length - 1] : '');
      console.log('当前段:', this.currentSegment);
    }
    
    // 如果是最终结果，并且句子缓冲区过长，考虑优化内存使用
    if (isFinal && this.sentenceBuffer.length > 20) {
      // 将前面的句子合并为一个字符串，保留最近的几个句子
      const preserveCount = 5; // 保留最近的5个句子
      const mergedText = this.sentenceBuffer.slice(0, -preserveCount).join('');
      this.sentenceBuffer = [mergedText, ...this.sentenceBuffer.slice(-preserveCount)];
      console.log('优化句子缓冲区，合并早期内容，当前长度:', this.sentenceBuffer.length);
    }
    
    // 调用回调函数
    if (this.onTextRecognized) {
      this.onTextRecognized(this.recognizedText, isFinal, false);
    }
    
    if (this.onStatusChange) {
      this.onStatusChange(this.isActive, this.recognizedText);
    }
  }
  
  /**
   * 开始录音
   */
  startRecording = async () => {
    try {
      // #ifdef H5
      // 在H5环境中使用Web Audio API
      try {
        console.log('🎤 开始获取麦克风权限...');
        
        // 如果已经有音频流，直接使用
        if (!this.audioStream) {
          // 获取麦克风权限 - 使用更严格的约束
          this.audioStream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
              sampleRate: 16000,
              channelCount: 1
            }
          });
        }
        
        console.log('✅ 麦克风权限获取成功，轨道数量:', this.audioStream.getTracks().length);
        
        // 如果已经预初始化，跳过重复初始化
        if (this.isPreInitialized && this.audioContext && this.scriptProcessor) {
          console.log('📌 使用预初始化的音频上下文');
          
          // 创建音频输入源
          this.audioInput = this.audioContext.createMediaStreamSource(this.audioStream);
          
          // 连接节点
          this.audioInput.connect(this.scriptProcessor);
          this.scriptProcessor.connect(this.audioContext.destination);
          
          console.log('✅ 音频处理节点连接完成（预初始化）');
          console.log('🎤 开始录音 (H5 - 预初始化)');
          return;
        }
        
        // 验证音频轨道状态
        const audioTracks = this.audioStream.getAudioTracks();
        audioTracks.forEach((track, index) => {
          console.log(`音频轨道 ${index}:`, {
            kind: track.kind,
            label: track.label,
            readyState: track.readyState,
            enabled: track.enabled
          });
        });
        
        // 创建音频上下文
        // 兼容不同浏览器的AudioContext实现
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        if (!AudioContext) {
          throw new Error('您的浏览器不支持 AudioContext，无法进行录音');
        }
        
        this.audioContext = new AudioContext({
          sampleRate: 16000
        });
        
        console.log('✅ AudioContext创建成功，状态:', this.audioContext.state);
        
        // 创建音频输入源
        this.audioInput = this.audioContext.createMediaStreamSource(this.audioStream);
        
        // 创建处理节点，兼容不同浏览器
        // 注意：ScriptProcessor已被废弃，但兼容性更好
        // 现代浏览器推荐使用AudioWorklet，但需要更复杂的兼容处理
        this.scriptProcessor = this.audioContext.createScriptProcessor(2048, 1, 1);
        
        // 设置音频处理事件
        this.scriptProcessor.onaudioprocess = (event) => {
          // 检查是否已停止 - 这是关键的安全检查
          if (!this.isActive) {
            console.log('🛑 检测到非活动状态，停止音频处理');
            return;
          }
          
          // 获取音频数据
          const inputData = event.inputBuffer.getChannelData(0);
          const inputData16 = new Int16Array(inputData.length);
          
          // 转换为PCM 16-bit
          for (let i = 0; i < inputData.length; ++i) {
            inputData16[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7FFF;
          }
          
          // 检查是否有有效音频（非全静音）
          let hasAudio = false;
          for (let i = 0; i < inputData16.length; i++) {
            if (Math.abs(inputData16[i]) > 500) { // 设置低于此阈值的视为静音
              hasAudio = true;
              break;
            }
          }
          
          // 更新是否正在听的状态
          if (hasAudio && !this.isListening) {
            this.isListening = true;
          }
          
          // 发送音频数据 - 使用音频缓存机制
          this.sendAudioData(inputData16.buffer);
        };
        
        // 连接节点
        this.audioInput.connect(this.scriptProcessor);
        this.scriptProcessor.connect(this.audioContext.destination);
        
        console.log('✅ 音频处理节点连接完成');
        console.log('🎤 开始录音 (H5)');
      } catch (error) {
        console.error('❌ H5录音初始化失败:', error);
        
        // 清理可能已创建的资源
        if (this.audioStream) {
          const tracks = this.audioStream.getTracks();
          tracks.forEach(track => track.stop());
          this.audioStream = null;
        }
        if (this.audioContext && this.audioContext.state !== 'closed') {
          try {
            this.audioContext.close();
          } catch (e) {
            console.warn('清理失败的AudioContext时出错:', e);
          }
          this.audioContext = null;
        }
        
        throw new Error('初始化录音失败: ' + (error.message || '未知错误'));
      }
      // #endif
      
      // #ifndef H5
      try {
        // 非H5环境使用uni-app的录音API
        const recorderManager = uni.getRecorderManager();
        
        // 清除之前可能存在的事件监听器
        if (this.audioContext && typeof this.audioContext.offStart === 'function') {
          this.audioContext.offStart();
          this.audioContext.offError();
          this.audioContext.offFrameRecorded();
          this.audioContext.offStop();
        }
        
        // 设置录音参数
        recorderManager.start({
          format: 'PCM',
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 16000,
          frameSize: 2048
        });
        
        // 监听录音事件
        recorderManager.onStart(() => {
          console.log('开始录音 (Native)');
        });
        
        recorderManager.onError((err) => {
          console.error('录音失败:', err);
          this.stopRecognition();
          uni.showToast({
            title: '录音失败',
            icon: 'none'
          });
        });
        
        // 监听录音停止事件
        recorderManager.onStop((res) => {
          console.log('录音已停止 (Native)');
        });
        
        // 监听录音数据
        recorderManager.onFrameRecorded((res) => {
          // 检查是否还在活动状态，避免停止后仍然发送数据
          if (!this.isActive) return;
          
          const { frameBuffer } = res;
          if (frameBuffer) {
            // 使用音频缓存机制
            this.sendAudioData(frameBuffer);
          }
        });
        
        // 保存录音管理器实例以便停止时使用
        this.audioContext = recorderManager;
      } catch (error) {
        console.error('Native录音初始化失败:', error);
        throw error;
      }
      // #endif
      
    } catch (error) {
      console.error('录音失败:', error);
      throw error;
    }
  }
  
  /**
   * 发送音频数据（支持缓存机制）
   * @param {ArrayBuffer} audioData - 音频数据
   */
  sendAudioData(audioData) {
    if (!this.isActive) return;
    
    // 检查WebSocket是否已连接
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      try {
        // 直接发送音频数据
        this.websocket.send(audioData);
        
        // 如果有缓存的音频数据，立即发送
        if (this.audioBuffer.length > 0) {
          console.log(`📦 发送缓存的音频数据，数量: ${this.audioBuffer.length}`);
          this.flushAudioBuffer();
        }
      } catch (e) {
        console.error('发送音频数据失败:', e);
        // 如果发送失败，将数据加入缓存
        this.bufferAudioData(audioData);
      }
    } else {
      // WebSocket未连接，缓存音频数据
      this.bufferAudioData(audioData);
    }
  }

  /**
   * 缓存音频数据
   * @param {ArrayBuffer} audioData - 音频数据
   */
  bufferAudioData(audioData) {
    // 检查缓存大小，防止内存溢出
    if (this.audioBuffer.length >= this.maxBufferSize) {
      console.warn(`⚠️ 音频缓存已满 (${this.maxBufferSize})，丢弃最早的数据`);
      this.audioBuffer.shift(); // 移除最早的数据
    }
    
    // 复制ArrayBuffer，避免引用问题
    const buffer = audioData.slice(0);
    this.audioBuffer.push(buffer);
    console.log(`📦 缓存音频数据，当前缓存数量: ${this.audioBuffer.length}`);
  }

  /**
   * 发送所有缓存的音频数据
   */
  flushAudioBuffer() {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket未连接，无法发送缓存数据');
      return;
    }
    
    if (this.audioBuffer.length === 0) {
      return;
    }
    
    console.log(`🚀 开始发送缓存的音频数据，数量: ${this.audioBuffer.length}`);
    
    // 发送所有缓存的音频数据
    for (let i = 0; i < this.audioBuffer.length; i++) {
      try {
        this.websocket.send(this.audioBuffer[i]);
      } catch (e) {
        console.error(`❌ 发送缓存数据失败 (${i + 1}/${this.audioBuffer.length}):`, e);
        // 保留未发送成功的数据
        this.audioBuffer = this.audioBuffer.slice(i);
        return;
      }
    }
    
    console.log('✅ 所有缓存数据发送完成');
    // 清空缓存
    this.audioBuffer = [];
  }
  
  /**
   * 停止语音识别
   */
  stopRecognition = async () => {
    try {
      // 保存状态以便判断是否需要显示提示
      const wasActive = this.isActive;
      const hadDetectedSpeech = this.hasDetectedSpeech;
      
      // 立即设置为非活动状态，阻止新的音频处理
      this.isActive = false;
      this.isListening = false;
      this.vadStart = false;
      
      // 清除所有计时器
      if (this.silenceTimeout) {
        clearTimeout(this.silenceTimeout);
        this.silenceTimeout = null;
      }
      
      if (this.autoCompleteTimeout) {
        clearTimeout(this.autoCompleteTimeout);
        this.autoCompleteTimeout = null;
      }
      if (this.noSpeechTimeout) {
        clearTimeout(this.noSpeechTimeout);
        this.noSpeechTimeout = null;
      }
      
      // 如果当前语音段有内容，添加到句子缓冲区
      if (this.currentSegment && this.currentSegment.trim()) {
        this.finalizeCurrentSegment();
        this.updateRecognizedText(true);
      }
      
      // 立即停止录音资源 - 优先处理音频资源释放
      await this.stopAudioResources();
      
      // 立即关闭WebSocket连接
      this.closeWebSocketConnection();
      
      // 立即通知状态变化，确保UI更新 - 传递静音停止标志
      if (this.onStatusChange) {
        this.onStatusChange(this.isActive, this.recognizedText, this.stoppedDueToSilence);
      }
      
      // 根据不同情况显示不同提示
      if (wasActive && !this.stoppedDueToSilence) { // 如果不是因为静音超时导致的停止，才显示提示
        if (!hadDetectedSpeech) {
          // uni.showToast({
          //   title: '未检测到语音',
          //   icon: 'none',
          //   duration: 1500
          // });
        } else {
          // uni.showToast({
          //   title: '已停止语音输入',
          //   icon: 'none',
          //   duration: 1500
          // });
        }
      }
      // 重置静音停止标志
      this.stoppedDueToSilence = false;
    } catch (error) {
      console.error('停止语音识别失败:', error);
      // 即使出错也要确保UI状态正确
      this.isActive = false;
      if (this.onStatusChange) {
        this.onStatusChange(this.isActive, this.recognizedText);
      }
    }
  }
  
  /**
   * 停止并释放所有音频资源
   */
  stopAudioResources = async () => {
    try {
      // 重置预初始化标志
      this.isPreInitialized = false;
      
      // #ifdef H5
      // H5环境下立即停止和释放所有音频资源
      await this.stopH5AudioResources();
      // #endif
      
      // #ifndef H5
      // 非H5环境立即停止录音
      await this.stopNativeAudioResources();
      // #endif
      
    } catch (error) {
      console.error('停止音频资源失败:', error);
    }
  }
  
  /**
   * 停止H5环境下的音频资源
   */
  stopH5AudioResources = async () => {
    try {
      console.log('开始停止H5音频资源...');
      
      // 第一步：立即停止所有音频轨道（这是关键，必须最先执行）
      if (this.audioStream) {
        try {
          const tracks = this.audioStream.getTracks();
          console.log('正在停止音频轨道，数量:', tracks.length);
          
          // 立即停止所有轨道
          for (let i = 0; i < tracks.length; i++) {
            const track = tracks[i];
            try {
              console.log(`音频轨道 ${i} 状态:`, track.readyState, '类型:', track.kind);
              if (track.readyState === 'live') {
                track.stop();
                console.log(`✅ 音频轨道 ${i} 已停止`);
              }
              
              // 额外确保轨道被停止
              if (track.readyState === 'live') {
                // 如果还是 live 状态，再次尝试停止
                track.enabled = false;
                track.stop();
                console.log(`🔄 音频轨道 ${i} 强制停止`);
              }
            } catch (e) {
              console.warn(`⚠️ 停止音频轨道 ${i} 失败:`, e);
            }
          }
          
          // 验证所有轨道都已停止
          const stillLiveTracks = tracks.filter(track => track.readyState === 'live');
          if (stillLiveTracks.length > 0) {
            console.warn('⚠️ 仍有活跃的音频轨道:', stillLiveTracks.length);
            // 强制停止仍然活跃的轨道
            stillLiveTracks.forEach((track, index) => {
              try {
                track.enabled = false;
                track.stop();
                console.log(`🔧 强制停止活跃轨道 ${index}`);
              } catch (e) {
                console.error(`❌ 强制停止轨道失败:`, e);
              }
            });
          } else {
            console.log('✅ 所有音频轨道已成功停止');
          }
          
        } catch (e) {
          console.warn('⚠️ 获取或停止音频轨道失败:', e);
        }
        
        // 清空流引用
        this.audioStream = null;
        console.log('🗑️ 音频流引用已清空');
      }
      
      // 第二步：断开音频处理节点
      if (this.scriptProcessor) {
        try {
          // 移除音频处理事件监听器
          this.scriptProcessor.onaudioprocess = null;
          // 断开所有连接
          this.scriptProcessor.disconnect();
          console.log('✅ ScriptProcessor已断开');
        } catch (e) {
          console.warn('⚠️ 断开scriptProcessor失败:', e);
        }
        this.scriptProcessor = null;
      }
      
      // 第三步：断开音频输入源
      if (this.audioInput) {
        try {
          // 断开音频输入源
          this.audioInput.disconnect();
          console.log('✅ AudioInput已断开');
        } catch (e) {
          console.warn('⚠️ 断开audioInput失败:', e);
        }
        this.audioInput = null;
      }
      
      // 第四步：关闭AudioContext
      if (this.audioContext) {
        try {
          const contextState = this.audioContext.state;
          console.log('AudioContext当前状态:', contextState);
          
          if (contextState !== 'closed') {
            if (typeof this.audioContext.close === 'function') {
              await this.audioContext.close();
              console.log('✅ AudioContext已关闭');
            } else if (typeof this.audioContext.suspend === 'function') {
              // 如果不支持close，立即suspend
              await this.audioContext.suspend();
              console.log('✅ AudioContext已暂停');
            }
          }
          
          // 验证AudioContext状态
          setTimeout(() => {
            if (this.audioContext && this.audioContext.state) {
              console.log('AudioContext最终状态:', this.audioContext.state);
            }
          }, 100);
          
        } catch (e) {
          console.warn('⚠️ 关闭AudioContext失败:', e);
        }
        this.audioContext = null;
      }
      
      // 第五步：额外的浏览器兼容性处理
      try {
        // 强制浏览器释放麦克风权限
        if (typeof navigator !== 'undefined' && navigator.mediaDevices) {
          // 某些浏览器需要明确触发权限释放
          console.log('🔄 触发浏览器权限释放检查...');
          
          // 通过重新检查权限状态来触发浏览器更新UI
          if (navigator.permissions && navigator.permissions.query) {
            navigator.permissions.query({name: 'microphone'}).then(result => {
              console.log('🎤 麦克风权限状态:', result.state);
            }).catch(e => {
              console.log('权限查询失败:', e);
            });
          }
        }
      } catch (e) {
        console.warn('⚠️ 浏览器兼容性处理失败:', e);
      }
      
      console.log('✅ H5音频资源停止完成');
      
    } catch (error) {
      console.error('❌ H5停止录音失败:', error);
      // 即使出错也要强制清空所有引用
      this.audioStream = null;
      this.scriptProcessor = null;
      this.audioInput = null;
      this.audioContext = null;
    }
  }
  
  /**
   * 停止Native环境下的音频资源
   */
  stopNativeAudioResources = async () => {
    try {
      // 立即停止录音管理器
      if (this.audioContext && typeof this.audioContext.stop === 'function') {
        console.log('正在停止录音管理器...');
        this.audioContext.stop();
        console.log('录音管理器已停止');
        
        // 移除所有事件监听器，确保完全清理
        if (typeof this.audioContext.offStart === 'function') {
          this.audioContext.offStart();
        }
        if (typeof this.audioContext.offError === 'function') {
          this.audioContext.offError();
        }
        if (typeof this.audioContext.offFrameRecorded === 'function') {
          this.audioContext.offFrameRecorded();
        }
        if (typeof this.audioContext.offStop === 'function') {
          this.audioContext.offStop();
        }
        
        this.audioContext = null;
      }
      
      // 额外的Native平台资源清理
      // #ifdef APP-PLUS
      try {
        // 确保录音权限被正确释放（Android特有）
        if (plus && plus.android) {
          // 在Android平台上，确保录音资源被释放
          console.log('Android平台：确保录音资源释放');
        }
      } catch (e) {
        console.warn('Android平台资源清理失败:', e);
      }
      // #endif
      
    } catch (error) {
      console.error('Native停止录音失败:', error);
    }
  }
  
  /**
   * 立即关闭WebSocket连接
   */
  closeWebSocketConnection = () => {
    if (this.websocket) {
      try {
        const currentState = this.websocket.readyState;
        console.log('WebSocket当前状态:', currentState);
        
        if (currentState === WebSocket.OPEN) {
          // 发送停止转写消息
          const stopTranscriptionMessage = {
            header: {
              appkey: defaultVoiceRecognitionConfig.appkey,
              namespace: "SpeechTranscriber",
              name: "StopTranscription",
              task_id: generateUUID(),
              message_id: generateUUID()
            },
            payload: {}
          };
          
          try {
            this.websocket.send(JSON.stringify(stopTranscriptionMessage));
            console.log('已发送停止转写消息');
          } catch (e) {
            console.warn('发送停止消息失败:', e);
          }
        }
        
        // 清除所有事件监听器
        this.websocket.onopen = null;
        this.websocket.onmessage = null;
        this.websocket.onerror = null;
        this.websocket.onclose = null;
        
        // 立即关闭连接，不等待延迟
        if (currentState === WebSocket.OPEN || currentState === WebSocket.CONNECTING) {
          this.websocket.close(1000, '用户主动停止');
          console.log('WebSocket连接已立即关闭');
        }
        
        this.websocket = null;
      } catch (error) {
        console.error('关闭WebSocket连接失败:', error);
        // 强制清空引用
        this.websocket = null;
      }
    }
  }
  
  /**
   * 获取当前识别文本
   */
  getRecognizedText() {
    return this.recognizedText;
  }
  
  /**
   * 获取当前活动状态
   */
  isVoiceInputActive() {
    return this.isActive;
  }
  
  /**
   * 重置语音会话
   * 开始新的语音识别会话时调用
   */
  resetSession() {
    // 如果还在录音状态，先停止录音
    if (this.isActive) {
      this.stopRecognition();
    }
    
    // 强制清理所有资源
    this.forceCleanupResources();
    
    // 重置所有状态变量
    this.hasNewSession = true;
    this.previousText = '';
    this.accumulatedResults = [];
    this.currentSegment = '';
    this.recognizedText = ''; // 发送按钮点击后重置文本
    this.sentenceBuffer = [];
    this.isListening = false;
    this.vadStart = false;
    this.lastSentenceTime = 0;
    this.hasDetectedSpeech = false;
    this.stoppedDueToSilence = false; // 重置静音停止标志
    this.initialInputText = ''; // 重置初始输入文本
    this.isActive = false;
    this.isPreInitialized = false; // 重置预初始化标志
    
    console.log('语音会话已重置');
  }
  
  /**
   * 强制清理所有资源（同步方法）
   */
  forceCleanupResources() {
    try {
      // 清除所有定时器
      if (this.silenceTimeout) {
        clearTimeout(this.silenceTimeout);
        this.silenceTimeout = null;
      }
      if (this.autoCompleteTimeout) {
        clearTimeout(this.autoCompleteTimeout);
        this.autoCompleteTimeout = null;
      }
      if (this.noSpeechTimeout) {
        clearTimeout(this.noSpeechTimeout);
        this.noSpeechTimeout = null;
      }
      
      // 强制清理音频资源
      // #ifdef H5
      if (this.audioStream) {
        try {
          const tracks = this.audioStream.getTracks();
          console.log('🔧 强制停止音频轨道，数量:', tracks.length);
          tracks.forEach((track, index) => {
            try {
              if (track.readyState === 'live') {
                track.enabled = false; // 先禁用
                track.stop(); // 再停止
                console.log(`🔧 强制停止音频轨道 ${index}`);
              }
            } catch (e) {
              console.warn(`⚠️ 强制停止音频轨道 ${index} 失败:`, e);
            }
          });
        } catch (e) {
          console.warn('⚠️ 强制清理audioStream失败:', e);
        }
        this.audioStream = null;
      }
      
      if (this.scriptProcessor) {
        try {
          this.scriptProcessor.onaudioprocess = null;
          this.scriptProcessor.disconnect();
          console.log('🔧 强制清理ScriptProcessor完成');
        } catch (e) {
          console.warn('⚠️ 强制清理scriptProcessor失败:', e);
        }
        this.scriptProcessor = null;
      }
      
      if (this.audioInput) {
        try {
          this.audioInput.disconnect();
          console.log('🔧 强制清理AudioInput完成');
        } catch (e) {
          console.warn('⚠️ 强制清理audioInput失败:', e);
        }
        this.audioInput = null;
      }
      
      if (this.audioContext) {
        try {
          if (this.audioContext.state !== 'closed') {
            if (typeof this.audioContext.close === 'function') {
              this.audioContext.close();
              console.log('🔧 强制关闭AudioContext');
            } else if (typeof this.audioContext.suspend === 'function') {
              this.audioContext.suspend();
              console.log('🔧 强制暂停AudioContext');
            }
          }
        } catch (e) {
          console.warn('⚠️ 强制关闭audioContext失败:', e);
        }
        this.audioContext = null;
      }
      // #endif
      
      // #ifndef H5
      if (this.audioContext && typeof this.audioContext.stop === 'function') {
        try {
          this.audioContext.stop();
          // 移除事件监听器
          if (typeof this.audioContext.offStart === 'function') {
            this.audioContext.offStart();
            this.audioContext.offError();
            this.audioContext.offFrameRecorded();
            this.audioContext.offStop();
          }
        } catch (e) {
          console.warn('强制停止录音管理器失败:', e);
        }
        this.audioContext = null;
      }
      // #endif
      
      // 强制关闭WebSocket
      if (this.websocket) {
        try {
          this.websocket.onopen = null;
          this.websocket.onmessage = null;
          this.websocket.onerror = null;
          this.websocket.onclose = null;
          
          if (this.websocket.readyState === WebSocket.OPEN || this.websocket.readyState === WebSocket.CONNECTING) {
            this.websocket.close(1000, '会话重置');
          }
        } catch (e) {
          console.warn('强制关闭WebSocket失败:', e);
        }
        this.websocket = null;
      }
      
      console.log('强制资源清理完成');
    } catch (error) {
      console.error('强制资源清理失败:', error);
    }
  }
}

/**
 * 创建语音识别服务实例
 * @param {Object} customConfig - 自定义配置，可覆盖默认配置
 * @returns {VoiceRecognitionService} 语音识别服务实例
 */
export const createVoiceRecognitionService = (customConfig = {}) => {
  return new VoiceRecognitionService(customConfig);
};

// 创建默认单例实例（保持向后兼容）
const voiceRecognitionService = new VoiceRecognitionService();

// 默认导出单例实例（保持向后兼容）
export default voiceRecognitionService;