<template>
  <view class="result-item">
    <!-- 主要内容区域 -->
    <view class="item-main" @click="handleLiItemClick">
      <!-- 左侧图标/复选框 -->
      <view class="item-left">
        <!-- 待办项的复选框 -->
        <checkbox-group v-if="type === 'todo'" @change="handleCheckboxChange">
          <checkbox
            :value="item.id.toString()"
            :checked="item.is_finished === 1"
            color="#4CAF50"
          />
        </checkbox-group>
        <!-- 其他类型的图标 -->
        <image v-else class="item-icon" :src="getItemIcon()" />
      </view>

      <!-- 内容区域 -->
      <view class="item-content">
        <!-- 标题 -->
        <view class="item-title">{{ getItemTitle() }}</view>

        <!-- 副标题/详情 -->
        <view class="item-subtitle">
          <!-- 待办项的时间和关联信息 -->
          <template v-if="type === 'todo'">
            <view v-if="item.notify_cycle === 0" class="time-info">
              <view class="time-text">{{ filterDateTime(item.due_date) }}</view>
              <view v-if="getOverdueText(item.due_date)" class="overdue-text">
                {{ getOverdueText(item.due_date) }}
              </view>
            </view>
            <view v-else class="notify-info">
              {{ getNotifyCycle(item.notify_cycle, item.notify_time) }}
            </view>
            <view class="related-info">
              {{ item.business?.title || item.business?.content || item.customer?.company_short_name || "-" }}
            </view>
          </template>

          <!-- 客户项的联系人信息 -->
          <template v-else-if="type === 'customer'">
            <view v-if="item.contact_name" class="contact-name">{{ item.contact_name }}</view>
            <view class="last-todo">{{ item.last_todo_time || 0 }}</view>
          </template>

          <!-- 商机项的时间和状态 -->
          <template v-else-if="type === 'business'">
            <view class="business-info">
              <view v-if="item.todo?.due_date" class="business-time">
                {{ filterDateTime(item.todo?.due_date) }}
              </view>
              <view class="todo-text">{{ item.todo?.title || "-" }}</view>
            </view>
            <view class="status-text">{{ getStatusText(item.status) || "-" }}</view>
          </template>

          <!-- 文件项的更新时间 -->
          <template v-else-if="type === 'file'">
            <view v-if="item.updated_at" class="file-time">{{ item.updated_at }}</view>
          </template>
        </view>
      </view>

      <!-- 右侧操作按钮（仅文件类型） -->
      <view v-if="type === 'file'" class="item-action">
        <uni-icons type="eye" size="20" color="#E2E4E9" @click.stop="handleLiItemClick" />
      </view>
    </view>

    <!-- 专门的下划线DOM（最后一条不显示） -->
    <view v-if="!isLast" class="item-divider"></view>
  </view>
</template>

<script setup>
import { 
  getNotifyCycle,
  filterDateTime,
  getOverdueText,
} from "@/utils/formatTime.js";
import { getFileIconPath } from "@/utils/fileUtils.js";

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['todo', 'customer', 'business', 'file'].includes(value)
  },
  stages: {
    type: Array,
    default: () => []
  },
  isLast: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'checkbox-change']);

// 获取项目图标
const getItemIcon = () => {
  const iconMap = {
    customer: '/static/tabBar/customer.png',
    business: '/static/global/briefcase.svg',
    file: getFileIconPath(props.item.name)
  };
  return iconMap[props.type] || '';
};

// 获取项目标题
const getItemTitle = () => {
  const titleMap = {
    todo: props.item.title,
    customer: props.item.company_name,
    business: props.item.title,
    file: props.item.name
  };
  return titleMap[props.type] || '';
};

// 获取商机状态文本
const getStatusText = (status) => {
  const stage = props.stages.find((stage) => stage.value === status);
  return stage ? stage.text : "未知状态";
};

// 处理复选框变化
const handleCheckboxChange = (evt) => {
  emit('checkbox-change', evt, props.item);
};

// 处理项目点击
const handleLiItemClick = () => {
  emit('click', props.item, props.type);
};
</script>

<style lang="scss" scoped>
// 导入通用样式
@import "/styles/search-common.scss";

.result-item {
  // 继承基础样式但移除一些属性
  @include flex-center;
  flex-direction: column; // 改为垂直布局以容纳下划线
  cursor: pointer;
  min-height: 44px;
  margin: 0 8px;

  .item-main {
    @include flex-center;
    width: 100%;
    margin: $spacing-lg $spacing-xxl $spacing-xs $spacing-xxl;

    .item-left {
      @include flex-center;
      margin-right: 6px;

      .item-icon {
        width: $icon-size-md;
        height: $icon-size-md;
      }
    }

    .item-content {
      flex: 1;
      // 移除 border-bottom，不再需要
      padding-bottom: $spacing-xs;
      overflow: hidden;

      .item-title {
        color: $primary-color;
        font-size: 14px;
        @include text-ellipsis;
        margin-bottom: $spacing-xs;
      }

      .item-subtitle {
        @include flex-center;
        color: $text-light;
        font-size: 12px;
        line-height: 16px;

        .time-info {
          @include flex-center;
          margin-right: $spacing-sm;

          .time-text {
            min-width: 95px;
          }

          .overdue-text {
            color: $error-color;
            margin-left: $spacing-sm;
            min-width: 50px;
          }
        }

        .notify-info {
          margin-right: $spacing-sm;
        }

        .related-info {
          @include text-ellipsis;
        }

        .contact-name {
          margin-right: $spacing-md;
        }

        .business-info {
          @include flex-center;
          max-width: 62vw;
          margin-right: $spacing-md;

          .business-time {
            min-width: 95px;
            margin-right: 4px;
          }

          .todo-text {
            @include text-ellipsis;
          }
        }

        .status-text {
          margin-left: auto;
        }

        .file-time {
          color: $text-light;
        }
      }
    }

    .item-action {
      @include flex-center;
      margin-left: $spacing-md;
    }
  }

  // 专门的下划线样式
  .item-divider {
    width: calc(100% - 18px); // 左右各留18px边距
    height: 1px;
    background-color: $divider-color;
    margin: 0 0 0 35px;
  }
}
</style>
