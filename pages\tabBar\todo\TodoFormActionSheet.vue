<template>
  <!-- 【新建】代办 -->
  <ActionSheet
    v-model:show="showForm"
    :custom-panel="true"
    :close-on-click-mask="false"
    @cancel="handleCancel"
  >
    <template #custom-panel>
      <!-- 顶部操作栏 -->
      <view class="form-header">
        <view class="form-header-btn" @click="handleCancel">取消</view>
        <view class="form-header-title">{{ title }}</view>
        <view class="form-header-btn confirm-btn" @click="handleSubmit">
          {{ confirmText }}
        </view>
      </view>
      <!-- 表单内容 -->
      <scroll-view scroll-y class="form-content">
        <uni-forms
          class="task-form"
          ref="formRef"
          :model-value="formData"
          :rules="rules"
          validate-trigger="submit"
          label-width="75px"
        >
          <uni-forms-item label="标题" name="title" required>
            <uni-easyinput v-model="formData.title" placeholder="请输入标题" />
          </uni-forms-item>
          <uni-forms-item label="截止时间">
            <DateTimePicker v-model="formData.due_date" type="datetime" />
          </uni-forms-item>
          <uni-forms-item label="提醒时间">
            <DateTimePicker v-model="formData.notify_time" 
              :type="timeOnly" 
              placeholder="请选择提醒时间"
            />
          </uni-forms-item>
          <uni-forms-item label="重复提醒" name="priority">
            <uni-data-select
              v-model="formData.notify_cycle"
              :localdata="reminderList"
              :clear="false"
              @change="handeReminderChange"
            ></uni-data-select>
          </uni-forms-item>
          <uni-forms-item label="优先级" name="priority">
            <uni-data-select
              v-model="formData.priority"
              :localdata="priorityList"
            ></uni-data-select>
          </uni-forms-item>
          <uni-forms-item label="关联客户" name="customer_id">
            <uni-data-select
              v-model="formData.customer_id"
              :localdata="oldCustomerList"
            ></uni-data-select>
          </uni-forms-item>
          <uni-forms-item label="关联商机" name="bo_id">
            <uni-data-select
              v-model="formData.bo_id"
              :localdata="oldBusinessList"
            ></uni-data-select>
          </uni-forms-item>
          <uni-forms-item label="备注" name="description">
            <uni-easyinput
              v-model="formData.description"
              placeholder="请输入备注"
              type="textarea"
            />
          </uni-forms-item>
        </uni-forms>
      </scroll-view>
    </template>
  </ActionSheet>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from "vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import { getCustomerList } from "@/http/customer.js";
import { fetchOpportunityList } from "@/http/business.js";
import DateTimePicker from "@/components/date-time-picker/date-time-picker.vue";

const emit = defineEmits(["update:show", "submit", "cancel"]);
const props = defineProps({
  // 是否显示
  show: Boolean,
  // 标题
  title: {
    type: String,
    default: "新增待办",
  },
  // 确认按钮文字
  confirmText: {
    type: String,
    default: "确定",
  },
  // 初始表单数据
  initialData: {
    type: Object,
    default: () => ({}),
  },
});
const showForm = ref(props.show);
const isSubmitting = ref(false);
const timeOnly = ref('datetime');  // 是否只显示时间
const formRef = ref(null);
const reminderList = ref([
  { value: 0, text: "无" },
  { value: 1, text: "每天" },
  { value: 2, text: "工作日" },
  { value: 3, text: "周末" },
  { value: 4, text: "每周" },
  { value: 5, text: "每两周" },
  { value: 6, text: "每月" },
  { value: 7, text: "每三个月" },
  { value: 8, text: "每六个月" },
  { value: 9, text: "每年" },
]);
const priorityList = ref([
  { value: 30, text: "高" },
  { value: 20, text: "中" },
  { value: 10, text: "低" },
]);
const oldCustomerList = ref([]);
const oldBusinessList = ref([]);

// 表单数据
const formData = reactive({
  title: "",
  notify_cycle: 0,
  due_date: null,
  notify_time: null,
  description: "",
  customer_id: undefined,
  priority: 0,
  bo_id: 0,
  ...props.initialData,
});

// 验证规则
const rules = reactive({
  title: {
    rules: [{ required: true, errorMessage: "请输入标题" }],
  },
  // due_date: {
  //   rules: [
  //     {
  //       required: true,
  //       errorMessage: "请选择时间",
  //       validate: (value) => {
  //         // 验证时间戳有效性
  //         return value !== null && value > 0;
  //       },
  //     },
  //     {
  //       validate: (value) => value > Date.now(),
  //       errorMessage: "时间不能早于当前时间",
  //     },
  //   ],
  // },
});

// 监听show变化
watch(
  () => props.show,
  (val) => {
    showForm.value = val;
  }
);

// 监听组件内部show变化
watch(showForm, (val) => {
  emit("update:show", val);
  if (!val) {
    resetForm();
  }
});

// 处理提醒时间变化
const handeReminderChange = (val) => {
  if (val!==0) {
    timeOnly.value = 'time';
  } else {
    timeOnly.value = 'datetime';
  }
};

// 提交表单
const handleSubmit = async () => {
  if (isSubmitting.value) return;
  isSubmitting.value = true;
  try {
    // 验证表单
    const validate = await formRef.value.validate();
    // 验证通过，提交数据
    emit("submit", { ...formData });
    showForm.value = false;
  } catch (err) {
    console.log("表单验证失败:", err);
  } finally {
    isSubmitting.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit("cancel");
  showForm.value = false;
};

// 重置表单
const resetForm = () => {
  formRef.value.clearValidate();
  Object.assign(formData, {
    title: "",
    due_date: null,
    description: "",
    priority: 0,
    notify_cycle: 0,
    bo_id: 0,
    customer_id: "",
    ...props.initialData,
  });
};

// 初始化客户列表
const queryCustomerList = async () => {
  try {
    const res = await getCustomerList({
      page: 1,
      limit: 200,
    });
    console.log("初始化客户列表:", res);
    if (res.code === 0 && res.data.list.length) {
      oldCustomerList.value = res.data?.list.map((item) => ({
        text: item.company_short_name, // 显示的文本
        value: item.customer_id, // 对应的值
      }));
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};

// 初始化商机列表
const queryBusinessList = async () => {
  try {
    const res = await fetchOpportunityList({
      page: 1,
      limit: 200,
    });
    console.log("初始化商机列表:", res);
    if (res.code === 0 && res.data.list.length) {
      oldBusinessList.value = res.data?.list.map((item) => ({
        text: item.title, // 显示的文本
        value: item.bo_id, // 对应的值
      }));
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};

// 暴露方法
defineExpose({
  show: () => {
    showForm.value = true;
  },
  close: () => {
    showForm.value = false;
  },
  resetForm,
});

onMounted(() => {
  // 查询客户列表
  queryCustomerList();
  // 初始化商机列表
  queryBusinessList();
});
</script>

<style scoped lang="scss">
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
}

.form-header-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.form-header-btn {
  font-size: 14px;
  color: #666;
  padding: 5px 10px;
}

.confirm-btn {
  color: #007aff;
}

.form-content {
  max-height: 80vh;
  .task-form {
    padding: 10px 15px;
    :deep(.uni-forms-item__label) {
      height: 35px;
      color: var(---, #787d86);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 35px;
      font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
    }
  }
}
</style>
