import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { Popover} from 'vant'
import 'vant/lib/popover/style'
// import CustomerService from './components/CustomerService.vue' //全局引用ai对话入口组件

export function createApp() {
  const app = createSSRApp(App)
  // app.component('CustomerService', CustomerService) //全局注册ai对话入口组件，每个页面将可以直接使用该组件
  app.use(Popover)
  return {
    app
  }
}
// #endif