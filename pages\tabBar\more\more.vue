<template>
  <view class="more-profile-section" :style="appSafeAreaStyle">
    <view
      class="profile-header"
      @click="handeMoreEdit"
    >
      <image :src="formData.portrait || avatarImage" class="avatar-container" mode="aspectFill"></image>
      <view class="profile-info">
        <text class="username">{{ formData.name || 'User' }}</text>
        <text class="phone">{{ formData.mobile|| '未绑定手机号'}}</text>
      </view>
      <uni-icons type="right" size="16"></uni-icons>
    </view>
    <view class="menu-section">
      <!-- <view class="menu-item" @click="handeMoneyBag">
        <image src="@/static/global/more-moneybag.svg" class="menu-icon"></image>
        <view class="menu-content">
          <text class="menu-title">付费充值</text>
          <text class="menu-subtitle">试用剩余20天</text>
        </view>
        <uni-icons type="right" size="16"></uni-icons>
      </view> -->
      <view class="menu-item" @click="handeRelatedDocumentsClick">
        <image src="@/static/global/more-file.svg" class="menu-icon"></image>
        <text class="menu-title">相关文件</text>
        <uni-icons type="right" size="16"></uni-icons>
      </view>
      <view class="menu-item" @click="handeGlobalSearch">
        <image src="@/static/global/more-search.svg" class="menu-icon"></image>
        <text class="menu-title">全局搜索</text>
        <uni-icons type="right" size="16"></uni-icons>
      </view>
      <view class="menu-item" @click="handeFeedback">
        <image src="@/static/global/more-feedback.svg" class="menu-icon"></image>
        <text class="menu-title">意见反馈</text>
        <uni-icons type="right" size="16"></uni-icons>
      </view>
      <view class="menu-item" @click="handleLogout">
        <image src="@/static/global/more-log-out.svg" class="menu-icon"></image>
        <text class="menu-title">退出登录</text>
        <uni-icons type="right" size="16" color="#FA5151"></uni-icons>
      </view>
      <!-- <view class="menu-item" @click="handeGlobalSetting">
        <image src="@/static/global/more-setting.svg" class="menu-icon"></image>
        <text class="menu-title">系统设置</text>
        <uni-icons type="right" size="16"></uni-icons>
      </view> -->
      <!-- #ifdef APP-PLUS -->
      <!-- 版本号显示区域 -->
      <view class="version-section" @click="handleVersionClick">
        <text class="version-text">版本号: {{ currentVersion }}</text>
      </view>
      <!-- #endif -->
    </view>
  </view>
  <!-- 客服入口组件 -->
  <CustomerService />
</template>
<script setup>
import { ref,computed,reactive,onMounted } from "vue";
import { getUserInfo } from '@/http/user';
import avatarImage from "@/static/user/avatar.png";
import { onShow,onTabItemTap } from '@dcloudio/uni-app';
import CustomerService from "@/components/CustomerService.vue";
import { getCurrentVersion } from '@/version.js';

// 表单数据
const formData = reactive({
  name: '',
  mobile: '',
  portrait: '',
});

// 版本号点击计数器
const versionClickCount = ref(0);
const versionClickTimer = ref(null);
const currentVersion = ref(getCurrentVersion());
// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`
  };
  // #endif
  return {};
});
// 个人信息设置
const handeMoreEdit = () => {
  uni.navigateTo({
    url: "/pages/userInfo/user/user-settings",
  });
};
// 相关文件
const handeRelatedDocumentsClick = () => {
  uni.navigateTo({
    url: "/pages/userInfo/related-documents/index",
  });
};
// 全局搜索
const handeGlobalSearch = () => {
  uni.navigateTo({
    url: "/pages/userInfo/global-search/index",
  });
};

/**
 * 处理版本号点击事件
 * 连续点击7次进入调试日志页面
 */
const handleVersionClick = () => {
  versionClickCount.value++;
  // 清除之前的定时器
  if (versionClickTimer.value) {
    clearTimeout(versionClickTimer.value);
  }
  // 如果点击次数达到8次，进入调试页面
  if (versionClickCount.value >= 8) {
    versionClickCount.value = 0;
    uni.showToast({
      title: '进入调试模式',
      icon: 'success'
    });
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/demo/index",
      });
    }, 500);
    return;
  }
  // 设置3秒后重置计数器
  versionClickTimer.value = setTimeout(() => {
    versionClickCount.value = 0;
  }, 4000);
};

/**
 * 处理意见反馈点击事件
 * 根据不同平台环境使用不同的方式打开反馈页面
 * H5环境使用window.open
 * APP环境使用系统浏览器打开链接
 */
const handeFeedback = () => {
  // 反馈页面URL
  const feedbackUrl = 'https://docs.qq.com/form/page/DUE5YeU5ZSUVMbUtX';
  // 判断运行环境
  // #ifdef H5
  window.open(feedbackUrl);
  // #endif
  // #ifdef APP-PLUS
  // 使用系统浏览器打开链接
  plus.runtime.openURL(feedbackUrl);
  // #endif
};

/**
 * 处理退出登录点击事件
 * 清空所有本地存储数据并跳转到登录页面
 */
const handleLogout = () => {
  uni.showModal({
    title: '退出登录',
    content: '确定要退出登录吗？',
    confirmText: '确定',
    cancelText: '取消',
    confirmColor: '#FA5151',
    success: (res) => {
      if (res.confirm) {
        // 执行退出登录操作
        performLogout();
      }
    }
  });
};

/**
 * 执行退出登录操作
 * 清空本地存储并跳转到登录页面
 */
const performLogout = () => {
  try {
    // 清空所有本地存储数据
    uni.clearStorage();
    uni.clearStorageSync();

    // 显示退出成功提示
    uni.showToast({
      title: '退出成功',
      icon: 'success',
      duration: 1500
    });
    
    // 延迟跳转到登录页面，确保提示显示完成
    setTimeout(() => {
      uni.navigateTo({
        url: "/pages/ai-chat",
        fail: (err) => {
          console.error('跳转到聊天页面失败:', err);
        }
      });
    }, 1500);
    
  } catch (error) {
    console.error('退出登录失败:', error);
    uni.showToast({
      title: '退出失败，请重试',
      icon: 'none'
    });
  }
};

// 系统设置
const handeGlobalSetting = () => {
  uni.showToast({
    title: '暂未开通此功能',
    icon: 'none'
  });
};
// 付费充值
const handeMoneyBag = () => {
  uni.showToast({
    title: '暂未开通此功能',
    icon: 'none'
  });
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getUserInfo();
    if (res.code === 0 && res.data) {
      // 合并数据到 formData
      Object.assign(formData, res.data);
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  }
};

// 点击 tab 时触发，参数为Object
onTabItemTap(() => {
  uni.setTabBarStyle({
    selectedColor: '#3E4551',
  });
})

// 修改点：使用 uni-app 的页面生命周期 onShow
// 每次页面显示时（包括初次加载、从其他页面返回）触发请求
onShow(() => {
  fetchUserInfo();
});

</script>

<style lang="scss" scoped>
.more-profile-section {
  // #ifdef H5 
  padding: 20px;
  max-width: 750px;
  margin: 0 auto;
  // #endif
  // #ifdef APP-PLUS
  padding: 0px 20px 0px 20px;
  // #endif
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .profile-header {
    display: flex;
    align-items: center;
    // #ifdef APP-PLUS
    padding: 20px 0px 0px 0px;
    // #endif
  }

  .avatar-container {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    border-radius: 10px;
  }

  .profile-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .username {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .phone {
    font-size: 14px;
    color: #999;
  }

  .menu-section {
    margin-top: 10px;
    .menu-item {
      display: flex;
      align-items: center;
      border-radius: 10px;
      border: 1px solid var(---normal, #e2e4e9);
      background: #fff;
      max-height: 65px;
      height: 60px;
      padding: 0 14px;
      margin: 20px 0;
    }
  }

  .menu-icon {
    width: 28px;
    height: 28px;
    margin-right: 15px;
  }

  .menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .menu-title {
    font-size: 16px;
    font-weight: 400;
    color: #333;
    flex: 1;
  }

  .menu-subtitle {
    font-size: 14px;
    color: #2196f3;
    margin-top: 4px;
  }
  
  // 版本号样式
  .version-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    margin-top: 20px;
  }
  
  .version-text {
    font-size: 14px;
    color: #999;
    text-align: center;
  }
}
</style>