<template>
  <view class="weboffice-preview-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <uni-icons type="back" size="20" color="#333"></uni-icons>
          <text class="back-text">返回</text>
        </view>
        
        <view class="navbar-center">
          <text class="file-title">{{ fileInfo.name || '文档预览' }}</text>
        </view>
        
        <view class="navbar-right">
          <!-- 可以添加更多操作按钮 -->
        </view>
      </view>
    </view>
    
    <!-- 预览内容区域 -->
    <view class="preview-content">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-container">
        <view class="loading-content">
          <uni-icons type="spinner-cycle" size="40" color="#007AFF" class="loading-icon"></uni-icons>
          <text class="loading-text">{{ loadingText }}</text>
        </view>
      </view>
      
      <!-- 错误状态 -->
      <view v-if="hasError" class="error-container">
        <view class="error-content">
          <uni-icons type="closeempty" size="60" color="#FF3B30" class="error-icon"></uni-icons>
          <text class="error-title">预览失败</text>
          <text class="error-message">{{ errorMessage }}</text>
          <button class="retry-button" @click="retryPreview">重试</button>
          <button class="back-button" @click="goBack">返回</button>
        </view>
      </view>
      
      <!-- WebOffice容器 -->
      <view
        ref="webofficeContainer" 
        class="weboffice-container"
        :style="{ display: isReady ? 'block' : 'none' }"
      ></view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { WebOfficePreview } from '@/services/aliyunWebOfficeService.js';

// 响应式数据
const webofficeContainer = ref(null);
const isLoading = ref(false);
const isReady = ref(false);
const hasError = ref(false);
const errorMessage = ref('');
const loadingText = ref('正在加载...');
const fileInfo = ref({});

// WebOffice实例
let webOfficeInstance = null;

// 防抖定时器
let resizeTimer = null;

/**
 * 检测移动设备
 * 它可以用来检测浏览器是否为 Safari 或苹果设备的浏览器。
 */
 function isAppleBrowser() {
  const userAgent = navigator.userAgent;
  // 检查是否是 iOS 设备或 Safari 浏览器
  const isIOS = /iPhone|iPad|iPod/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  // 判断是否是苹果设备浏览器
  return isIOS || isSafari;
}
function isMobileDevice() {
  return window.innerWidth <= 768;  // 判断是否是小于等于768px宽的屏幕
}
function isIOS() {
  return /iPhone|iPad|iPod/.test(navigator.userAgent);
}
function isSafari() {
  return /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
}
function adjustStylesForDevice() {
  const container = document.querySelector('.weboffice-container');
  
  if (isMobileDevice()) {
    if (isIOS() && isSafari()) {
      container.style.height = 'calc(100vh - 120px)';
    } else {
      container.style.height = '100vh';
    }
  } else {
    container.style.height = '100vh';
  }
}

/**
 * 开始预览
 */
const startPreview = async () => {
  if (!fileInfo.value || !webofficeContainer.value) {
    console.error('❌ 预览参数不完整');
    return;
  }
  
  try {
    isLoading.value = true;
    hasError.value = false;
    isReady.value = false;
    loadingText.value = '正在初始化预览...';
    
    // 等待DOM更新
    await nextTick();
    
    
    // 设置容器尺寸
    // const containerHeight = calculateContainerHeight();
    // if((/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))){
    //   console.error('isAppleBrowser------------------------------------');
    //   // container.style.height = `calc(100vh - 120px)`;
    //   console.log(/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)> -1) 
    // }
    // 调用函数
    // adjustStylesForDevice();
    
    await nextTick();
    
    // 创建WebOffice预览实例
    webOfficeInstance = new WebOfficePreview(webofficeContainer.value);
    
    // 监听加载状态变化
    const originalShowLoading = webOfficeInstance.showLoading;
    webOfficeInstance.showLoading = (message) => {
      loadingText.value = message;
      originalShowLoading.call(webOfficeInstance, message);
    };
    
    // 初始化预览
    await webOfficeInstance.init(fileInfo.value);
    
    // 预览成功
    isLoading.value = false;
    isReady.value = true;
    
    console.log('✅ WebOffice预览页面初始化成功');
    
  } catch (error) {
    console.error('❌ WebOffice预览失败:', error);
    
    isLoading.value = false;
    hasError.value = true;
    errorMessage.value = error.message || '预览失败，请稍后重试';
  }
};

/**
 * 重试预览
 */
const retryPreview = () => {
  console.log('🔄 重试预览');
  startPreview();
};

/**
 * 返回上一页
 */
const goBack = () => {
  // #ifdef H5
  if (window.history.length > 1) {
    window.history.back();
  } else {
    uni.navigateBack({
      delta: 1
    });
  }
  // #endif
};

/**
 * 销毁预览实例
 */
const destroyPreview = () => {
  if (webOfficeInstance) {
    try {
      webOfficeInstance.destroy();
      webOfficeInstance = null;
      console.log('✅ WebOffice预览实例已销毁');
    } catch (error) {
      console.warn('⚠️ 预览实例销毁失败:', error);
    }
  }
  
  isLoading.value = false;
  isReady.value = false;
  hasError.value = false;
};

// 生命周期钩子
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
  
  console.log('📄 WebOffice预览页面挂载，参数:', options);
  
  // 解析文件信息
  if (options.fileInfo) {
    try {
      fileInfo.value = JSON.parse(decodeURIComponent(options.fileInfo));
      console.log('📄 解析文件信息:', fileInfo.value);

      // 验证必要参数
      if (!fileInfo.value.id) {
        throw new Error('文件ID缺失');
      }
      
      if (!fileInfo.value.name) {
        throw new Error('文件名缺失');
      }
      // 开始预览
      startPreview();
      
    } catch (error) {
      console.error('❌ 解析文件信息失败:', error);
      hasError.value = true;
      errorMessage.value = error.message || '文件信息解析失败';
      return;
    }
  } else {
    console.error('❌ 缺少文件信息参数');
    hasError.value = true;
    errorMessage.value = '缺少文件信息';
    return;
  }
});

onBeforeUnmount(() => {
  console.log('🗑️ WebOffice预览页面卸载');
  
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
    resizeTimer = null;
  }
  destroyPreview();
});
</script>

<style scoped lang="scss">
// 动画相关样式
@import '/styles/animations.scss';
// 变量定义
$primary-color: #007AFF;
$error-color: #FF3B30;
$text-color: #333;
$text-secondary: #666;
$navbar-height: 44px;

/**
 * WebOffice预览页面
 */
.weboffice-preview-page {
  width: 100%;
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  
  // 自定义导航栏
  .custom-navbar {
    height: $navbar-height;
    background-color: #fff;
    border-bottom: 1px solid #e5e5e5;
    position: relative;
    z-index: 100;
    
    .navbar-content {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;
      
      .navbar-left {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        &:active {
          background-color: #e5e5e5;
        }
        
        .back-text {
          margin-left: 4px;
          font-size: 16px;
          color: $text-color;
          min-width: 35px;
        }
      }
      
      .navbar-center {
        flex: 1;
        text-align: center;
        margin: 0 16px;
        
        .file-title {
          font-size: 16px;
          font-weight: 500;
          color: $text-color;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 320px;
          display: inline-block;
        }
      }
      
      .navbar-right {
        width: 60px; // 保持布局平衡
      }
    }
  }
  
  // 预览内容区域
  .preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #f5f5f5;
    
    // WebOffice容器
    .weboffice-container {
      width: 100%;
      height: 100%;
      // height: calc(100vh - 120px);
      // 适配底部安全区域
      // padding-bottom: calc(300px + constant(safe-area-inset-bottom)); // iOS 11.0
      // padding-bottom: calc(300px + env(safe-area-inset-bottom)); // iOS 11.2+
      // position: relative;
      
      // iframe样式会通过JS动态设置
    }
  }
  
  // 加载状态
  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    
    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      
      .loading-icon {
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        font-size: 16px;
        color: $text-secondary;
        text-align: center;
      }
    }
  }
  
  // 错误状态
  .error-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    
    .error-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding: 40px 20px;
      text-align: center;
      
      .error-icon {
        opacity: 0.6;
      }
      
      .error-title {
        font-size: 18px;
        font-weight: 500;
        color: $error-color;
      }
      
      .error-message {
        font-size: 14px;
        color: $text-secondary;
        line-height: 1.5;
        max-width: 300px;
      }
      
      .retry-button,
      .back-button {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        margin: 4px;
      }
      
      .retry-button {
        background-color: $primary-color;
        color: #fff;
        
        &:hover {
          background-color: darken($primary-color, 10%);
        }
        
        &:active {
          background-color: darken($primary-color, 15%);
          transform: scale(0.98);
        }
      }
      
      .back-button {
        background-color: #6c757d;
        color: #fff;
        
        &:hover {
          background-color: darken(#6c757d, 10%);
        }
        
        &:active {
          background-color: darken(#6c757d, 15%);
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>