<template>
  <uni-popup 
    ref="popupRef" 
    :type="popupType" 
    background-color="#fff"
    :mask-click="true"
    :border-radius="isPCDevice ? '10px' : '10px 10px 0px 0px'"
  >
  <!-- 功能引导页面 -->
    <view class="skill-selector" :class="{ 'pc-centered': isPCDevice }">
      <!-- 头部 -->
      <view class="skill-header">
        <text class="skill-title">我能帮您做什么</text>
        <uni-icons type="closeempty" class="close-icon" size="24" color="#000" @click="closePopup"></uni-icons>
      </view>
      <!-- 内容区域 -->
      <scroll-view class="skill-content" scroll-y>
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <uni-load-more status="loading" :iconSize="20" :showText="true" loadingText="加载中..."></uni-load-more>
        </view>
        <!-- 技能分类内容 -->
        <view v-else>
          <view 
            v-for="(category, index) in skillCategories" 
            :key="category.id || index"
            class="skill-category"
          >
            <text class="category-title">{{ category.title }}</text>
            <view class="skill-tags">
              <view 
                v-for="(skill, skillIndex) in category.child || category.skills" 
                :key="skill.id || skillIndex"
                class="skill-tag"
                :class="{ 'skill-tag-active': selectedSkill === skill.id || selectedSkill === skill.value }"
                @click="selectSkill(skill, category)"
              >
                <text class="skill-tag-text">{{ skill.label }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { getChatGuidance } from '@/http/ai-chat-task.js'
import { isPC } from '@/utils/utils.js'

// 定义props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// 定义emits
const emit = defineEmits(['update:modelValue', 'close', 'select'])

// 组件引用
const popupRef = ref(null)

// 状态变量
const loading = ref(false)
const selectedSkill = ref('')
const skillCategories = ref([])
const windowWidth = ref(0) // 窗口宽度
const resizeTimer = ref(null) // 防抖计时器
const lastDeviceType = ref(null) // 上次的设备类型

// 设备类型检测 - 基于窗口宽度实时判断
const isPCDevice = computed(() => {
  // 使用utils.js中的isPC方法作为基础判断
  const isBasicPC = isPC()
  
  // #ifdef H5
  // 在基础判断基础上，结合窗口宽度进行实时响应式判断
  if (windowWidth.value > 0) {
    // 如果窗口宽度小于等于768px，强制判定为移动端
    if (windowWidth.value <= 768) {
      return false
    }
    // 如果窗口宽度大于768px，判定为PC端
    if (windowWidth.value > 768) {
      return true
    }
  }
  // 默认使用基础的isPC判断结果
  return isBasicPC
  // #endif

  // #ifdef APP-PLUS
  return false
  // #endif
})

// 弹出层类型
const popupType = computed(() => {
  return isPCDevice.value ? 'center' : 'bottom'
})

/**
 * 更新窗口尺寸
 */
const updateWindowSize = () => {
  // #ifdef H5
  windowWidth.value = window.innerWidth
  // #endif
}

/**
 * 窗口尺寸变化处理 - 添加防抖
 */
const handleResize = () => {
  // 清除之前的计时器
  if (resizeTimer.value) {
    clearTimeout(resizeTimer.value)
  }
  // 防抖处理，300ms后执行
  resizeTimer.value = setTimeout(() => {
    const oldWidth = windowWidth.value
    updateWindowSize()
    // 检测设备类型是否真的发生了变化
    const currentDeviceType = isPCDevice.value ? 'pc' : 'mobile'
    // 更新上次设备类型记录
    lastDeviceType.value = currentDeviceType
    resizeTimer.value = null
  }, 100) // 100ms防抖
}

/**
 * 打开弹出层
 */
const open = async () => {
  // 从本地存储获取上次选中的技能
  const savedSkill = uni.getStorageSync('selectedSkill')
  if (savedSkill) {
    selectedSkill.value = savedSkill
  }
  
  // 打开弹出层
  popupRef.value?.open()
  
  // 如果没有数据，则获取数据
  if (skillCategories.value.length === 0) {
    await fetchSkillData()
  }
}

/**
 * 关闭弹出层
 */
const closePopup = () => {
  popupRef.value?.close()
  emit('close')
}

/**
 * 重置组件状态
 * 用于发送消息后清理组件状态
 */
const resetState = () => {
  // 重置选中状态
  selectedSkill.value = ''
  
  // 清除本地存储
  uni.removeStorageSync('selectedSkill')
  uni.removeStorageSync('selectedSkillLabel')
  uni.removeStorageSync('selectedSkillExample')
  
  // 关闭弹出层（如果正在显示）
  if (popupRef.value && popupRef.value.isOpen) {
    popupRef.value.close()
  }
  
  // 触发选择事件，传递空值表示重置选择
  emit('select', { label: '', value: '', example: '' })
  emit('update:modelValue', '')
  
  console.log('技能选择器状态已重置')
}

/**
 * 选择技能
 */
const selectSkill = (skill, category) => {
  // 如果点击的是已选中的技能，则取消选择
  if (selectedSkill.value === skill.id || selectedSkill.value === skill.value) {
    selectedSkill.value = ''
    // 清除本地存储
    uni.removeStorageSync('selectedSkill')
    uni.removeStorageSync('selectedSkillLabel')
    uni.removeStorageSync('selectedSkillExample')
    // 触发选择事件，传递空值表示取消选择
    emit('select', { label: '', value: '', example: '',word:'' })
    emit('update:modelValue', '')
    // 延迟关闭
    setTimeout(() => {
      closePopup()
    }, 100)
    return
  }
  
  selectedSkill.value = skill.id || skill.value
  // 保存到本地存储
  uni.setStorageSync('selectedSkill', selectedSkill.value)
  // uni.setStorageSync('selectedSkillLabel', skill.label)
  uni.setStorageSync('selectedSkillLabel', skill.word)
  uni.setStorageSync('selectedSkillExample', skill.eg || '')
  
  // 构造选择结果
  const selectResult = {
    id: skill.id,
    label: skill.label,
    word: skill.word,
    value: skill.id || skill.value,
    example: skill.eg || '',
    category: category.title
  }
  
  // 触发选择事件
  emit('select', selectResult)
  emit('update:modelValue', skill.label)
  // 延迟关闭，让用户看到选中效果
  setTimeout(() => {
    closePopup()
  }, 100)
}

/**
 * 获取技能数据
 */
const fetchSkillData = async () => {
  try {
    loading.value = true
    const response = await getChatGuidance()
    // console.error('技能数据响应:', response)
    if (response.code === 0 && response.data) {
      skillCategories.value = response.data
    } else {
      console.error('获取技能数据失败:', response.msg || '未知错误')
      uni.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      })
      // 使用默认数据作为降级方案
      skillCategories.value = []
    }
  } catch (error) {
    console.error('获取技能数据异常:', error)
    uni.showToast({
      title: '网络异常，请稍后重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close: closePopup,
  resetState
})

// 初始化
onMounted(async () => {
  // 初始化窗口尺寸
  updateWindowSize()
  // 设置初始设备类型记录
  lastDeviceType.value = isPCDevice.value ? 'pc' : 'mobile'
  // console.log('初始设备类型:', lastDeviceType.value)
  // 添加窗口尺寸变化监听
  // #ifdef H5
  window.addEventListener('resize', handleResize)
  // #endif
  // 从本地存储恢复选中状态
  const savedSkill = uni.getStorageSync('selectedSkill')
  if (savedSkill) {
    selectedSkill.value = savedSkill
  }
  
  // 预加载技能数据
  await fetchSkillData()
})

// 移除窗口尺寸变化监听
onUnmounted(() => {
  // 清理计时器
  if (resizeTimer.value) {
    clearTimeout(resizeTimer.value)
    resizeTimer.value = null
  }
  // #ifdef H5
  window.removeEventListener('resize', handleResize)
  // #endif
})
</script>

<style lang="scss" scoped>
.skill-selector {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  height: 70vh;
  max-height: 700px;
  max-width: 750px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
  transition: all 0.3s ease;
  &.pc-centered {
    height: 50vh;
    max-height: 500px;
    min-width: 750px;
    max-width: 750px;
    width: 100%;
    margin: 0 auto;
    border-radius: 10px;
  }
}

:deep(.uni-popup) {
  z-index: 999 !important;
}

:deep(.uni-popup__wrapper) {
  z-index: 999 !important;
}

.skill-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  // border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  position: relative;
  z-index: 10001;
  
  .skill-title {
    color: var(---, #000);
    font-family: "PingFang SC";
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  
  .close-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: opacity 0.2s ease;
    
    &:active {
      opacity: 0.6;
    }
  }
}

.skill-content {
  flex: 1;
  // padding: 16px 24px 24px;
  padding-left: 20px;
  overflow-y: auto;
  position: relative;
  z-index: 10001;
}

.skill-category {
  margin-bottom: 18px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .category-title {
    color: var(---, #787D86);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      width: 3px;
      height: 12px;
      margin-top: 2px;
      background-color: #4D5BDE;
      border-radius: 2px;
      margin-right: 8px;
    }
  }
  
  .skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .skill-tag {
    border-radius: 10px;
    border: 1px solid var(---normal, #E2E4E9);
    background: #FFF;
    padding: 4px 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s ease;
    }
    
    &:active {
      transform: scale(0.96);
      
      &::before {
        left: 100%;
      }
    }
    
    &.skill-tag-active {
      // background: linear-gradient(135deg, #e6f4ff 0%, #bae0ff 100%);
      // box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      border-color: #4D5BDE;
      background: #EEEFFC;
      
      .skill-tag-text {
        color: #4D5BDE;
        font-weight: 500;
      }
    }
    
    .skill-tag-text {
      transition: all 0.3s ease;
      color: var(---, #000);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

// 优化滚动条样式
.skill-content::-webkit-scrollbar {
  width: 4px;
}

.skill-content::-webkit-scrollbar-track {
  background: transparent;
}

.skill-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
  
  &:hover {
    background: #bfbfbf;
  }
}

// 加载容器样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 200px;
}
</style> 