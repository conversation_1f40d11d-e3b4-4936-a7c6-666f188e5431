$primary-color: #4d5bde;
$light-gray: #f5f5f5;
$dark-gray: #333333;
$medium-gray: #999999;
$border-color: #eeeeee;
// 全局容器样式
.chat-container {
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 53px; /* 顶部导航栏的高度 */
  height: 100vh;
  // overflow: hidden;
  // #ifdef APP-PLUS
  background: #F8F9FA;
  // #endif

  // 顶部导航栏样式 - 固定在顶部
  .chat-header-container {
    // #ifdef APP-PLUS
    top: 40px;
    // background: rgb(4, 245, 112);
    // #endif
  }

  // 输入区域
  .chat-input-container {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    padding: 0 15px 0 15px;
    z-index: 9;
    max-width: 750px;
    margin: 0 auto;
    // #ifdef APP-PLUS
    // background: #f1f1f1;
    // #endif

    // 添加渐变效果遮罩
    &::before {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      height: 30px;
      pointer-events: none;
      bottom: 100px;
      background: linear-gradient(180deg, rgba(248, 249, 250, 0) 0%, #f8f9fa 100%);
      // background: red;
      z-index: -1;
      margin: 0 auto;
      max-width: 750px;
      width: calc(100% - 60px);
    }

    // 适配底部安全区域
    padding-bottom: calc(10px + constant(safe-area-inset-bottom)); // iOS 11.0
    padding-bottom: calc(10px + env(safe-area-inset-bottom)); // iOS 11.2+

    // 已上传文件列表
    .uploaded-files-container {
      margin-bottom: 2px;
      overflow: visible;
      padding: 5px 15px;

      .file-list-scroll {
        width: 100%;
        white-space: nowrap;
      }

      .file-list {
        display: inline-flex;
        align-items: center;
        padding: 4px 0;
        max-height: 40px;
        .img-item {
          position: relative;
          margin-right: 10px;
          .file-image {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background-size: 100% 100%;
          }
        }
        .file-item {
          position: relative;
          display: flex;
          // flex-direction: column;
          align-items: center;
          margin-right: 10px;
          max-width: 160px;
          background-color: #ffff;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
          border-radius: 10px;
          max-height: 40px;
          min-height: 40px;
          padding: 0 10px;

          .file-thumbnail {
            width: 20px;
            height: 20px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            .file-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .file-type-icon {
              width: 28px;
              height: 28px;
              object-fit: contain;
            }
          }

          .file-name {
            width: 100%;
            font-size: 14px;
            font-weight: 400;
            color: #3e4551;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .file-delete {
          position: absolute;
          top: -2px;
          right: -4px;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          z-index: 2;
          .delete-icon {
            font-size: 12px;
            line-height: 1;
          }
        }
      }
    }

    .input-wrapper {
      background-color: #ffffff;
      padding: 8px 12px;
      box-shadow: 0px 2px 12px 0px rgba(68, 73, 77, 0.13);
      border-radius: 10px;
      border: 1px solid var(---normal, #e2e4e9);
      margin: 0 15px;
      .chat-input {
        height: auto;
        min-height: 44px;
        max-height: 260px;
        font-size: 14px;
        overflow-y: auto; /* 添加垂直滚动条 */
        padding-right: 5px; /* 为滚动条留出空间 */
        width: 100%;
      }
      /* PC端专用样式 */
      .pc-input {
        // border: 1px solid #e0e0e0;
        // border-radius: 8px;
        padding: 0;
      }
      /* 自定义滚动条样式 */
      .chat-input::-webkit-scrollbar {
        width: 2px;
      }
      .chat-input::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }
      .chat-input::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 2px;
      }
      .chat-input::-webkit-scrollbar-thumb:hover {
        background: #555;
      }

      .input-tools {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        .tool-left {
          display: flex;
          align-items: center;
          .clear-input-button {
            width: 30px;
            height: 30px;
            border-radius: 10px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            .clear-icon {
              width: 20px;
              height: 20px;
              background-size: 100% 100%;
            }
          }
          .tool-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-left: 5px;
            background: var(---bg_hover, #f8f9fa);
            color: var(---, #adb1ba);
            border-radius: 10px;
            padding: 5px 8px 5px 5px;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            .tag-btn-icon {
              width: 20px;
              height: 20px;
              background-size: 100% 100%;
            }
          }
          .tool-item-active {
            background: #eeeffc;
            color: var(---, #4d5bde);
          }
        }

        .tool-right {
          display: flex;
          align-items: center;
          .file-btn-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 8px;
          }

          .tool-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 10px;
            margin-left: 8px;

            &.send-button {
              background-color: $primary-color;
              transition: all 0.3s;
              &.send-button-disabled {
                background: var(---, #d0d1d6);
                cursor: not-allowed;
                opacity: 0.6;
              }
            }
          }
        }
      }
    }
  }
}

// 录音麦克风样式
.speech-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;

  &.voice-tool {
    width: 30px;
    height: 30px;
    border-radius: 10px;
    transition: all 0.3s;
    position: relative;
    uni-image {
      height: 30px;
    }
    &.voice-active {
      background-color: rgba(77, 91, 222, 0.1);
    }

    .voice-wave-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 15px;
      height: 15px;
    }

    .voice-wave-bar {
      width: 2px;
      background-color: #4d5bde;
      border-radius: 2px;
      animation: voiceWave 1s infinite ease-in-out;

      &:nth-child(1) {
        height: 8px;
        animation-delay: 0s;
      }
      &:nth-child(2) {
        height: 16px;
        animation-delay: 0.2s;
      }
      &:nth-child(3) {
        height: 10px;
        animation-delay: 0.4s;
      }
      &:nth-child(4) {
        height: 14px;
        animation-delay: 0.6s;
      }
    }

    .voice-tooltip {
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 6px 10px;
      border-radius: 6px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 10;

      &:after {
        content: "";
        position: absolute;
        bottom: -6px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid rgba(0, 0, 0, 0.7);
      }
    }
  }
}

// 图片和文件消息样式
.message-image-container {
  // width: 100%;
  max-width: 100px;
  padding: 0 5px;
  .message-image {
    height: 100px;
    width: 100px;
    border-radius: 10px;
    background-size: 100% 100%;
  }

  .image-filename {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 用户消息文件样式
.user-message {
  .message-file-container {
    padding: 8px;
    border-radius: 8px;
    background-color: #ffffff;
    .file-info {
      display: flex;
      align-items: center;
      .file-type-icon {
        width: 30px;
        height: 30px;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .file-details {
        flex: 1;
        overflow: hidden;
        .file-name {
          font-size: 14px;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .file-size {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
  .file-deleted-message {
    .file-info {
      display: flex;
      align-items: center;
      .file-type-icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        flex-shrink: 0;
      }
    }
  }
}
// AI消息文件样式
.assistant-message {
  .message-file-container {
    padding: 6px;
    .file-info {
      display: flex;
      align-items: center;
      .file-type-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      .file-details {
        flex: 1;
        overflow: hidden;
        .file-name {
          font-size: 12px;
          font-weight: 400;
          color: #787d86;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .file-size {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

// 添加以下样式：空内容占位符样式
.empty-content {
  min-height: 20px;
  padding: 5px 0;
  display: block;
}
// 底部操作按钮的样式
.input-btn-icon {
  height: 30px;
  width: 30px;
}
// 修改 CSS 样式，添加处理中状态与工具调用容器的样式
.tool-container {
  margin-bottom: 10px;
  border-radius: 10px;
  border: solid 1px #e2e4e9;
  max-width: 90%;
  // 展开状态时添加padding以与message-bubble对齐
  &.expanded {
    padding: 10px;
    .Title-status {
      margin: -10px -10px 0 -10px;
    }
  }
  // 在CSS中添加处理状态的样式
  .processing-status {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    cursor: pointer;
    height: 40px; // 设置固定高度为40px
    box-sizing: border-box; // 确保padding包含在高度内
    .status-text {
      font-size: 14px;
      color: #3e4551;
      font-weight: 400;
      margin-right: 5px;
    }
  }
  .Title-status {
    border-bottom: 1px solid var(---normal, #e2e4e9);
  }
  // 网络搜索结果样式
  .search-result-item {
    padding: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .search-result-title {
      overflow: hidden; /* 超出内容隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
    .search-link {
      color: #787d86;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 超出内容隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
      text-decoration: underline;
    }
    .search-title-text {
      color: #333333;
      font-weight: 600;
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 超出内容隐藏 */
    }
    .search-result-date {
      color: #999999;
      font-size: 12px;
      min-width: 65px;
      max-width: 80px;
    }
  }
}
// 会话时间
.conversation-time {
  width: 100%;
  font-size: 12px;
  font-weight: 400;
  color: #adb1ba;
  text-align: center;
  margin-top: 4px;
}
// 清空上下文
.clear-context-msg {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;
  color: #adb1ba;
  text-align: center;
  margin-top: 4px;
  width: 100%;
  &::before,
  &::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #e2e4e9;
    margin: 0 10px; /* 线条与文字的间距 */
  }
}

.processing-text-container {
  border: solid 1px rgba(226, 228, 233, 1);
  border-radius: 10px;
  padding: 8px 12px;
  .processing-text {
    font-size: 14px;
    color: #666;
    text-align: center;
  }
}

// 主容器
.chat-container {
  // 聊天内容区域
  .chat-scroll-container {
    // 工具调用样式
    .tool-call-container {
      width: 100%;
      .tool-call-header {
        display: flex;
        align-items: center;
        padding: 5px;
        cursor: pointer;

        .status-icon {
          margin-right: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.loading {
            width: 16px;
            height: 16px;

            .tool-loading {
              transform: scale(0.8);
            }
          }

          &.completed {
            width: 16px;
            height: 16px;
          }
        }

        .tool-info {
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 14px;
          font-weight: 400;
          color: #3e4551;
          font-family: PingFang SC;
          // .tool-label {}
          // .tool-name {}
        }
      }

      .tool-call-content {
        max-height: 520px;
        margin-left: 24px;
        color: #787d86;
        overflow: auto;
        .tool-args,
        .tool-result {
          font-size: 12px;
          line-height: 1.5;
          white-space: pre-wrap;
          word-break: break-word;
        }
        .args-title-1 {
          padding: 0 5px;
        }
        .args-title-2 {
          padding: 0 5px;
        }

        .tool-content-title {
          font-weight: 400;
          color: #787d86;
        }

        .tool-content-text {
          font-weight: 400;
          margin-bottom: 8px;
          color: #787d86;
        }

        .tool-content-result {
          color: #787d86;
          // margin-top: 4px;
          // font-style: italic;
        }
      }
    }
    // 消息列表
    .message-list {
      padding: 10px 18px;
      width: 100%;
      box-sizing: border-box;
      position: relative; // 添加相对定位，使加载动画可以相对于消息列表定位
      // 消息项
      .message-item {
        margin-bottom: 10px;
        // overflow: hidden;

        // 用户消息
        &.user-message {
          .message-content {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .message-bubble {
              min-width: 60px; // 最小宽度
              font-weight: 400;
              font-size: 15px;
              color: rgba(0, 0, 0, 0.85);
              text-align: justify; // 对齐文本，避免空格
              .message-text {
                background-color: #a4e17b;
                padding: 10px;
                border-radius: 10px 0 10px 10px;
                width: auto; // 使用自适应宽度
                word-wrap: break-word; // 确保长单词换行
                word-break: break-word; // 允许在任何字符间换行
                white-space: pre-wrap; // 保留空格和换行符
                overflow-wrap: break-word; // 允许内容换行
                user-select: text; /* 允许文本选择 */
                -webkit-user-select: text; /* 针对 Safari */
                -moz-user-select: text; /* 针对 Firefox */
                -ms-user-select: text; /* 针对 IE 和 Edge */
              }
              .file-deleted-message {
                background-color: #ffffff;
                padding: 10px;
                border-radius: 10px 0 10px 10px;
              }
            }
          }
          .message-actions {
            display: flex;
            align-items: center;
            margin-top: 8px;
            margin-left: 4px;
            height: 20px;
            opacity: 0;
            visibility: hidden;
            transition:
            opacity 0.2s ease,
            visibility 0.2s ease;

            &.actions-visible {
              opacity: 1;
              visibility: visible;
            }
          }
        }

        // AI助手消息
        &.assistant-message {
          .message-content {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .message-bubble {
              max-width: 95%; // 最大宽度
              width: auto;
              background-color: #ffffff;
              padding:0 10px;
              // white-space: pre-wrap;
              // overflow: hidden;
              font-size: 15px;
              color: rgba(0, 0, 0, 0.85);
              font-weight: 400;
              border-radius: 0 10px 10px 10px;
            }
          }
        }
      }
    }
  }
}
