// 引用网络请求中间件
import request from '@/utils/request';

// 分页获取商机列表，支持过滤条件。
export function fetchOpportunityList(data) {
    return request({
        url: '/api/busiopp/list',
        method: 'GET',
        data
    })
}

// 新增商机接口
export function createOpportunity(data) {
    return request({
        url: '/api/busiopp/save',
        method: 'POST',
        data
    })
}

// 删除商机接口
export function removeOpportunity(data) {
    return request({
        url: '/api/busiopp/del',
        method: 'POST',
        data
    })
}

// 修改商机接口
export function updateOpportunity(data) {
    return request({
        url: '/api/busiopp/save',
        method: 'POST',
        data
    })
}

// 获取商机详情
export function fetchOpportunityDetail(data) {
    return request({
        url: '/api/busiopp/detail',
        method: 'GET',
        data
    })
}
