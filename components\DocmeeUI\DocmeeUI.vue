<template>
  <view class="docmee-ui-container">
    <!-- PPT编辑器容器 - SDK会自动挂载iframe到这里 -->
     <!-- #ifdef H5 -->
    <view 
      ref="pptContainer" 
      class="ppt-container" 
    ></view>
    <!-- #endif -->
    <!-- APP端使用web-view组件 -->
     <!-- #ifdef APP-PLUS -->
    <web-view 
      :src="appWebViewSrc"
      @message="onAppMessage"
      @load="onAppLoad"
      @error="onAppError"
      class="app-webview"
    ></web-view>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, watchEffect } from 'vue'

/**
 * 组件Props定义
 */
const props = defineProps({
  // PPT ID
  pptId: {
    type: String,
    default: '1935894086229823488'
  },
  // 访问token
  token: String,
  // 页面类型：dashboard(仪表板), editor(编辑器), creator(创建器)
  page: {
    type: String,
    default: 'editor',
    validator: (value) => ['dashboard', 'editor', 'creator', 'customTemplate', 'templateCreator', 'templateMarker'].includes(value)
  },
  // 语言设置
  lang: {
    type: String,
    default: 'zh'
  },
  // 是否为移动端模式
  isMobile: {
    type: Boolean,
    default: false
  },
})

/**
 * 组件Emits定义
 */
const emit = defineEmits([
  'message',         // 接收到消息
  'mounted',         // 编辑器挂载完
])

// 响应式数据
const pptContainer = ref(null)
let docmeeUI = null // SDK实例

/**
 * 计算属性：APP端WebView源地址
 */
const appWebViewSrc = computed(() => {
  // #ifdef APP-PLUS
  try {
    // 手动构建查询参数，兼容不支持URLSearchParams的环境
    const params = [
      'token=' + encodeURIComponent(props.token),
      'pptId=' + encodeURIComponent(props.pptId),
      'page=' + encodeURIComponent(props.page),
      'lang=' + encodeURIComponent(props.lang),
      'timestamp=' + Date.now() // 避免缓存
    ];
    const url = `/static/ppt-editor.html?${params.join('&')}`;
    console.log('🌐 APP端WebView URL:', url);
    return url;
  } catch (error) {
    console.error('❌ APP端URL构建失败:', error);
    return '/static/ppt-editor.html';
  }
  // #endif
  // #ifndef APP-PLUS
  return '';
  // #endif
})

/**
 * 检查SDK是否加载
 */
const checkSDK = () => {
  // #ifdef H5
  return typeof window !== 'undefined' && window.DocmeeUI
  // #endif
  // #ifndef H5
  return false // APP端通过webview处理
  // #endif
}

/**
 * 初始化PPT编辑器
 */
const initPPTEditor = () => {
  // #ifdef H5
  if (!checkSDK()) {
    console.error('PPT编辑器SDK未加载')
    return
  }
  try {
    // 确保获取到真正的DOM元素
    let containerElement = null
    // 通过ref获取
    if (pptContainer.value) {
      containerElement = pptContainer.value
      // 如果是vue组件实例，获取其$el
      if (containerElement.$el) {
        containerElement = containerElement.$el
      }
    }
    console.log('✅ 成功获取容器元素:', containerElement)
    const config = {
      pptId: props.pptId,
      token: props.token,
      container: containerElement, // 使用真实的DOM元素
      page: props.page,
      animation:false,
      lang: props.lang,
      isMobile: props.isMobile,
      onMessage: handleMessage
    }
    console.log('🚀 初始化PPT编辑器', config)
    // 创建DocmeeUI实例
    docmeeUI = new window.DocmeeUI(config)
    emit('mounted')
  } catch (err) {
    console.error('❌ 初始化PPT编辑器失败:', err)
  }
  // #endif
}

/**
 * APP端WebView加载完成
 */
const onAppLoad = (event) => {
  // #ifdef APP-PLUS
  console.log('📱 APP端WebView加载完成:', event.detail)
  // 延迟触发mounted事件，确保页面完全加载
  emit('mounted', {
    platform: 'app',
    url: event.detail.src || appWebViewSrc.value
  })
  console.log('✅ APP端PPT编辑器加载完成')
  // #endif
}

/**
 * APP端WebView消息处理
 */
const onAppMessage = (event) => {
  // #ifdef APP-PLUS
  console.log('📱 APP端收到WebView消息:', event)
  // #endif
}

/**
 * APP端WebView错误处理
 */
const onAppError = (event) => {
  // #ifdef APP-PLUS
  console.error('❌ APP端WebView加载错误:', event.detail)
  emit('message', {
    type: 'app-webview-error',
    error: event.detail
  })
  // #endif
}

/**
 * 处理SDK消息回调
 */
const handleMessage = (message) => {
  console.log('📨 收到PPT编辑器消息:', message)
  switch (message.type) {
    case 'mounted':
      emit('mounted', message.data)
      break
    default:
      // 转发其他消息
      emit('message', message)
      break
  }
}

/**
 * 加载SDK脚本
 */
const loadSDK = () => {
  // #ifdef H5
  return new Promise((resolve, reject) => {
    // 如果SDK已存在，直接返回
    if (checkSDK()) {
      resolve()
      return
    }
    // 动态加载SDK脚本
    const script = document.createElement('script')
    script.src = '/static/js/docmee-ui-sdk-iframe.min.js' // 从static目录加载
    script.onload = () => {
      console.log('✅ DocmeeUI SDK加载成功')
      resolve()
    }
    script.onerror = () => {
      console.error('❌ DocmeeUI SDK加载失败')
      reject(new Error('SDK加载失败'))
    }
    document.head.appendChild(script)
  })
  // #endif
  
  // #ifndef H5
  return Promise.reject(new Error('当前平台不支持'))
  // #endif
}

watchEffect(() => {
  if (props.token && checkSDK()) {
    setTimeout(() => {
      initPPTEditor()
    }, 500);
  }
})

onMounted(async () => {
  try {
    // #ifdef H5
    // H5端：直接加载SDK并初始化
    loadSDK()
    // #endif
    // #ifdef APP-PLUS
    // APP端：WebView会自动加载，等待load事件
    console.log('📱 APP端准备加载WebView，URL:', appWebViewSrc.value)
    // #endif
  } catch (err) {
    console.error('❌ 组件初始化失败:', err)
  }
})

onUnmounted(() => {
  // 清理资源
  if (docmeeUI) {
    docmeeUI = null
    console.log('🗑️ PPT编辑器实例已清理')
  }
})

// 暴露方法给父组件
defineExpose({})
</script>

<style scoped lang="scss">
.docmee-ui-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.ppt-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.app-webview {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  display: block;
}
</style> 