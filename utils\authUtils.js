// 登录状态管理工具

/**
 * 检查用户是否已登录
 * @returns {Boolean} 是否已登录
 */
export function isLoggedIn() {
  const token = uni.getStorageSync('token');
  const userInfo = uni.getStorageSync('userInfo') || {};  // 默认给 userInfo 赋值一个空对象，避免后续出现错误
  // 判断条件更简洁，提前检查 token 和 userInfo.is_activated
  return !!token && userInfo.is_activated === 1;
}

/**
 * 是否需要激活账号
 * @returns {Boolean} 是否需要激活账号
 */
export function needActivation() {
  const token = uni.getStorageSync('token');
  const userInfo = uni.getStorageSync('userInfo');
  return !!token && (!userInfo || userInfo.is_activated === 0);
}

/**
 * 退出登录
 * @param {Function} callback 退出登录后的回调函数
 */
export function logout(callback) {
  // 清除本地存储的用户信息
  uni.removeStorageSync('token');
  uni.removeStorageSync('userInfo');
  
  // 执行回调
  if (typeof callback === 'function') {
    callback();
  }
}