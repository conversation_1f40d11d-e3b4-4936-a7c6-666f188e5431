import { getCurrentVersion, shouldShowUpdateTip } from '../version.js';

/**
 * 全局版本检测器
 */
class VersionChecker {
  constructor() {
    this.hasShownUpdateTip = false;
    this.checkInterval = null;
  }

  /**
   * 检测版本更新
   */
  checkVersion() {
    // #ifdef H5
    try {
      // 如果已经显示过更新提示，不再重复检测
      if (this.hasShownUpdateTip) {
        return;
      }
      const currentVersion = getCurrentVersion();
      const storedVersion = uni.getStorageSync('app_version');
      if (shouldShowUpdateTip(storedVersion)) {
        console.log(`版本更新检测: ${storedVersion} -> ${currentVersion}`);
        this.showVersionUpdateTip();
        this.hasShownUpdateTip = true;
      } else if (!storedVersion) {
        // 首次访问，设置版本号
        uni.setStorageSync('app_version', currentVersion);
        console.log('首次访问，已设置版本号:', currentVersion);
      }
    } catch (error) {
      console.error('版本检测失败:', error);
    }
    // #endif
  }

  /**
   * 显示版本更新提示
   */
  showVersionUpdateTip() {
    // uni.showModal({
    //   title: '版本更新提示',
    //   content: '检测到应用已更新，为确保最佳体验，请刷新页面获取最新版本',
    //   confirmText: '立即刷新',
    //   cancelText: '稍后处理',
    //   success: (res) => {
    //     if (res.confirm) {
    //       // #ifdef H5
    //       try {
    //         uni.setStorageSync('app_version', getCurrentVersion());
    //         console.log('用户确认刷新，已更新版本号');
    //         // 强制刷新页面
    //         window.location.reload(true);
    //       } catch (error) {
    //         console.error('刷新失败:', error);
    //       }
    //       // #endif
    //     } else {
    //       console.log('用户选择稍后处理版本更新');
    //       // 用户选择稍后处理，重置标志，允许在下次页面显示时再次检测
    //       this.hasShownUpdateTip = false;
    //     }
    //   }
    // });
    window.location.reload(true);
  }

  /**
   * 重置检测状态（用于测试或特殊情况）
   */
  resetCheckState() {
    this.hasShownUpdateTip = false;
  }
}

// 创建全局实例
const versionChecker = new VersionChecker();

export default versionChecker;