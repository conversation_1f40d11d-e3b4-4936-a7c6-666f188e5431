<template>
  <view class="result-section">
    <!-- 统一的标题栏 -->
    <view class="section-header" @click="toggleExpand">
      <image
        class="expand-icon"
        src="/static/tabBar/down.png"
        :style="{ transform: isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)' }"
      />
      <view class="section-title">{{ title }}</view>
    </view>

    <!-- 统一的内容区域 -->
    <transition name="slide-fade">
      <view class="section-content" v-show="isExpanded">
        <SearchResultItem
          v-for="(item, index) in list"
          :key="index"
          :item="item"
          :type="getItemType()"
          :stages="stages"
          :is-last="index === list.length - 1"
          @click="handleItemClick"
          @checkbox-change="handleCheckboxChange"
        />
      </view>
    </transition>
  </view>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { updateTodoItem } from "@/http/todo.js";
import { openWebOfficePreview } from "@/utils/fileUtils.js";
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具
import { fetchUserConfig } from "@/http/user.js";
import SearchResultItem from './SearchResultItem.vue';

const emit = defineEmits(["update:expanded", "toggleExpand"]);
const props = defineProps({
  // 是否显示动作面板
  expanded: Boolean,
  // 标题
  title: String,
  list: {
    type: Array,
    default: [],
  },
  state: {
    type: Number,
    default: 1,
  },
});

// 简化展开状态管理 - 只管理当前组件的展开状态
const isExpanded = ref(false);

// 商机阶段选项 - 改为动态获取
const stages = ref([]);

// 获取项目类型
const getItemType = () => {
  const typeMap = {
    1: 'todo',
    2: 'customer',
    3: 'business',
    4: 'file'
  };
  return typeMap[props.state] || 'todo';
};

// 统一的项目点击处理
const handleItemClick = (item, type) => {
  switch (type) {
    case 'todo':
      handeToDoToEdit(item);
      break;
    case 'customer':
      handeCustomerToEdit(item);
      break;
    case 'business':
      handeBusinessToEdit(item);
      break;
    case 'file':
      handleFilePreview(item);
      break;
  }
};

// 【选中任务】
const handleCheckboxChange = async (evt, item) => {
  try {
    // 根据当前状态决定是选中还是取消选中
    const newStatus = item.is_finished === 1 ? false : true;
    const res = await updateTodoItem({
      id: item.id,
      is_finished: newStatus,
    });

    if (res.code === 0) {
      // 更新本地数据状态
      item.is_finished = newStatus ? 1 : 0;
      uni.showToast({
        title: newStatus ? "已完成" : "已取消",
        icon: "success",
        duration: 1500
      });
    } else {
      uni.showToast({
        title: "操作失败，请重试",
        icon: "none",
        duration: 2000
      });
    }
  } catch (error) {
    console.error("更新待办状态失败:", error);
    uni.showToast({
      title: "操作失败，请重试",
      icon: "none",
      duration: 2000
    });
  }
};
// 获取商机阶段配置
const getBusinessStages = async () => {
  try {
    const res = await fetchUserConfig();
    console.log("获取商机阶段配置:", res);
    if (res.code === 0 && res.data && res.data.business_opportunities) {
      const statusMap = res.data.business_opportunities.status_map || {};
      // 将接口返回的状态映射转换为下拉选项格式
      stages.value = Object.entries(statusMap).map(([value, text]) => ({
        value: parseInt(value), // 确保值是数字类型
        text,
      }));
    }
  } catch (err) {
    console.error("获取商机阶段配置失败:", err);
  }
};
// 修改切换方法
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};
// 商机点击
const handeBusinessToEdit = (item) => {
  const encodedTitle = encodeURIComponent(item.title);
  uni.navigateTo({
    url: `/pages/template/business-detail/index?business_id=${item.bo_id}&title=${encodedTitle}`,
  });
};
// 进入待办详情
const handeToDoToEdit = (item) => {
  uni.navigateTo({
    url: `/pages/template/todo-details/index?id=${item.id}`,
  });
};
// 进入客户详情
const handeCustomerToEdit = (item) => {
  const encodedTitle = encodeURIComponent(item.company_short_name);
  // 跳转到客户详情页面
  uni.navigateTo({
    url: `/pages/template/customer-details/index?customer_id=${item.customer_id}&title=${encodedTitle}`,
  });
};
/**
 * 处理文件预览
 * @param {Object} file - 文件对象
 */
const handleFilePreview = async (file) => {
  try {
    // 调用预览服务，传入不同平台的预览回调
    await previewFile(file, {
      onWebOfficePreview: openWebOfficePreview,// H5端WebOffice预览
    });
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};

// 监听 list 变化，当有数据时自动展开
watch(
  () => props.list,
  (newList) => {
    if (newList && newList.length > 0) {
      // 有数据时自动展开
      isExpanded.value = true;
    } else {
      // 无数据时收起
      isExpanded.value = false;
    }
  },
  { immediate: true }
); // immediate: true 确保首次加载时也会执行

onMounted(() => {
  // 获取商机阶段配置
  getBusinessStages();
});
</script>

<style lang="scss" scoped>
// 导入通用样式
@import "/styles/common-styles.scss";
@import "/styles/search-common.scss";

.result-section {
  @extend .result-section-base;
}
</style>
