<!-- 客户详情 -->
<template>
  <z-paging
    ref="pagingRef"
    v-model="dataList"
    @query="queryCustomerList"
    :refresherEnabled="false"
    :defaultPageSize="30"
    :showLoadingMoreNoMoreView="false"
    :style="appSafeAreaStyle"
  >
    <template #top>
      <uni-nav-bar
        left-icon="left"
        right-icon="more-filled"
        :title="customerTitle"
        title-text-align="center"
        backgroundColor="#fff"
        color="#000"
        height="60px"
        @clickLeft="handerLeftBack"
        @clickRight="handerRightEdit"
      />
      <z-tabs :list="tabList" :current="2" @change="tabsChange" />
    </template>
    <!-- 客户活动 -->
    <customerActivity
      v-if="tabIndex === 0"
      :dataList="dataList"
      @refresh="queryCustomerList"
    />
    <!-- 关联商机 -->
    <businessOpportunities
      v-if="tabIndex === 1"
      :businessList="dataList"
      :businessId="customer_id"
      @refresh="queryCustomerList"
    />
    <!-- 客户详情 -->
    <tabsCusDetails
      v-if="tabIndex === 2"
      :userDetails="customerDetailsData"
      @refresh="changeCustomerDetails"
    />
    <!-- 客户文件 -->
    <customerFile
      v-if="tabIndex === 3"
      :fileList="dataList"
      @refresh="queryCustomerList"
      :customer_id="customer_id"
      :customer_parent_id="customer_parent_id"
    />
    <ActionSheet
      v-if="customerShow"
      v-model:show="customerShow"
      :actions="actions"
      title="操作"
      @select="onFileSelect"
    />
    <!-- AI 对话组件 -->
    <CustomerService />
  </z-paging>
</template>
<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import customerActivity from "./customer-activity.vue";
import businessOpportunities from "./business-opportunities.vue";
import tabsCusDetails from "./tabs-cus-details.vue";
import customerFile from "./customer-file.vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import CustomerService from "@/components/CustomerService.vue";
import { eventBus } from "@/utils/eventBus.js";
import { updateTodoItem, getTodoList } from "@/http/todo.js";
import { fetchOpportunityList } from "@/http/business.js";
import { clearLinkTapStatus } from '@/utils/globalState.js';
import {
  fetchAttachmentList,
  saveOrEditAttachment,
} from "@/http/attachment.js";
import { deleteCustomer, getCustomerDetail } from "@/http/customer.js";

const pagingRef = ref(null);
// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
const dataList = ref([]);
const tabIndex = ref(2);
const customerTitle = ref("客户详情");
const customer_id = ref("");
const customer_parent_id = ref();
const customerShow = ref(false);
const actions = ref([{ name: "删除", color: "danger" }]);
// 客户详情数据
const customerDetailsData = ref({});
const tabList = ref(["客户活动", "关联商机", "客户详情", "客户文件"]);
// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`,
  };
  // #endif
  return {};
});
// 【顶部导航栏】左侧按钮点击时触发
const handerLeftBack = () => {
  clearLinkTapStatus()
  uni.switchTab({
    url: "/pages/tabBar/customer/customer",
  });
};
// 【顶部导航栏】右侧按钮点击时触发
const handerRightEdit = () => {
  customerShow.value = true;
};
const tabsChange = (index) => {
  tabIndex.value = index;
  // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
  pagingRef.value.reload();
};
// 【顶部导航栏】修改客户详情
const changeCustomerDetails = () => {
  eventBus.emit("customerUpdated", customer_id.value);
};
// 点击选项时触发，禁用或加载状态下不会触发
const onFileSelect = (item) => {
  switch (item.name) {
    case "删除":
      onDeleteCustomer();
      customerShow.value = false;
      break;
  }
};

// 删除客户
const onDeleteCustomer = async () => {
  try {
    const res = await deleteCustomer({
      customer_id: customer_id.value,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除成功",
        icon: "success",
      });
      // 触发全局事件，通知列表页刷新
      // eventBus.emit("customerUpdated", customer_id.value);
      uni.switchTab({
        url: "/pages/tabBar/customer/customer",
      });
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};

// 获取客户详情
const getCustomerOpportunityDetails = async () => {
  try {
    const res = await getCustomerDetail({
      customer_id: customer_id.value,
      type: tabIndex.value + 1,
    });
    if (res.code === 0) {
      console.log("客户详情:", res);
      customerDetailsData.value = res.data;
      customerTitle.value = res.data.company_short_name || res.data.company_name
    }
  } catch (error) {
    console.error("获取客户详情失败:", err);
  }
};

// 初始化代办列表
const queryCustomerList = async (pageNo, pageSize) => {
  try {
    // 获取待办任务列表数据
    if (tabIndex.value === 0) {
      const res = await getTodoList({
        page: pageNo || 1,
        limit: pageSize || 30,
        customer_id: customer_id.value,
        type: tabIndex.value + 1,
      });
      const pageData = res.data.list || [];
      console.log("客户活动----------:", pageData);
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
    // 获取待办商机列表数据
    if (tabIndex.value === 1) {
      const res = await fetchOpportunityList({
        page: pageNo || 1,
        limit: pageSize || 30,
        customer_id: customer_id.value,
        type: tabIndex.value + 1,
      });
      const pageData = res.data.list || [];
      console.log("关联商机:", res);
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
    // 获取客户详情数据
    if (tabIndex.value === 2) {
      await getCustomerOpportunityDetails();
    }
    // 获取客户文件数据
    if (tabIndex.value === 3) {
      const res = await fetchAttachmentList({
        // 1 客户文件，2 商机文件，3 产品介绍，4 客户资料
        classify: 1,
        classify_id: customer_id.value,
        page: 1,
        limit: 1000,
      });
      const pageData = res.data?.list || [];
      pageData.forEach((element) => {
        element.isShowInput = false;
      });
      console.log("客户文件:", pageData);
      customer_parent_id.value = pageData[0]?.parent_id;
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
  } catch (err) {
    console.error("客户请求失败:", err);
  }
};

// 使用 Uni-app 官方生命周期
onLoad(async (options) => {
  // 这里 options 包含所有平台的参数
  console.log("路由参数:", options);
  if (options.customer_id) {
    customer_id.value = options.customer_id;
    // 等待获取商机详情数据完成
    await getCustomerOpportunityDetails();
  }
});

onMounted(() => {});
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/public-layout.scss';
</style>
