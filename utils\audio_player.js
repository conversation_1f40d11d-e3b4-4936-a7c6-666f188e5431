class PCMAudioPlayer {
    constructor(sampleRate) {
        this.sampleRate = sampleRate;
        this.audioContext = null;
        this.audioQueue = [];
        this.isPlaying = false;
        this.currentSource = null;
        this.isPaused = false;
        this.pausedAt = 0;
        this.startedAt = 0;
        this.currentBuffer = null; // 存储当前缓冲区用于恢复播放
        this.offset = 0; // 播放位置偏移量
        this.onPlaybackEnd = null; // 播放结束回调函数
        this.lastPlaybackTime = 0; // 上次播放结束的时间戳
        this.debounceTimeout = null; // 防抖定时器
        this.debounceDelay = 500; // 防抖延迟时间(毫秒)
    }

    connect() {
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
    }

    pushPCM(arrayBuffer) {
        this.audioQueue.push(arrayBuffer);
        this._playNextAudio();
    }

    /**
     * 将arrayBuffer转为audioBuffer
     */
    _bufferPCMData(pcmData) {
        const sampleRate = this.sampleRate; // 设置为 PCM 数据的采样率
        const length = pcmData.byteLength / 2; // 假设 PCM 数据为 16 位，需除以 2
        const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);
        const channelData = audioBuffer.getChannelData(0);
        const int16Array = new Int16Array(pcmData); // 将 PCM 数据转换为 Int16Array

        for (let i = 0; i < length; i++) {
            // 将 16 位 PCM 转换为浮点数 (-1.0 到 1.0)
            channelData[i] = int16Array[i] / 32768; // 16 位数据转换范围
        }
        let audioLength = length/sampleRate*1000;
        console.log(`准备音频: ${length} 采样, ${audioLength} 毫秒`);
        
        // 记录当前音频片段的时长，用于防抖判断
        this.currentAudioDuration = audioLength;
        
        return audioBuffer;
    }

    async _playAudio(arrayBuffer) {
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }

        const audioBuffer = this._bufferPCMData(arrayBuffer);
        this.currentBuffer = audioBuffer; // 保存当前缓冲区用于暂停/恢复

        this.currentSource = this.audioContext.createBufferSource();
        this.currentSource.buffer = audioBuffer;
        this.currentSource.connect(this.audioContext.destination);

        this.currentSource.onended = () => {
            // 只有在非暂停状态下才触发结束逻辑
            if (!this.isPaused) {
                console.log('音频播放结束');
                this.isPlaying = false;
                this.currentSource = null;
                this.currentBuffer = null;
                this.offset = 0;
                
                // 使用防抖处理回调函数
                this._debouncedPlaybackEnd();
                
                this._playNextAudio(); // 播放队列中的下一个音频
            }
        };

        // 从偏移量开始播放(新播放为0，恢复播放为非0)
        this.startedAt = this.audioContext.currentTime - this.offset;
        this.currentSource.start(0, this.offset);
        this.isPlaying = true;
    }

    _playNextAudio() {
        if (this.audioQueue.length > 0 && !this.isPlaying && !this.isPaused) {
            // 计算总的字节长度
            const totalLength = this.audioQueue.reduce((acc, buffer) => acc + buffer.byteLength, 0);
            const combinedBuffer = new Uint8Array(totalLength);
            let offset = 0;

            // 将所有 audioQueue 中的 buffer 拼接到一个新的 Uint8Array 中
            for (const buffer of this.audioQueue) {
                combinedBuffer.set(new Uint8Array(buffer), offset);
                offset += buffer.byteLength;
            }

            // 清空 audioQueue，因为我们已经拼接完所有数据
            this.audioQueue = [];
            // 发送拼接的 audio 数据给 playAudio
            this._playAudio(combinedBuffer.buffer);
        }
    }
    
    /**
     * 暂停当前音频播放
     */
    pause() {
        if (this.isPlaying && this.currentSource && !this.isPaused) {
            // 计算我们在音频中的位置
            this.pausedAt = this.audioContext.currentTime;
            this.offset = this.pausedAt - this.startedAt;
            
            // 停止当前播放
            this.currentSource.stop();
            this.currentSource = null;
            
            // 标记为暂停但总体仍然"播放中"
            this.isPaused = true;
            console.log('音频已暂停，位置:', this.offset);
        }
    }
    
    /**
     * 从暂停位置恢复播放
     */
    resume() {
        if (this.isPaused && this.currentBuffer) {
            this.isPaused = false;
            
            // 创建新的音频源并从存储的偏移量开始播放
            this.currentSource = this.audioContext.createBufferSource();
            this.currentSource.buffer = this.currentBuffer;
            this.currentSource.connect(this.audioContext.destination);
            
            this.currentSource.onended = () => {
                if (!this.isPaused) {
                    console.log('音频播放结束');
                    this.isPlaying = false;
                    this.isPaused = false;
                    this.currentSource = null;
                    this.currentBuffer = null;
                    this.offset = 0;
                    // 如果设置了回调函数，则调用
                    if (typeof this.onPlaybackEnd === 'function') {
                        this.onPlaybackEnd();
                    }
                    this._playNextAudio(); // 播放队列中的下一个音频
                }
            };
            
            // 从偏移量开始播放
            this.startedAt = this.audioContext.currentTime - this.offset;
            this.currentSource.start(0, this.offset);
            console.log('音频已恢复，从位置:', this.offset);
        }
    }
    
    stop() {
        if (this.currentSource) {
            this.currentSource.stop(); // 停止当前音频播放
            this.currentSource = null; // 清除音频源引用
        }
        this.isPlaying = false; // 更新播放状态
        this.isPaused = false;  // 重置暂停状态
        this.offset = 0;        // 重置播放位置
        this.currentBuffer = null; // 清除当前缓冲区
        this.audioQueue = []; // 清空音频队列
        
        // 清除防抖定时器
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
            this.debounceTimeout = null;
        }
        
        console.log('播放停止并清空队列');
    }
    /**
     * 使用防抖处理播放结束回调
     * 只有在指定时间内没有新的音频播放结束事件时，才会触发回调
     */
    _debouncedPlaybackEnd() {
        const now = Date.now();
        this.lastPlaybackTime = now;
        
        // 清除之前的定时器
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
        }
        
        // 设置新的定时器
        this.debounceTimeout = setTimeout(() => {
            // 检查是否有新的音频片段在播放
            if (this.audioQueue.length === 0 && !this.isPlaying) {
                console.log('触发最终的播放结束回调');
                // 如果设置了回调函数，则调用
                if (typeof this.onPlaybackEnd === 'function') {
                    this.onPlaybackEnd();
                }
            }
        }, this.debounceDelay);
    }
    
    /**
     * 设置播放结束回调函数
     * @param {Function} callback - 播放结束时调用的回调函数
     * @param {number} [debounceTime=500] - 可选的防抖时间(毫秒)
     */
    setOnPlaybackEndCallback(callback, debounceTime) {
        this.onPlaybackEnd = callback;
        // 允许自定义防抖时间
        if (typeof debounceTime === 'number' && debounceTime > 0) {
            this.debounceDelay = debounceTime;
        }
    }
}

export default PCMAudioPlayer;