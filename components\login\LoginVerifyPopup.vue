<template>
  <view class="login-verify-container">
    <view class="popup-mask" v-if="modelValue" @click.stop="handleMaskClick">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">身份验证</text>
          <view class="close-icon" @click.stop="close">
            <uni-icons type="close" size="20" color="#999"></uni-icons>
          </view>
        </view>

        <view class="popup-body">
          <!-- 手机号输入 -->
          <view class="input-group">
            <input
              class="input-field"
              type="number"
              placeholder="请输入手机号"
              v-model="mobile"
              maxlength="11"
            />
          </view>

          <!-- 验证码输入 -->
          <view class="input-group code-group">
            <input
              class="input-field"
              type="number"
              placeholder="请输入验证码"
              v-model="code"
              maxlength="6"
            />
            <view
              class="code-button"
              :class="{ disabled: isSending || !isValidMobile }"
              @click="sendVerifyCode"
            >
              {{ codeButtonText }}
            </view>
          </view>

          <!-- 下一步按钮 -->
          <view
            class="submit-button"
            :class="{ disabled: !isFormValid }"
            @click="handleLogin"
          >
            下一步
          </view>
        </view>
      </view>
    </view>
    <!-- 激活账号弹框组件 -->
    <ActivateAccountPopup
      v-if="showActivateForm"
      v-model="showActivateForm"
      :token="tempToken"
      @activate-success="handleActivateSuccess"
      @activate-login="handleActivateLogin"
    />
  </view>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, onMounted } from "vue";
import { login, sendCode } from "@/http/auth.js";
import { getUserInfo } from "@/http/user.js";
import ActivateAccountPopup from "./ActivateAccountPopup.vue";
import pushNotificationService from "@/utils/pushNotificationService.js";

// 使用defineModel实现双向绑定
const modelValue = defineModel(false);

const emit = defineEmits(["login-success"]);

const mobile = ref("");
const code = ref("");
const isSending = ref(false);
const countdown = ref(60);
const timer = ref(null);
// 激活账号相关状态
const tempToken = ref("");
const showActivateForm = ref(false);

const isValidMobile = computed(() => {
  return /^1\d{10}$/.test(mobile.value);
});

const isFormValid = computed(() => {
  return isValidMobile.value && code.value.length === 6;
});

const codeButtonText = computed(() => {
  if (isSending.value) {
    return `${countdown.value}秒后重发`;
  }
  return "获取验证码";
});

const close = () => {
  // 重置状态
  tempToken.value = "";
  showActivateForm.value = false;
  modelValue.value = false;
};

const handleMaskClick = () => {
  // 点击遮罩层不关闭弹窗
};

const startCountdown = () => {
  isSending.value = true;
  countdown.value = 60;
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      isSending.value = false;
      clearInterval(timer.value);
    }
  }, 1000);
};

const sendVerifyCode = async () => {
  if (!isValidMobile.value || isSending.value) {
    return;
  }

  try {
    const res = await sendCode({
      mobile: mobile.value,
    });

    if (res.code === 0) {
      uni.showToast({
        title: "验证码已发送",
        icon: "none",
      });
      startCountdown();
    } else {
      uni.showToast({
        title: res.msg || "发送验证码失败",
        icon: "none",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "发送验证码失败",
      icon: "none",
    });
    console.error("发送验证码失败:", error);
  }
};

// 处理激活成功回调
const handleActivateSuccess = async () => {
  try {
    // 获取用户信息
    const userInfoRes = await getUserInfo();
    if (userInfoRes.code === 0) {
      uni.setStorageSync("userInfo", userInfoRes.data);
      // 通知登录成功
      const data = {
        token: tempToken.value,
        userInfo: userInfoRes.data,
      };
      emit("login-success", data);
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
  }
  // 关闭弹窗
  close();
};

// 处理登录未激活回调
const handleActivateLogin = async (userInfo) => {
  console.error("userInfo", userInfo);
  // 已登陆未激活
  if (userInfo.is_activated === 0) {
    modelValue.value = false;
    showActivateForm.value = true;
  } else {
    const data = {
      token: tempToken.value,
      userInfo: userInfo,
    };
    emit("login-success", data, false);
  }
};

const handleLogin = async () => {
  if (!isFormValid.value) {
    return;
  }
  try {
    // 确保获取到推送客户端ID
    const pushClientId = await pushNotificationService.ensureClientId();
    // console.log('🔐 登录时使用的推送客户端ID:', pushClientId);
    const res = await login({
      mobile: mobile.value,
      code: code.value,
      cid: pushClientId, // 添加推送客户端ID参数
    });
    if (res.code === 0) {
      // 需要激活账号，关闭身份验证弹框，显示激活账号弹框
      tempToken.value = res.data.token;
      // 只保存token
      uni.setStorageSync("token", res.data.token);
      console.error('存储整个登录响应数据',res.data);
      // 登录成功，检查是否需要激活账号
      if (res.data && res.data.is_active === 0) {
        modelValue.value = false; // 关闭身份验证弹框
        showActivateForm.value = true; // 打开激活账号弹框
      } else {
        try {
          // 获取用户信息
          const userInfoRes = await getUserInfo();
          if (userInfoRes.code === 0) {
            uni.setStorageSync("userInfo", userInfoRes.data);
            // 通知登录成功
            const data = {
              token: res.data.token,
              userInfo: userInfoRes.data,
            };
            emit("login-success", data);
          }
        } catch (error) {
          console.error("获取用户信息失败:", error);
        }
        close();
      }
    }
  } catch (error) {
    uni.showToast({
      title: "登录失败",
      icon: "none",
    });
  }
};

// 外部组件调用时检查登录状态
onMounted(async () => {
  // 未登录状态下-检查是否有token-获取用户信息
  const token = uni.getStorageSync("token");
  if (token !== undefined && token !== null && token !== "") {
    tempToken.value = token;
    // 获取最新的用户信息
    const userInfoRes = await getUserInfo();
    if (userInfoRes.code === 0) {
      uni.setStorageSync("userInfo", userInfoRes.data);
      const data = {
        token: tempToken.value,
        userInfo: userInfoRes.data,
      };
      // 如果用户信息未激活，关闭身份验证弹框，显示激活账号弹框
      if (userInfoRes.data.is_activated === 0) {
        // 已登陆未激活
        modelValue.value = false; // 关闭身份验证弹框
        showActivateForm.value = true; // 打开激活账号弹框
      } else {
        emit("login-success", data, false);
      }
    }
  }
});

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>

<style scoped lang="scss">
// 变量定义
$primary-color: #000;
$primary-disabled: #ccc;
$text-color: #333;
$text-secondary: #999;
$border-color: #e5e5e5;
$background-light: #f5f5f5;
$background-divider: #f0f0f0;
$white: #fff;
$mask-color: rgba(0, 0, 0, 0.5);

// 尺寸变量
$border-radius-small: 4px;
$border-radius-medium: 12px;
$input-height: 45px;
$button-height: 45px;
$code-button-width: 110px;

// 间距变量
$spacing-small: 5px;
$spacing-medium: 10px;
$spacing-large: 15px;
$spacing-xl: 20px;

/**
 * 登录验证弹窗容器
 */
.login-verify-container {
  // 弹窗遮罩层
  .popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $mask-color;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  // 弹窗内容容器
  .popup-content {
    // width: 80%;
    background-color: $white;
    border-radius: $border-radius-medium;
    overflow: hidden;
    max-width: 750px;
    margin: 0 auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  // 弹窗头部
  .popup-header {
    position: relative;
    padding: $spacing-xl 0;
    text-align: center;
    border-bottom: 1px solid $background-divider;

    // 弹窗标题
    .popup-title {
      font-size: 18px;
      font-weight: 500;
      color: $text-color;
    }

    // 关闭图标
    .close-icon {
      position: absolute;
      right: $spacing-large;
      top: 50%;
      transform: translateY(-50%);
      padding: $spacing-small;
      cursor: pointer;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }

      &:active {
        opacity: 0.5;
      }
    }
  }

  // 弹窗主体内容
  .popup-body {
    padding: $spacing-xl;

    // 输入组容器
    .input-group {
      margin-bottom: $spacing-large;

      // 输入框
      .input-field {
        width: 100%;
        height: $input-height;
        border: 1px solid $border-color;
        border-radius: $border-radius-small;
        padding: 0 $spacing-large;
        font-size: 15px;
        box-sizing: border-box;
        transition: border-color 0.2s ease;

        &:focus {
          border-color: $primary-color;
          outline: none;
        }

        &::placeholder {
          color: $text-secondary;
        }
      }

      // 验证码输入组
      &.code-group {
        display: flex;
        align-items: center;

        .input-field {
          flex: 1;
        }

        // 验证码按钮
        .code-button {
          width: $code-button-width;
          height: $button-height;
          background-color: $background-light;
          color: $text-color;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: $spacing-medium;
          border-radius: $border-radius-small;
          cursor: pointer;
          transition: all 0.2s ease;
          user-select: none;

          &:hover:not(.disabled) {
            background-color: darken($background-light, 5%);
          }

          &:active:not(.disabled) {
            background-color: darken($background-light, 10%);
          }

          &.disabled {
            color: $text-secondary;
            background-color: $background-light;
            cursor: not-allowed;
            opacity: 0.6;
          }
        }
      }
    }

    // 提交按钮
    .submit-button {
      height: $button-height;
      background-color: $primary-color;
      color: $white;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: $border-radius-small;
      margin-top: $spacing-xl;
      cursor: pointer;
      transition: all 0.2s ease;
      user-select: none;

      &:hover:not(.disabled) {
        background-color: lighten($primary-color, 10%);
      }

      &:active:not(.disabled) {
        background-color: darken($primary-color, 5%);
        transform: translateY(1px);
      }

      &.disabled {
        background-color: $primary-disabled;
        cursor: not-allowed;
        opacity: 0.6;

        &:hover {
          background-color: $primary-disabled;
          transform: none;
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 480px) {
  .login-verify-container {
    .popup-content {
      // width: 90%;
      margin: 0 $spacing-medium;
      max-width: 375px;
    }

    .popup-body {
      padding: $spacing-large;

      .input-group {
        &.code-group {
          .code-button {
            width: 90px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 动画效果
.popup-mask {
  animation: fadeIn 0.3s ease-out;
}

.popup-content {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>