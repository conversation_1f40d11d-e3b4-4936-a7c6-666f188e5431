<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <title>PPT编辑器</title>
    <style type="text/css">
        body {
            margin: 0;
        }
        
        #ppt-container {
            width: 100%;
            padding-top: 35px;
            height: calc(100vh - 35px);
            position: relative;
        }
        
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        
        .error-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
            text-align: center;
            color: #333;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">正在加载PPT编辑器...</div>
    <div id="error-container" style="display: none;"></div>
    <div id="ppt-container"></div>

    <script type="text/javascript">
        /**
         * 获取URL参数 - 兼容不支持URLSearchParams的环境
         */
        function getUrlParams() {
            const search = window.location.search;
            const params = {};
            
            if (search) {
                // 移除开头的?号
                const queryString = search.substring(1);
                // 分割参数
                const pairs = queryString.split('&');
                
                for (let i = 0; i < pairs.length; i++) {
                    const pair = pairs[i].split('=');
                    if (pair.length === 2) {
                        const key = decodeURIComponent(pair[0]);
                        const value = decodeURIComponent(pair[1]);
                        params[key] = value;
                    }
                }
            }
            
            return {
                token: params.token || 'sk_6KAvUf5vTrFsvrnCq5',
                pptId: params.pptId || '1935894086229823488',
                page: params.page || 'editor',
                animation: params.animation || false,
                mode: params.mode || 'light',
                lang: params.lang || 'zh',
                // padding: "80px 20px 0px",
                isMobile: true, // APP端强制为移动端模式
            };
        }
        
        /**
         * 显示错误信息
         */
        function showError(message) {
            const loading = document.getElementById('loading');
            const errorContainer = document.getElementById('error-container');
            if (loading) loading.style.display = 'none';
            errorContainer.innerHTML = `<div class="error-message">${message}</div>`;
            errorContainer.style.display = 'block';
        }
        
        /**
         * 隐藏加载状态
         */
        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) loading.style.display = 'none';
        }
        
        /**
         * 加载PPT编辑器SDK - 直接使用CDN
         */
        function loadPPTSDK() {
            return new Promise((resolve, reject) => {
                const cdnList = [
                    'https://unpkg.com/@docmee/sdk-ui@latest/dist/index.global.js',
                    'https://cdn.jsdelivr.net/npm/@docmee/sdk-ui@latest/dist/index.global.js'
                ];
                
                let currentIndex = 0;
                
                function tryLoad() {
                    if (currentIndex >= cdnList.length) {
                        reject(new Error('所有CDN都加载失败'));
                        return;
                    }
                    const script = document.createElement('script');
                    script.src = cdnList[currentIndex];
                    script.onload = () => {
                        console.log(`✅ PPT SDK加载成功: ${cdnList[currentIndex]}`);
                        resolve(cdnList[currentIndex]);
                    };
                    script.onerror = () => {
                        console.warn(`⚠️ PPT SDK加载失败: ${cdnList[currentIndex]}`);
                        currentIndex++;
                        tryLoad();
                    };
                    document.head.appendChild(script);
                }
                
                tryLoad();
            });
        }
        
        /**
         * 初始化PPT编辑器
         */
        function initPPTEditor() {
            const params = getUrlParams();
            const container = document.getElementById('ppt-container');
            
            if (!container) {
                showError('容器元素不存在');
                return;
            }
            
            if (!window.DocmeeUI) {
                showError('PPT编辑器SDK未加载');
                return;
            }
            
            try {
                console.log('🚀 初始化PPT编辑器', params);
                
                const config = {
                    ...params,
                    container: container,
                    onMessage: (message) => {
                        console.log('📨 PPT编辑器消息:', message);
                    }
                };
                
                // 创建PPT编辑器实例
                const docmeeUI = new window.DocmeeUI(config);
                
                // 隐藏加载状态
                hideLoading();
                
                console.log('✅ PPT编辑器初始化完成');
                
            } catch (error) {
                console.error('❌ PPT编辑器初始化失败:', error);
                showError('PPT编辑器初始化失败: ' + error.message);
            }
        }
        
        /**
         * 主初始化函数
         */
        function init() {
            console.log('🚀 开始加载PPT编辑器');
            
            // 直接加载PPT SDK
            loadPPTSDK()
                .then((source) => {
                    console.log(`✅ PPT SDK加载成功 (${source})`);
                    initPPTEditor();
                })
                .catch((error) => {
                    console.error('❌ SDK加载失败:', error);
                    showError('SDK加载失败: ' + error.message);
                });
        }
        
        // 页面加载完成后直接初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面DOM加载完成');
            init();
        });
        
        // 如果DOM已经加载完成，直接初始化
        if (document.readyState === 'loading') {
            // DOM还在加载中，等待DOMContentLoaded事件
        } else {
            // DOM已加载完成，直接初始化
            init();
        }
    </script>
</body>
</html> 