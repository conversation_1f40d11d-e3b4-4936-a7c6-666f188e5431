<template>
  <view class="datetime-picker">
    <!-- 选择器触发区域 -->
    <view class="picker-container" @click="showPicker">
      <view class="picker-icon">
        <uni-icons :type="iconType" size="16" color="#999"></uni-icons>
      </view>
      <view class="picker-value" :class="{ placeholder: !displayValue }">
        {{ displayValue || placeholder }}
      </view>
      <view v-if="modelValue && showClear" class="clear-icon" @click.stop="clearValue">
        <uni-icons type="clear" size="16" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 弹出层 -->
    <uni-popup ref="popup" type="bottom">
      <view class="popup-content">
        <!-- 头部操作栏 -->
        <view class="popup-header">
          <text class="cancel-btn" @click="cancelPicker">取消</text>
          <text class="title">{{ pickerTitle }}</text>
          <text class="confirm-btn" @click="confirmPicker">确定</text>
        </view>

        <!-- 选择器主体 -->
        <view class="picker-body">
          <picker-view
            class="picker-view"
            :value="currentPickerValue"
            :indicator-style="indicatorStyle"
            @change="onPickerChange"
          >
            <!-- 年份列 -->
            <picker-view-column v-if="showYearColumn">
              <view class="picker-item" v-for="(item,index) in columns.years" :key="index">{{ getFormatter()('year', item) }}</view>
            </picker-view-column>
            
            <!-- 月份列 -->
            <picker-view-column v-if="showMonthColumn">
              <view class="picker-item" v-for="(item,index) in columns.months" :key="index">{{ getFormatter()('month', parseInt(item)) }}</view>
            </picker-view-column>
            
            <!-- 日期列 -->
            <picker-view-column v-if="showDayColumn">
              <view class="picker-item" v-for="(item,index) in columns.days" :key="index">{{ getFormatter()('day', parseInt(item)) }}</view>
            </picker-view-column>
            
            <!-- 小时列 -->
            <picker-view-column v-if="showHourColumn">
              <view class="picker-item" v-for="(item,index) in columns.hours" :key="index">{{ getFormatter()('hour', parseInt(item)) }}</view>
            </picker-view-column>
            
            <!-- 分钟列 -->
            <picker-view-column v-if="showMinuteColumn">
              <view class="picker-item" v-for="(item,index) in columns.minutes" :key="index">{{ getFormatter()('minute', parseInt(item)) }}</view>
            </picker-view-column>
        </picker-view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";

const props = defineProps({
  // 绑定值，选中的日期时间
  modelValue: {
    type: [String, Number, Date],
    default: null
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: "请选择日期时间"
  },
  // 选择器类型
  type: {
    type: String,
    default: "datetime", // date, year-month, month-day, time, datetime, datehour
    validator: (value) => {
      return ["date", "year-month", "month-day", "time", "datetime", "datehour"].includes(value);
    }
  },
  // 最小日期
  minDate: {
    type: [Date, String, Number],
    default: () => new Date(new Date().getFullYear() - 10, 0, 1)
  },
  // 最大日期
  maxDate: {
    type: [Date, String, Number],
    default: () => new Date(new Date().getFullYear() + 10, 11, 31)
  },
  // 格式化函数
  formatter: {
    type: Function,
    default: null
  },
  // 选项过滤函数
  filter: {
    type: Function,
    default: null
  },
  // 是否显示清除按钮
  showClear: {
    type: Boolean,
    default: true
  },
  // 输出的日期格式
  format: {
    type: String,
    default: "" // 为空时根据type自动选择格式
  }
});

const emit = defineEmits(["update:modelValue", "change", "confirm", "cancel", "click"]);

// 内部格式化函数
const internalFormatter = (type, value) => {
  if (type === 'year') return `${value}年`;
  if (type === 'month') return `${value}月`;
  if (type === 'day') return `${value}日`;
  if (type === 'hour') return `${value}时`;
  if (type === 'minute') return `${value}分`;
  return value;
};

// 获取格式化函数
const getFormatter = () => {
  return props.formatter || internalFormatter;
};

// 弹窗引用
const popup = ref(null);
// 当前选中的日期
const currentDate = ref(null);
// 临时选择的日期值
const tempDate = ref(null);
// 当前选择器的值
const currentPickerValue = ref([]);

// 指示器样式
const indicatorStyle = 'height: 34px; line-height: 34px;background-color: rgba(0, 122, 255, 0.1);';

// 根据类型计算显示哪些列
const showYearColumn = computed(() => {
  return ["date", "year-month", "datetime", "datehour"].includes(props.type);
});

const showMonthColumn = computed(() => {
  return ["date", "year-month", "month-day", "datetime", "datehour"].includes(props.type);
});

const showDayColumn = computed(() => {
  return ["date", "month-day", "datetime", "datehour"].includes(props.type);
});

const showHourColumn = computed(() => {
  return ["time", "datetime", "datehour"].includes(props.type);
});

const showMinuteColumn = computed(() => {
  return ["time", "datetime"].includes(props.type);
});

// 图标类型
const iconType = computed(() => {
  return ["time"].includes(props.type) ? "calendar-filled" : "calendar";
});

// 计算选择器标题
const pickerTitle = computed(() => {
  const titles = {
    date: "选择日期",
    "year-month": "选择年月",
    "month-day": "选择月日",
    time: "选择时间",
    datetime: "选择日期时间",
    datehour: "选择日期和小时"
  };
  return titles[props.type] || "选择";
});

// 获取有效的最小日期
const getValidMinDate = () => {
  const minDate = new Date(props.minDate);
  return isNaN(minDate.getTime()) ? new Date(new Date().getFullYear() - 10, 0, 1) : minDate;
};

// 获取有效的最大日期
const getValidMaxDate = () => {
  const maxDate = new Date(props.maxDate);
  return isNaN(maxDate.getTime()) ? new Date(new Date().getFullYear() + 10, 11, 31) : maxDate;
};

// 列数据
const columns = computed(() => {
  const result = {
    years: [],
    months: [],
    days: [],
    hours: [],
    minutes: []
  };

  const minDate = getValidMinDate();
  const maxDate = getValidMaxDate();

  // 确保最小日期不超过最大日期
  if (minDate > maxDate) {
    return result;
  }

  // 生成年份列表
  if (showYearColumn.value) {
    for (let i = minDate.getFullYear(); i <= maxDate.getFullYear(); i++) {
      result.years.push(i);
    }
  }

  // 根据当前选择的年份，确定月份范围
  const tempYear = tempDate.value ? tempDate.value.getFullYear() : new Date().getFullYear();
  const minMonth = tempYear === minDate.getFullYear() ? minDate.getMonth() + 1 : 1;
  const maxMonth = tempYear === maxDate.getFullYear() ? maxDate.getMonth() + 1 : 12;

  // 生成月份列表
  if (showMonthColumn.value) {
    for (let i = minMonth; i <= maxMonth; i++) {
      result.months.push(i < 10 ? `0${i}` : `${i}`);
    }
  }

  // 根据当前选择的年月，确定日期范围
  const tempMonth = tempDate.value ? tempDate.value.getMonth() + 1 : new Date().getMonth() + 1;
  let minDay = 1;
  let maxDay = getDaysInMonth(tempYear, tempMonth);

  if (tempYear === minDate.getFullYear() && tempMonth === minDate.getMonth() + 1) {
    minDay = minDate.getDate();
  }
  if (tempYear === maxDate.getFullYear() && tempMonth === maxDate.getMonth() + 1) {
    maxDay = maxDate.getDate();
  }

  // 生成日期列表
  if (showDayColumn.value) {
    for (let i = minDay; i <= maxDay; i++) {
      result.days.push(i < 10 ? `0${i}` : `${i}`);
    }
  }

  // 生成小时列表
  if (showHourColumn.value) {
    for (let i = 0; i < 24; i++) {
      result.hours.push(i < 10 ? `0${i}` : `${i}`);
    }
  }

  // 生成分钟列表
  if (showMinuteColumn.value) {
    for (let i = 0; i < 60; i++) {
      result.minutes.push(i < 10 ? `0${i}` : `${i}`);
    }
  }

  // 应用过滤器
  if (props.filter) {
    result.years = result.years.filter(year => props.filter('year', year));
    result.months = result.months.filter(month => props.filter('month', parseInt(month)));
    result.days = result.days.filter(day => props.filter('day', parseInt(day)));
    result.hours = result.hours.filter(hour => props.filter('hour', parseInt(hour)));
    result.minutes = result.minutes.filter(minute => props.filter('minute', parseInt(minute)));
  }

  return result;
});

// 获取月份的天数
const getDaysInMonth = (year, month) => {
  return new Date(year, month, 0).getDate();
};

// 清除值
const clearValue = (e) => {
  if (e) e.stopPropagation();
  currentDate.value = null;
  emit("update:modelValue", null);
  emit("change", null);
};

// 显示选择器
const showPicker = () => {
  emit("click");
  if (popup.value) {
    // 初始化临时日期
    initTempDate();
    // 打开弹窗
    popup.value.open();
  }
};

// 初始化临时日期
const initTempDate = () => {
  let date;
  
  if (currentDate.value) {
    date = new Date(currentDate.value);
  } else {
    date = new Date();
    
    // 对于特定类型，设置特定的默认值
    if (props.type === 'year-month') {
      date.setDate(1);
    } else if (props.type === 'month-day') {
      // 对于month-day类型，如果有modelValue，尝试解析
      if (props.modelValue && typeof props.modelValue === 'string' && props.modelValue.includes('-')) {
        const [month, day] = props.modelValue.split('-').map(num => parseInt(num, 10));
        if (!isNaN(month) && !isNaN(day)) {
          date.setMonth(month - 1);
          date.setDate(day);
        }
      }
      // 保持当前月日，年份设为当前年
    } else if (props.type === 'time') {
      // 对于纯时间类型，日期部分不重要
    }
  }
  
  // 确保日期在有效范围内
  const minDate = getValidMinDate();
  const maxDate = getValidMaxDate();
  
  if (date < minDate) date = new Date(minDate);
  if (date > maxDate) date = new Date(maxDate);
  
  tempDate.value = date;
  updatePickerValue(date);
};

// 更新选择器的值
const updatePickerValue = (date) => {
  if (!date) return;
  
  const pickerValue = [];
  
  if (showYearColumn.value) {
    const yearIndex = columns.value.years.findIndex(y => y === date.getFullYear());
    pickerValue.push(yearIndex > -1 ? yearIndex : 0);
  }
  
  if (showMonthColumn.value) {
    const month = date.getMonth() + 1;
    const monthStr = month < 10 ? `0${month}` : `${month}`;
    const monthIndex = columns.value.months.findIndex(m => parseInt(m) === month);
    pickerValue.push(monthIndex > -1 ? monthIndex : 0);
  }
  
  if (showDayColumn.value) {
    const day = date.getDate();
    const dayStr = day < 10 ? `0${day}` : `${day}`;
    const dayIndex = columns.value.days.findIndex(d => parseInt(d) === day);
    pickerValue.push(dayIndex > -1 ? dayIndex : 0);
  }
  
  if (showHourColumn.value) {
    const hour = date.getHours();
    const hourStr = hour < 10 ? `0${hour}` : `${hour}`;
    const hourIndex = columns.value.hours.findIndex(h => parseInt(h) === hour);
    pickerValue.push(hourIndex > -1 ? hourIndex : 0);
  }
  
  if (showMinuteColumn.value) {
    const minute = date.getMinutes();
    const minuteStr = minute < 10 ? `0${minute}` : `${minute}`;
    const minuteIndex = columns.value.minutes.findIndex(m => parseInt(m) === minute);
    pickerValue.push(minuteIndex > -1 ? minuteIndex : 0);
  }
  // console.error('更新选择器的值',pickerValue);
  currentPickerValue.value = pickerValue;
};

// 选择器变化事件
const onPickerChange = (e) => {
  const values = e.detail.value;
  if (!values) return;
  
  let index = 0;
  const date = new Date(tempDate.value || new Date());
  
  if (showYearColumn.value && index < values.length) {
    const yearIndex = values[index++];
    if (yearIndex >= 0 && yearIndex < columns.value.years.length) {
      date.setFullYear(columns.value.years[yearIndex]);
    }
  }
  
  if (showMonthColumn.value && index < values.length) {
    const monthIndex = values[index++];
    if (monthIndex >= 0 && monthIndex < columns.value.months.length) {
      date.setMonth(parseInt(columns.value.months[monthIndex]) - 1);
    }
  }
  
  if (showDayColumn.value && index < values.length) {
    const dayIndex = values[index++];
    if (dayIndex >= 0 && dayIndex < columns.value.days.length) {
      date.setDate(parseInt(columns.value.days[dayIndex]));
    }
  }
  
  if (showHourColumn.value && index < values.length) {
    const hourIndex = values[index++];
    if (hourIndex >= 0 && hourIndex < columns.value.hours.length) {
      date.setHours(parseInt(columns.value.hours[hourIndex]));
    }
  }
  
  if (showMinuteColumn.value && index < values.length) {
    const minuteIndex = values[index++];
    if (minuteIndex >= 0 && minuteIndex < columns.value.minutes.length) {
      date.setMinutes(parseInt(columns.value.minutes[minuteIndex]));
    } else {
      date.setMinutes(0);
    }
  }
  
  // 保存临时日期并更新选择器值
  tempDate.value = new Date(date);
  currentPickerValue.value = [...values];
};

// 确认选择
const confirmPicker = () => {
  if (!tempDate.value) {
    popup.value?.close();
    return;
  }
  
  // 更新当前日期
  currentDate.value = new Date(tempDate.value);
  
  // 格式化输出
  const formattedValue = formatOutputValue(currentDate.value);
  emit("update:modelValue", formattedValue);
  emit("change", formattedValue);
  emit("confirm", formattedValue);
  
  popup.value?.close();
};

// 取消选择
const cancelPicker = () => {
  emit("cancel");
  popup.value?.close();
};

// 格式化输出值
const formatOutputValue = (date) => {
  if (!date) return "";
  
  // 根据类型自动选择格式
  let formatStr = props.format;
  if (!formatStr) {
    switch(props.type) {
      case 'date':
        formatStr = 'YYYY-MM-DD';
        break;
      case 'year-month':
        formatStr = 'YYYY-MM';
        break;
      case 'month-day':
        formatStr = 'MM-DD';
        break;
      case 'time':
        formatStr = 'HH:mm';
        break;
      case 'datetime':
        formatStr = 'YYYY-MM-DD HH:mm';
        break;
      case 'datehour':
        formatStr = 'YYYY-MM-DD HH';
        break;
      default:
        formatStr = 'YYYY-MM-DD HH:mm';
    }
  }
  
  // 格式化日期
  return formatDate(date, formatStr);
};

// 格式化日期
const formatDate = (date, format) => {
  if (!date) return "";
  
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  
  const padZero = (num) => {
    return num < 10 ? `0${num}` : `${num}`;
  };
  
  return format
    .replace(/YYYY/g, year)
    .replace(/YY/g, String(year).slice(2))
    .replace(/MM/g, padZero(month))
    .replace(/M/g, month)
    .replace(/DD/g, padZero(day))
    .replace(/D/g, day)
    .replace(/HH/g, padZero(hour))
    .replace(/H/g, hour)
    .replace(/mm/g, padZero(minute))
    .replace(/m/g, minute)
    .replace(/ss/g, padZero(second))
    .replace(/s/g, second);
};

// 显示值
const displayValue = computed(() => {
  if (!currentDate.value) return "";
  return formatOutputValue(currentDate.value);
});

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 对于month-day格式，特殊处理
      if (props.type === 'month-day' && typeof newVal === 'string' && newVal.includes('-')) {
        const [month, day] = newVal.split('-').map(num => parseInt(num, 10));
        if (!isNaN(month) && !isNaN(day)) {
          const date = new Date();
          date.setMonth(month - 1);
          date.setDate(day);
          currentDate.value = date;
        }
      } else {
        // 尝试解析日期
        const date = new Date(newVal);
        
        // 检查日期是否有效
        if (!isNaN(date.getTime())) {
          currentDate.value = date;
        }
      }
    } else {
      // 当传入空值时，清除选中状态
      currentDate.value = null;
    }
  },
  { immediate: true }
);

// 组件挂载时初始化
onMounted(() => {
  // 如果有初始值，解析日期
  if (props.modelValue) {
    // 对于month-day格式，特殊处理
    if (props.type === 'month-day' && typeof props.modelValue === 'string' && props.modelValue.includes('-')) {
      const [month, day] = props.modelValue.split('-').map(num => parseInt(num, 10));
      if (!isNaN(month) && !isNaN(day)) {
        const date = new Date();
        date.setMonth(month - 1);
        date.setDate(day);
        currentDate.value = date;
      }
    } else {
      const date = new Date(props.modelValue);
      if (!isNaN(date.getTime())) {
        currentDate.value = date;
      }
    }
  }
});

// 暴露方法
defineExpose({
  open: showPicker,
  close: cancelPicker,
  clear: clearValue
});
</script>

<style lang="scss" scoped>
.datetime-picker {
  width: 100%;

  .picker-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 35px;
    padding: 0 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;

    .picker-value {
      flex: 1;
      font-size: 14px;
      color: #606266;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0 5px;
      
      &.placeholder {
        color: #999;
      }
    }

    .picker-icon, .clear-icon {
      flex-shrink: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .popup-content {
    background-color: #fff;
    // border-radius: 10px;
    // overflow: hidden;

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 45px;
      padding: 0 15px;
      border-bottom: 1px solid #f5f5f5;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .cancel-btn,
      .confirm-btn {
        font-size: 14px;
        padding: 5px;
      }

      .cancel-btn {
        color: #666;
      }

      .confirm-btn {
        color: #007aff;
      }
    }

    .picker-body {
      height: 220px;

      .picker-view {
        width: 100%;
        height: 100%;

        .picker-item {
          line-height: 34px;
          text-align: center;
          font-size: 16px;
          color: #333;
        }
      }
    }
  }
}
</style>
