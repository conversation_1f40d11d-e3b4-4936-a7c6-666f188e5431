# 通用文件下载函数使用说明

## 导入方式
```javascript
import { downloadFile } from "@/utils/fileUtils.js";
```

## 函数说明
`downloadFile` 是一个通用的文件下载函数，支持H5和APP-PLUS平台，能够处理所有文件格式的真正下载。

### 函数签名
```javascript
/**
 * 通用文件下载函数 - 支持所有文件格式的真正下载
 * @param {string} url - 文件下载地址（必需）
 * @param {string} fileName - 文件名（可选，如果不传则从URL中提取）
 * @returns {Promise} 下载结果
 */
downloadFile(url, fileName = null)
```

## 使用示例

### 1. 基础使用（只传URL）
```javascript
// 函数会自动从URL中提取文件名
await downloadFile('https://example.com/files/document.pdf');
```

### 2. 指定文件名
```javascript
// 手动指定下载后的文件名
await downloadFile(
  'https://example.com/files/123456', 
  '项目报告.pdf'
);
```

### 3. 在组件中使用
```javascript
// 在Vue组件的方法中使用
const handleDownload = async (fileItem) => {
  try {
    await downloadFile(fileItem.url, fileItem.name);
    console.log('下载成功');
  } catch (error) {
    console.error('下载失败:', error);
  }
};
```

### 4. 批量下载
```javascript
// 批量下载多个文件
const downloadMultipleFiles = async (fileList) => {
  for (const file of fileList) {
    try {
      await downloadFile(file.url, file.name);
      // 可以添加延迟避免同时下载过多文件
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error(`下载 ${file.name} 失败:`, error);
    }
  }
};
```

## 平台特性

### H5端特性
- 支持所有文件格式的强制下载
- 对容易被浏览器预览的文件（PDF、图片等）强制设置为下载模式
- 针对iOS Safari进行了优化
- 自动清理下载资源，避免内存泄漏

### APP端特性  
- 自动保存文件到本地
- Android设备会显示保存成功提示
- 下载完成后自动尝试打开文件
- 支持下载进度显示

## 错误处理
函数内部已经处理了常见错误情况：
- 网络请求失败
- 文件保存失败  
- 文件打开失败

调用时建议使用try-catch包裹：
```javascript
try {
  await downloadFile(url, fileName);
} catch (error) {
  // 处理下载失败的情况
  console.error('下载失败:', error);
}
```

## 注意事项
1. URL必须是有效的文件下载链接
2. 确保文件服务器支持跨域访问（H5端）
3. 大文件下载可能需要较长时间，建议添加加载提示
4. APP端需要相应的文件访问权限 