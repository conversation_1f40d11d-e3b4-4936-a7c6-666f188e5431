<template>
  <view class="message-mix-container">
    <!-- 图片内容区域 -->
    <view class="message-images-container" v-if="hasImages">
      <scroll-view class="message-images-row" scroll-x="true" show-scrollbar="true" enhanced="true">
        <view 
          v-for="(item, itemIndex) in parseMixContent(content).filter(i => i.content_type && isImageFile(i.content_type))" 
          :key="'img-'+itemIndex"
          class="message-image-item"
        >
          <image
            v-if="item.attachment?.downloadSignUrl || item.attachment?.url"
            style="width: 100px; height: 100px;"
            mode="aspectFill"
            :src="item.attachment?.downloadSignUrl || item.attachment?.url"
            @click="handleImagePreview(item.attachment?.downloadSignUrl || item.attachment?.url)"
          ></image>
          <image
            v-else
            style="width: 100px; height: 100px;"
            mode="aspectFill"
            src="/static/chatImg/image-deleted.svg"
          ></image>
        </view>
      </scroll-view>
    </view>
    
    <!-- 文件内容区域 -->
    <view class="message-files-container" v-if="hasFiles">
      <scroll-view class="message-files-row" scroll-x="true" show-scrollbar="true" enhanced="true">
        <view 
          v-for="(item, itemIndex) in parseMixContent(content).filter(i => i.content_type !== 'str' && (!isImageFile(i.content_type || '')))" 
          :key="'file-'+itemIndex"
          class="message-file-item"
          @click="item.attachment ? handleFilePreview(item.attachment) : null"
        >
          <view class="file-info" v-if="item.attachment">
            <image :src="getFileIconPath(item.attachment.name)" class="file-type-icon"></image>
            <view class="file-details">
              <view class="file-name">{{ item.attachment.name }}</view>
            </view>
          </view>
          <view class="file-info" v-else>
            <image src="/static/file/unknown.svg" class="file-type-icon"></image>
            <view class="file-details">
              <view class="file-name">原文件被删除</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 文本内容区域 -->
    <view class="message-text-container" v-if="hasText">
      <view class="message-text-row">
        <view 
          v-for="(item, itemIndex) in parseMixContent(content).filter(i => i.content_type === 'str')" 
          :key="'text-'+itemIndex"
          class="message-text"
        >
          {{ item.content }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { isImageFile, getFileIconPath } from '@/utils/fileUtils';
import { computed } from 'vue';

// 定义组件属性
const props = defineProps({
  content: {
    type: [String, Array, Object],
    required: true
  }
});

// 解析混合内容的方法
const parseMixContent = (content) => {
  if (!content) return [];
  
  try {
    // 如果content已经是数组，直接返回
    if (Array.isArray(content)) {
      return content;
    }
    
    // 尝试将字符串解析为JSON
    if (typeof content === 'string') {
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      // 如果解析结果不是数组，将其包装为数组
      return [{ content_type: 'str', content: parsed.toString() }];
    }
    
    // 如果content是对象，将其包装为数组
    if (typeof content === 'object' && content !== null) {
      return [content];
    }
  } catch (error) {
    console.error('解析mix内容失败:', error);
    // 解析失败时，将原始内容作为字符串返回
    return [{ content_type: 'str', content: String(content) }];
  }
  
  // 默认返回空数组
  return [];
};

// 计算属性：判断是否有图片内容
const hasImages = computed(() => {
  return parseMixContent(props.content).some(item => item.content_type && isImageFile(item.content_type));
});

// 计算属性：判断是否有文件内容
const hasFiles = computed(() => {
  return parseMixContent(props.content).some(item => 
    // 包含有效附件的文件
    (item.attachment && (!item.content_type || !isImageFile(item.content_type))) ||
    // 或者是非图片、非文本类型但附件为null的项（表示文件被删除）
    (item.attachment === null && item.content_type !== 'str' && !isImageFile(item.content_type || ''))
  );
});

// 计算属性：判断是否有文本内容
const hasText = computed(() => {
  return parseMixContent(props.content).some(item => item.content_type === 'str');
});

// 定义事件
const emit = defineEmits(['onPreviewImage', 'previewFile']);

/**
 * 处理图片预览
 * @param {string} url - 图片URL
 */
function handleImagePreview(url) {
  emit('onPreviewImage', url);
}

/**
 * 处理文件预览
 * @param {Object} file - 文件对象
 */
function handleFilePreview(file) {
  emit('previewFile', file);
}

</script>

<style lang="scss" scoped>
.message-mix-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end; // 修改为居右对齐
}


// 内容容器通用样式
.message-images-container,
.message-files-container,
.message-text-container {
  // width: 100%;
  max-width: 365px;
  display: flex;
  flex-direction: column;
  align-items: flex-end; // 修改为居右对齐
}

// 图片行样式
.message-images-container {
  .message-images-row {
    display: flex;
    flex-direction: row-reverse; // 从右向左排列
    flex-wrap: nowrap; // 防止换行，确保可以水平滚动
    white-space: nowrap; // 防止内容换行
    
    .message-image-item {
      display: inline-block; // 使用行内块元素确保横向排列
      width: 100px;
      height: 100px;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 10px; // 改为左侧间距
      min-width: 100px; // 确保最小宽度
      
      &:last-child {
        margin-right: 0; // 最后一个图片不需要左边距
      }
      
      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// 文件行样式
.message-files-container {
  .message-files-row {
    display: flex;
    flex-direction: row-reverse; // 从右向左排列
    flex-wrap: nowrap; // 防止换行，确保可以水平滚动
    white-space: nowrap; // 防止内容换行
    -webkit-overflow-scrolling: touch; // 增强iOS滚动体验

    .message-file-item {
      display: inline-block; // 使用行内块元素确保横向排列
      min-width: 100px; // 设置最小宽度
      max-width: 300px; // 设置最大宽度
      margin:5px;
      
      &:last-child {
        margin: 0; // 最后一个文件不需要左边距
      }
      
      .file-info {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px;
        border-radius: 10px;
        background-color: #FFFFFF;
        border: solid 1px #E2E4E9;;

        .file-type-icon {
          width: 32px;
          height: 32px;
          flex-shrink: 0;
        }
        
        .file-details {
          display: flex;
          flex-direction: column;
          flex: 1;
          overflow: hidden;
          align-items: flex-end; // 右对齐文件详情
          
          .file-name {
            font-size: 12px;
            font-weight: 400;
            color: #3E4551;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: right; // 文件名右对齐
            width: 100%;
          }
        }
      }
    }
  }
}

// 文本行样式
.message-text-container {
  .message-text-row {
    min-width: 60px; // 最小宽度
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    text-align: justify; // 对齐文本，避免空格
    .message-text {
      background-color: #A4E17B;
      padding: 10px;
      border-radius: 10px 0 10px 10px;
      width: auto; // 使用自适应宽度
      word-wrap: break-word; // 确保长单词换行
      word-break: break-word; // 允许在任何字符间换行
      white-space: pre-wrap; // 保留空格和换行符
      overflow-wrap: break-word; // 允许内容换行
      user-select: text; /* 允许文本选择 */
      -webkit-user-select: text; /* 针对 Safari */
      -moz-user-select: text; /* 针对 Firefox */
      -ms-user-select: text; /* 针对 IE 和 Edge */
    }
  }
}

/* 针对 PC 端的样式，最大宽度为 100% */
@media (min-width: 1024px) {
  .message-images-container,
  .message-files-container,
  .message-text-container {
    max-width: 700px;
  }
}
</style>