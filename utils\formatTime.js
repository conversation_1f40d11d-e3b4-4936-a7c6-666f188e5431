/**
 * 格式化上次联系时间
 * @param {string|number} timeString - 时间字符串或时间戳
 * @returns {string} - 格式化后的时间描述
 */
export function formatLastContactTime(timeString) {
  if (!timeString) {
    return "-";
  }
  
  try {
    // 获取今天的日期（去除时分秒）
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // 解析上次联系时间（支持多种格式）
    let lastContactDate;
    if (typeof timeString === 'number') { // 如果是时间戳
      lastContactDate = new Date(timeString);
    } else if (typeof timeString === 'string') { // 如果是字符串
      // 尝试替换 '-' 为 '/' 以兼容 iOS
      lastContactDate = new Date(timeString.replace(/-/g, '/'));
    } else {
      return "-"; // 不支持的类型
    }
    
    // 检查日期是否有效
    if (isNaN(lastContactDate.getTime())) {
      console.warn("无效的日期格式:", timeString);
      return "-";
    }
    
    // 获取上次联系的日期（去除时分秒）
    const contactDay = new Date(lastContactDate);
    contactDay.setHours(0, 0, 0, 0);
    
    // 计算时间差（毫秒）
    const diffTime = today.getTime() - contactDay.getTime();
    
    // 如果时间差为负数（未来时间），显示 '-'
    if (diffTime < 0) {
      return "-";
    }
    
    // 计算天数差
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    // 根据天数差返回不同的文本
    if (diffDays === 0) {
      return "今天联系";
    } else if (diffDays === 1) {
      return "昨天联系";
    } else if (diffDays > 1 && diffDays <= 7) {
      return `${diffDays}天前联系`;
    } else if (diffDays > 7 && diffDays <= 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks}周前联系`;
    } else if (diffDays > 30 && diffDays <= 365) {
      const months = Math.floor(diffDays / 30);
      return `${months}个月前联系`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years}年前联系`;
    }
  } catch (error) {
    console.error("格式化联系时间出错:", error, timeString);
    return "-";
  }
}

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date|string|number} date - 日期对象、时间字符串或时间戳
 * @returns {string} - 格式化后的日期字符串
 */
export function formatDate(date) {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'object' ? date : new Date(date);
    
    if (isNaN(dateObj.getTime())) {
      return '';
    }
    
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '';
  }
}

// filterDateTime 函数：接收一个日期时间字符串（dateTime）作为输入。
// 如果 dateTime 中的时分秒是 00:00:00，函数会返回 YYYY-MM-DD。
// 如果时分秒不是 00:00:00，函数会返回 YYYY-MM-DD HH:mm，即去掉秒部分。
export function filterDateTime(dateTime) {
  if (!dateTime) {
    return "-";
  }
  const date = new Date(dateTime);
  if (isNaN(date.getTime())) {
    return "-";
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  // 判断是否为 00:00:00，若是则只显示年月日，否则显示年月日时分
  if (date.getHours() === 0 && date.getMinutes() === 0 && date.getSeconds() === 0) {
    return `${year}-${month}-${day}`;
  }
  return `${year}/${month}/${day} ${hours}:${minutes}`;
}

/**
 * 获取时间字符串中的时分部分
 * @param {string} dateTime - 日期时间字符串
 * @returns {string} - 格式化后的时分字符串 (HH:mm)
 */
export function getTimeOnly(dateTime) {
  if (dateTime === null || dateTime === "") {
      return "-";
  }
  const date = new Date(dateTime);
  if (isNaN(date.getTime())) {
      return "-";
  }
  const hours = date.getHours();
  const minutes = date.getMinutes();
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
}

// 计算当前时间
export const getCurrentTime = () => {
  const now = new Date();
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
};

// 计算当前星期几
export const getCurrentDay = () => {
  const now = new Date();
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  return weekdays[now.getDay()];
};

/**
 * 获取通知周期的方法
 * @param {number} notifyCycle - 通知周期类型
 * @param {string} dueDate - 截止日期
 * @returns {string} - 格式化后的通知周期字符串
 */
export const getNotifyCycle = (notifyCycle, dueDate) => {
  // 通知循环周期映射表
  const cycleMap = {
    0: '无',
    1: '每天',
    2: '工作日',
    3: '周末',
    4: '每周',
    5: '每两周',
    6: '每月',
    7: '每三个月',
    8: '每六个月',
    9: '每年'
  };
  
  // 如果没有截止日期，只返回通知周期文本
  if (!dueDate) {
    return cycleMap[notifyCycle] || '-';
  }
  
  try {
    const date = new Date(dueDate);
    if (isNaN(date.getTime())) {
      return cycleMap[notifyCycle] || '-';
    }
    
    const timeOnly = getTimeOnly(dueDate);
    
    // 1 每天，2 工作日，3 周末，只显示+时分
    if ([1, 2, 3].includes(notifyCycle)) {
      return `${cycleMap[notifyCycle]} ${timeOnly}`;
    }
    
    // 4 每周，5 每两周，周后面+'日', '一', '二', '三', '四', '五', '六' + 时分
    if ([4, 5].includes(notifyCycle)) {
      const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekday = weekdays[date.getDay()];
      return `${cycleMap[notifyCycle]}${weekday} ${timeOnly}`;
    }
    
    // 6 每月，7 每三个月，8 每六个月 + 几号 + 时分
    if ([6, 7, 8].includes(notifyCycle)) {
      const day = date.getDate();
      return `${cycleMap[notifyCycle]}${day}号 ${timeOnly}`;
    }
    
    // 9 每年 + 几月几号 + 时分
    if (notifyCycle === 9) {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${cycleMap[notifyCycle]}${month}月${day}号 ${timeOnly}`;
    }
    
    // 0 永不 或其他情况
    return cycleMap[notifyCycle] || '-';
    
  } catch (error) {
    console.error('获取通知周期出错:', error);
    return cycleMap[notifyCycle] || '-';
  }
};

// 获取星期几的方法
export const getWeekday = (dateTime) => {
  const date = new Date(dateTime);
  const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
  return `每周${weekdays[date.getDay()]}`;
};


/**
 * 计算逾期时间
 * @param {string} dueDate - 截止日期字符串，格式如 "2025-06-11 17:32:03"
 * @returns {string} - 逾期时间描述，如 "逾期3天" 或空字符串（未逾期）
 */
export function getOverdueText(dueDate) {
  if (!dueDate) {
    return '';
  }
  
  try {
    // 解析截止日期
    const due = new Date(dueDate.replace(/-/g, '/'));
    
    // 检查日期是否有效
    if (isNaN(due.getTime())) {
      return '';
    }
    
    // 获取当前时间
    const now = new Date();
    
    // 计算时间差（毫秒）
    const diffTime = now.getTime() - due.getTime();
    
    // 如果时间差小于等于0，说明未逾期
    if (diffTime <= 0) {
      return '';
    }
    // 计算逾期天数
    const overdueDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    // 如果逾期天数为0，只显示"逾期"，否则显示"逾期X天"
    if (overdueDays === 0) {
      return '逾期';
    }
    
    return `逾期${overdueDays}天`;
  } catch (error) {
    console.error('计算逾期时间出错:', error);
    return '';
  }
}
  
