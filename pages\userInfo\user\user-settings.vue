<template>
  <!-- 个人信息设置 -->
  <view class="brws-customer-details" :style="appSafeAreaStyle">
    <uni-nav-bar
      left-icon="left"
      title="个人信息设置"
      title-text-align="center"
      backgroundColor="#fff"
      color="#000"
      height="60px"
      @clickLeft="handerLeftBack"
    />
    <view class="page-container">
      <uni-forms ref="formRef" :model="formData">
        <view class="info-card">
          <!-- 头像 -->
          <view class="info-item" @click="selectAvatarForUpload">
            <text class="item-label">头像</text>
            <view class="item-content">
              <image :src="userPortrait" class="avatar-container" mode="aspectFill"></image>
            </view>
            <uni-icons type="right" size="16"></uni-icons>
          </view>
          <!-- 姓名 -->
          <view class="info-item">
            <text class="item-label">姓名</text>
            <uni-easyinput
              v-model="formData.name"
              placeholder="请输入姓名"
              maxlength="10"
              @input="handleFieldChange('name')"
            />
          </view>
          <!-- 手机号 -->
          <view class="info-item">
            <text class="item-label">手机号</text>
            <uni-easyinput
              v-model="formData.mobile"
              placeholder="请输入手机号"
              maxlength="11"
              @input="handleFieldChange('mobile')"
            />
          </view>
          <!-- 公司 -->
          <view class="info-item">
            <text class="item-label">我的公司</text>
            <uni-easyinput
              v-model="formData.company"
              placeholder="请输入公司名称"
              maxlength="500"
              @input="handleFieldChange('company')"
            />
          </view>
        </view>
        <!-- 用户画像部分 -->
        <view class="section-box">
          <image style="width: 25px; height: 25px" src="@/static/tabBar/down.png"></image>
          <view class="section-title">用户画像</view>
          <uni-icons type="refreshempty" color="#4D5BDE" size="16" @click="refreshPortrait"></uni-icons>
        </view>
        <uni-easyinput
          class="user-image-input"
          type="textarea"
          v-model="formData.desc"
          :styles="{ backgroundColor: '#f8f9fa' }"
          placeholder="请输入用户画像"
          maxLength="1000"
          @input="handleFieldChange('desc')"
        />
      </uni-forms>
    </view>
    <!-- xe-upload 组件（隐藏，用于文件选择） -->
    <xe-upload 
      ref="xeUpload"
      @callback="handleXeUploadCallback"
    />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted,computed } from "vue";
import avatarImage from "@/static/user/avatar.png";
import { getUserInfo, saveUserInfo } from '@/http/user';
import XeUpload from "@/uni_modules/xe-upload/components/xe-upload/xe-upload.vue";
import { useFileUpload } from '@/utils/useFileUpload.js'; // 导入文件上传hooks
// 使用文件上传hooks
const {
    chooseAndUpload,
    setXeUploadRef, 
    handleXeUploadCallback 
  } = useFileUpload();

// xe-upload 组件引用
const xeUpload = ref(null);
// 组件引用
const formRef = ref(null);
// 头像预览
const userPortrait = ref(null);
// 表单数据
const formData = reactive({
  id: '',
  name: '',
  mobile: '',
  third_id: '',
  company: '',
  desc:'',
});

// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`,
  };
  // #endif
  return {};
});

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getUserInfo();
    if (res.code === 0 && res.data) {
      // 合并数据到 formData
      // Object.assign(formData, res.data);
      formData.id = res.data.id || '';
      formData.name = res.data.name || '';
      formData.mobile = res.data.mobile || '';
      formData.third_id = res.data.third_id || '';
      formData.company = res.data.company || '';
      formData.desc = res.data.desc || '';
      // 设置头像
      userPortrait.value = res.data.portrait || avatarImage;
      console.log('获取用户信息',res.data);
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  }
};

// 处理字段变化创建防抖函数
const debounce = (fn, delay = 1500) => {
  let timer = null;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// 处理头像上传成功并调用后端保存接口
const handleAvatarUploadSuccess = async (portrait) => {
  const res = await saveUserInfo({
    portrait: portrait,
  });
};

// 原始的保存方法
const saveField = async (field) => {
  try {
    const res = await saveUserInfo({
      ...formData,
      [field]: formData[field],
    });
    if (res.code === 0) {
      // uni.showToast({
      //   title: '保存成功',
      //   icon: 'success'
      // });
    }
  } catch (error) {
    console.error('保存失败:', error);
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
};
// 使用防抖包装的处理字段变化方法 处理字段变化方法
const handleFieldChange = (field, options = { useDebounce: true, delay: 800 }) => {
  if (options.useDebounce) {
    // 使用防抖
    const debouncedSave = debounce(saveField, options.delay);
    debouncedSave(field);
  } else {
    // 直接执行
    saveField(field);
  }
};

// 处理头像上传选择 - 使用新的hooks
const selectAvatarForUpload = async () => {
  console.log('🚀 开始选择文件上传...')
  try {
    // 使用新的 hooks 选择并上传文件
    const results = await chooseAndUpload('image', {
      count: 1, // 最多选择9个文件
      extension: [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // 图片
      ],
    });
    if (results && results.length > 0) {
      console.error('✅ 文件上传成功:', results);
      // 更新头像预览
      userPortrait.value = results[0].fullUrl;
      console.log('头像预览已更新');
      await handleAvatarUploadSuccess(results[0].url);
      console.log('头像保存到后端成功');
      // 刷新用户信息
      setTimeout(async () => {
        console.log('开始刷新用户信息...');
        await fetchUserInfo();
      }, 200);
      uni.showToast({
        title: "头像上传成功",
        icon: "success"
      });
    }
  } catch (error) {
    console.error('💥 文件上传过程出错:', error);
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none',
      duration: 3000
    });
  }
};

// 返回上一页
const handerLeftBack = () => {
  uni.switchTab({
    url: '/pages/tabBar/more/more',
  });
};

// 刷新用户画像
const refreshPortrait = async () => {
  await fetchUserInfo();
  uni.showToast({
    title: '刷新成功',
    icon: 'success'
  });
};

// 页面加载时获取用户信息
onMounted(() => {
  fetchUserInfo();
  if (xeUpload.value) {
    setXeUploadRef(xeUpload.value);
  }
});
</script>

<style lang="scss" scoped>
:deep(.uni-nav-bar-text) {
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.brws-customer-details {
  max-width: 750px;
  margin: 0 auto;
  .page-container {
    margin: 20px 18px;
    .info-card {
      padding: 14px;
      border-radius: 10px;
      border: 1px solid var(---normal, #e2e4e9);
      background: #fff;

      .info-item {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #e2e4e9;
        &:last-child {
          border-bottom: none;
        }

        .item-label {
          color: #606266;
          font-size: 14px;
          width: 80px;
        }
        .item-content {
          flex: 1;
          color: #303133;
          font-size: 16px;
        }
      }
      .avatar-container {
        width: 50px;
        height: 50px;
        border-radius: 10px;
      }
    }
    .section-box {
      display: flex;
      align-items: center;
      margin: 20px 0 8px 0;
      .section-title {
        color: var(---, #787d86);
        font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC",
          "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei",
          sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        flex: 1;
      }
    }
    .user-image-input {
      color: var(---, #787d86);
    }
  }
}
</style>