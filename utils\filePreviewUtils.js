/**
 * 文件预览工具模块
 * 提供跨平台的文件预览功能
 */

/**
 * 通用文件预览函数
 * @param {Object} attachment - 文件对象
 * @param {Object} options - 预览选项
 * @param {Function} options.onWebOfficePreview - H5端WebOffice预览回调
 * @param {Function} options.onAppPreview - APP端预览回调
 * @param {Function} options.onMiniProgramPreview - 小程序端预览回调
 * @returns {Promise} 预览结果
 */
export const previewFile = async (attachment, options = {}) => {
  try {
    console.log('🔍 开始预览文件:', attachment);
    
    // 检查文件是否存在预览链接
    if (!attachment || (!attachment.url && !attachment.tempUrl && !attachment.downloadSignUrl)) {
      uni.showToast({
        title: '无法预览，文件链接不存在',
        icon: 'none'
      });
      return { success: false, message: '文件链接不存在' };
    }
    
    // 获取文件扩展名
    const fileExtension = attachment.name ? ('.' + attachment.name.split('.').pop().toLowerCase()) : '';
    
    // 智能选择预览URL：优先使用下载签名URL，其次tempUrl，最后url
    const previewUrl = getPreviewUrl(attachment);
    console.log('使用预览URL:', previewUrl);
    
    // 图片文件直接使用uni.previewImage预览
    if (isImageFile(fileExtension)) {
      return await previewImage(previewUrl);
    }
    
    // 根据平台选择文档预览方式
    // #ifdef H5
    return await previewDocumentH5(attachment, previewUrl, options);
    // #endif
    
    // #ifdef APP-PLUS
    return await previewDocumentApp(attachment, previewUrl, options);
    // #endif
    
    // #ifdef MP-WEIXIN
    return await previewDocumentMiniProgram(attachment, previewUrl, options);
    // #endif
    
    // 其他环境降级处理
    return await previewDocumentFallback(fileExtension, previewUrl);
    
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '文件预览失败',
      icon: 'none'
    });
    return { success: false, message: error.message };
  }
};

/**
 * 获取预览URL
 * @param {Object} attachment - 文件对象
 * @returns {string} 预览URL
 */
const getPreviewUrl = (attachment) => {
  if (attachment.downloadSignUrl) {
    console.log('使用下载签名URL预览');
    return attachment.downloadSignUrl;
  } else if (attachment.tempUrl) {
    console.log('使用tempUrl预览');
    return attachment.tempUrl;
  } else {
    console.log('使用服务器URL预览');
    return attachment.url;
  }
};

/**
 * 判断是否为图片文件
 * @param {string} fileExtension - 文件扩展名
 * @returns {boolean} 是否为图片
 */
const isImageFile = (fileExtension) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
  return imageExtensions.includes(fileExtension);
};

/**
 * 预览图片文件
 * @param {string} previewUrl - 预览URL
 * @returns {Promise} 预览结果
 */
const previewImage = async (previewUrl) => {
  return new Promise((resolve) => {
    console.log('预览图片文件:', previewUrl);
    uni.previewImage({
      urls: [previewUrl],
      current: previewUrl,
      indicator: 'number',
      loop: false,
      success: () => {
        resolve({ success: true, message: '图片预览成功' });
      },
      fail: (err) => {
        console.error('图片预览失败:', err);
        uni.showToast({
          title: '图片预览失败',
          icon: 'none'
        });
        resolve({ success: false, message: '图片预览失败' });
      }
    });
  });
};

/**
 * H5端文档预览
 * @param {Object} attachment - 文件对象
 * @param {string} previewUrl - 预览URL
 * @param {Object} options - 预览选项
 * @returns {Promise} 预览结果
 */
const previewDocumentH5 = async (attachment, previewUrl, options) => {
  try {
    const { isSupportedByWebOffice } = await import('@/services/aliyunWebOfficeService.js');
    
    if (isSupportedByWebOffice(attachment.name)) {
      console.log('H5环境：使用阿里云WebOffice预览:', attachment.name);
      
      // 调用WebOffice预览回调
      if (options.onWebOfficePreview && typeof options.onWebOfficePreview === 'function') {
        options.onWebOfficePreview(attachment);
        return { success: true, message: 'WebOffice预览已启动' };
      }
      
      // 如果没有提供回调，显示提示
      uni.showToast({
        title: '正在准备文档预览...',
        icon: 'loading',
        duration: 2000
      });
      return { success: true, message: '正在准备文档预览' };
    } else {
      // 不支持WebOffice的格式，降级到浏览器打开
      console.log('不支持WebOffice预览，使用浏览器打开:', attachment.name);
      window.open(previewUrl, '_blank');
      return { success: true, message: '已在浏览器中打开' };
    }
  } catch (error) {
    console.warn('WebOffice服务加载失败，使用浏览器打开:', error);
    window.open(previewUrl, '_blank');
    return { success: true, message: '已在浏览器中打开' };
  }
};

/**
 * APP端文档预览
 * @param {Object} attachment - 文件对象
 * @param {string} previewUrl - 预览URL
 * @param {Object} options - 预览选项
 * @returns {Promise} 预览结果
 */
const previewDocumentApp = async (attachment, previewUrl, options) => {
  return new Promise((resolve) => {
    console.log('APP环境：使用原生文档预览:', attachment.name);
    
    // 如果提供了自定义APP预览回调
    if (options.onAppPreview && typeof options.onAppPreview === 'function') {
      options.onAppPreview(attachment);
      resolve({ success: true, message: 'APP预览已启动' });
      return;
    }
    
    // 使用默认APP预览逻辑
    uni.showLoading({
      title: '正在下载文件...'
    });
    
    uni.downloadFile({
      url: previewUrl,
      success: (downloadRes) => {
        if (downloadRes.statusCode === 200) {
          const tempFilePath = downloadRes.tempFilePath;
          
          // 使用plus.runtime.openFile打开文件
          plus.runtime.openFile(tempFilePath, {
            popover: {
              x: '50%',
              y: '50%'
            }
          }, (error) => {
            console.error('APP文件预览失败:', error);
            uni.showToast({
              title: '文件预览失败',
              icon: 'none'
            });
            resolve({ success: false, message: 'APP文件预览失败' });
          });
          
          console.log('✅ APP端文件预览成功:', attachment.name);
          resolve({ success: true, message: 'APP文件预览成功' });
        } else {
          uni.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
          resolve({ success: false, message: '文件下载失败' });
        }
      },
      fail: (error) => {
        console.error('APP文件下载失败:', error);
        uni.showToast({
          title: '文件下载失败',
          icon: 'none'
        });
        resolve({ success: false, message: 'APP文件下载失败' });
      },
      complete: () => {
        uni.hideLoading();
      }
    });
  });
};

/**
 * 小程序端文档预览
 * @param {Object} attachment - 文件对象
 * @param {string} previewUrl - 预览URL
 * @param {Object} options - 预览选项
 * @returns {Promise} 预览结果
 */
const previewDocumentMiniProgram = async (attachment, previewUrl, options) => {
  return new Promise((resolve) => {
    // 如果提供了自定义小程序预览回调
    if (options.onMiniProgramPreview && typeof options.onMiniProgramPreview === 'function') {
      options.onMiniProgramPreview(attachment);
      resolve({ success: true, message: '小程序预览已启动' });
      return;
    }
    
    // 获取文件扩展名
    const fileExtension = attachment.name ? ('.' + attachment.name.split('.').pop().toLowerCase()) : '';
    
    // 检查是否支持的文件格式
    const supportedFormats = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf'];
    
    if (!supportedFormats.includes(fileExtension)) {
      uni.showToast({
        title: '小程序不支持此文件格式预览',
        icon: 'none'
      });
      resolve({ success: false, message: '小程序不支持此文件格式预览' });
      return;
    }
    
    // 先下载文件到本地
    uni.showLoading({
      title: '正在下载文件...'
    });
    
    uni.downloadFile({
      url: previewUrl,
      success: (downloadRes) => {
        if (downloadRes.statusCode === 200) {
          // 使用wx.openDocument打开文档
          wx.openDocument({
            filePath: downloadRes.tempFilePath,
            fileType: fileExtension.substring(1), // 去掉点号
            success: () => {
              console.log('✅ 小程序文档预览成功:', attachment.name);
              resolve({ success: true, message: '小程序文档预览成功' });
            },
            fail: (error) => {
              console.error('小程序文档预览失败:', error);
              uni.showToast({
                title: '文档预览失败',
                icon: 'none'
              });
              resolve({ success: false, message: '小程序文档预览失败' });
            }
          });
        } else {
          uni.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
          resolve({ success: false, message: '文件下载失败' });
        }
      },
      fail: (error) => {
        console.error('小程序文件下载失败:', error);
        uni.showToast({
          title: '文件下载失败',
          icon: 'none'
        });
        resolve({ success: false, message: '小程序文件下载失败' });
      },
      complete: () => {
        uni.hideLoading();
      }
    });
  });
};

/**
 * 降级处理：其他环境的文档预览
 * @param {string} fileExtension - 文件扩展名
 * @param {string} previewUrl - 预览URL
 * @returns {Promise} 预览结果
 */
const previewDocumentFallback = async (fileExtension, previewUrl) => {
  return new Promise((resolve) => {
    uni.showModal({
      title: '文件预览',
      content: `当前环境暂不支持${fileExtension}格式的在线预览，是否尝试下载此文件？`,
      confirmText: '下载',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 尝试下载文件
          uni.showToast({
            title: '请在浏览器中下载文件',
            icon: 'none'
          });
          resolve({ success: true, message: '用户选择下载文件' });
        } else {
          resolve({ success: false, message: '用户取消预览' });
        }
      }
    });
  });
};

/**
 * 快捷预览函数 - 适用于大多数场景
 * @param {Object} attachment - 文件对象
 * @param {Function} onWebOfficePreview - WebOffice预览回调（可选）
 * @returns {Promise} 预览结果
 */
export const quickPreviewFile = async (attachment, onWebOfficePreview = null) => {
  return await previewFile(attachment, {
    onWebOfficePreview: onWebOfficePreview
  });
};

/**
 * 导出默认预览函数（向后兼容）
 */
export default previewFile;
