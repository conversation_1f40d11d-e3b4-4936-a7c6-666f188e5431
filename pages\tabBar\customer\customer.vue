<template>
  <!-- 客户列表页面 -->
  <z-paging
    ref="pagingRef"
    v-model="dataList"
    @query="queryList"
    :refresherEnabled="false"
    :defaultPageSize="50"
    :showLoadingMoreNoMoreView="false"
    :style="appSafeAreaStyle"
    :scrollable="!isDragging"
  >
    <template #top>
      <view class="customer-container">
        <view class="customer-header">
          <view class="item-header" @click="handleNewBuildClick">
            <uni-icons type="plusempty" size="20"></uni-icons>
            <text>新建</text>
          </view>
        </view>
        <view class="section-box">
          <image
            style="width: 25px; height: 25px"
            src="@/static/tabBar/down.png"
          ></image>
          <view class="section-title">默认排序</view>
        </view>
      </view>
    </template>
    <!-- 可拖拽列表容器 -->
    <view class="sortable-list">
      <view
        class="sortable-item"
        v-for="(item, index) in dataList"
        :key="`customer-${item.customer_id || index}`"
        :class="{ 
          dragging: dragIndex === index,
          'drag-feedback': dragState.dragFeedbackIndex === index,
          'out-of-bounds': dragState.isOutOfBounds && dragIndex === index
        }"
        :style="{
          'touch-action': isDragging && dragIndex === index ? 'none' : 'pan-y',
          '-webkit-touch-callout': 'none',
          '-webkit-user-select': 'none',
          'user-select': 'none'
        }"
        @touchstart="handleTouchStart($event, item, index)"
        @touchmove="handleTouchMove($event, item, index)"
        @touchend="handleTouchEnd"
        @touchcancel="handleTouchEnd"
      >
        <!-- <uni-swipe-action ref="swipeRef">
          <uni-swipe-action-item
            :disabled="true"
            @click="handleItemClick($event, item, index)"
          >
          <view class="customer-item">
              <image
                class="customer-img"
                src="/static/user/users.png"
              ></image>
              <view class="content-wrapper" @click.stop="handeToEdit(item, index)">
                <view class="customer-title">{{
                  item.company_short_name
                }}</view>
                <view class="title-row">
                  <view>{{ item.contact_name || "-" }}</view>
                  <view>{{ formatLastContactTime(item.last_todo_time) }}</view>
                </view>
              </view>
            </view>
          </uni-swipe-action-item>
        </uni-swipe-action> -->
        <view class="customer-item">
          <image class="customer-img" src="/static/user/users.png"></image>
          <view class="content-wrapper" @click.stop="handeToEdit(item, index)">
            <view class="customer-title">{{ item.company_short_name }}</view>
            <view class="title-row">
              <view>{{ item.contact_name || "-" }}</view>
              <view>{{ formatLastContactTime(item.last_todo_time) }}</view>
            </view>
          </view>
        </view>        
      </view>
    </view>
    <!-- 添加底部占位空间 -->
    <view class="bottom-space"></view>
  </z-paging>
  <!-- 新建客户表单弹窗 -->
  <CustomerFormActionSheet
    v-if="showCustomerForm"
    v-model:show="showCustomerForm"
    @submit="handleCustomerSubmit"
  />
  <!-- 客服入口组件 -->
  <CustomerService />
</template>

<script setup>
import { onTabItemTap, onShow } from "@dcloudio/uni-app";
import { ref, computed, reactive, onMounted, nextTick, onUnmounted } from "vue";
import { eventBus } from "@/utils/eventBus.js";
import CustomerService from "@/components/CustomerService.vue";
import CustomerFormActionSheet from "./CustomerFormActionSheet.vue";
import {
  getCustomerList,
  addCustomer,
  deleteCustomer,
  updateCustomer,
} from "@/http/customer.js";
// 导入公共格式化方法
import { formatLastContactTime } from "@/utils/formatTime.js";

// 使用 ref 来定义响应式数据
const dataList = ref([]);
// 引用 z-paging 组件
const pagingRef = ref(null);
// 引用 swipe-action 组件
const swipeRef = ref(null);
const showCustomerForm = ref(false);
// 拖拽相关状态
const isDragging = ref(false);
const dragIndex = ref(-1);
const longPressTimer = ref(null);
// 添加一个防止点击的标记
const preventClick = ref(false);
// 添加节流相关状态
const throttleTimer = ref(null);
const lastMoveTime = ref(0);
const dragState = reactive({
  startY: 0,
  currentY: 0,
  startTime: 0,
  itemHeight: 0,
  positions: [],
  scrollTop: 0,
  autoScrollSpeed: 0,
  autoScrollTimer: null,
  dragFeedbackIndex: -1, // 拖拽反馈的索引
  touchStartX: 0, // 记录触摸开始的X坐标，用于判断是否是左右滑动
  touchStartY: 0, // 记录触摸开始的Y坐标
  isHorizontalMove: false, // 是否是水平方向移动（左右滑动）
  isOutOfBounds: false, // 是否超出拖拽范围
  // 缓存边界信息，减少重复计算
  cachedBounds: null,
  boundsUpdateTime: 0,
  // 添加原始索引记录
  originalIndex: -1, // 拖拽开始时的原始索引
  originalItem: null, // 拖拽开始时的原始元素
});

// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`,
  };
  // #endif
  return {};
});
// 添加获取商机列表边界的函数，增加缓存机制优化性能
const getBusinessListBounds = () => {
  return new Promise((resolve) => {
    const now = Date.now();
    // 如果缓存存在且未过期（500ms内），直接返回缓存
    if (dragState.cachedBounds && (now - dragState.boundsUpdateTime) < 500) {
      resolve(dragState.cachedBounds);
      return;
    }
    
    // #ifdef H5 || APP-PLUS
    try {
      const query = uni.createSelectorQuery();
      const systemInfo = uni.getSystemInfoSync();
      query.select('.sortable-list').boundingClientRect((rect) => {
        if (rect) {
          // 获取z-paging滚动容器信息
          const pagingQuery = uni.createSelectorQuery();
          pagingQuery.select('.z-paging-content').boundingClientRect((pagingRect) => {
            const bounds = {
              top: Math.max(rect.top, 0),
              bottom: Math.min(rect.bottom, systemInfo.windowHeight),
              left: rect.left,
              right: rect.right,
              // 添加滚动容器的信息
              scrollContainer: pagingRect,
              scrollTop: pagingRect ? pagingRect.scrollTop || 0 : 0,
              scrollHeight: pagingRect ? pagingRect.height || 0 : 0,
              clientHeight: pagingRect ? pagingRect.height || 0 : 0
            };
            // 缓存边界信息
            dragState.cachedBounds = bounds;
            dragState.boundsUpdateTime = now;
            resolve(bounds);
          }).exec();
        } else {
          resolve(null);
        }
      }).exec();
    } catch (error) {
      console.error("获取商机列表边界失败:", error);
      resolve(null);
    }
    // #endif
    
    // #ifndef H5 || APP-PLUS
    resolve(null);
    // #endif
  });
};
// 初始化拖拽排序
const initDragSort = () => {
  // 获取列表项的高度和位置信息
  const query = uni.createSelectorQuery();
  query
    .selectAll(".sortable-item")
    .boundingClientRect((rects) => {
      if (!rects || rects.length === 0) return;
      // 计算平均项目高度
      let totalHeight = 0;
      rects.forEach((rect) => {
        totalHeight += rect.height;
      });
      dragState.itemHeight = totalHeight / rects.length;
      // 存储每个元素的位置信息
      dragState.positions = rects.map((rect, index) => ({
        index,
        top: rect.top,
        height: rect.height,
        bottom: rect.top + rect.height,
      }));
    })
    .exec();
};

// 触摸开始处理
const handleTouchStart = (e, item, index) => {
  // APP环境下安全处理事件对象
  if (!e || !e.touches || !e.touches[0]) {
    console.warn('触摸事件对象无效');
    return;
  }
  // 记录触摸开始位置
  dragState.touchStartX = e.touches[0].clientX;
  dragState.touchStartY = e.touches[0].clientY;
  dragState.isHorizontalMove = false;
  // 记录原始索引和元素
  dragState.originalIndex = index;
  dragState.originalItem = { ...item }; // 深拷贝原始元素
  // 清除之前的长按定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
  }
  // 记录开始位置和时间
  dragState.startY = e.touches[0].clientY;
  dragState.startTime = Date.now();
  // 设置长按定时器 - APP端减少延迟
  const longPressDelay = uni.getSystemInfoSync().platform === 'ios' || uni.getSystemInfoSync().platform === 'android' ? 500 : 800;
  longPressTimer.value = setTimeout(() => {
    // 如果是水平方向移动，不触发拖拽
    if (dragState.isHorizontalMove) return;
    // 长按触发拖拽
    startDrag(e, item, index);
  }, longPressDelay);
};

// 开始拖拽
const startDrag = (e, item, index) => {
  // APP环境下安全处理事件对象
  if (!e || !e.touches || !e.touches[0]) {
    console.warn('开始拖拽时事件对象无效');
    return;
  }
  // 标记为拖拽状态
  isDragging.value = true;
  dragIndex.value = index;
  // 记录当前位置
  dragState.currentY = e.touches[0].clientY;
  // 阻止默认行为和冒泡
  try {
    e.preventDefault && e.preventDefault();
    e.stopPropagation && e.stopPropagation();
  } catch (error) {
    console.warn('阻止默认行为失败:', error);
  }
  // 禁用z-paging的滚动
  if (pagingRef.value) {
    pagingRef.value.setScrollEnable && pagingRef.value.setScrollEnable(false);
  }
  // 强制更新位置信息
  updatePositions(true);
  // 立即获取边界信息并缓存
  getBusinessListBounds();
  // 创建克隆元素（视觉反馈）
  createDragFeedback(index);
};

// 创建拖拽视觉反馈 - 使用uni-app原生方式
const createDragFeedback = (index) => {
  // 不再依赖document，直接通过CSS类来实现视觉反馈
  // 为拖拽项添加特殊样式
  dragState.dragFeedbackIndex = index;
  
  // 触发震动反馈
  try {
    uni.vibrateShort({
      success: function () {
        console.log("震动反馈成功");
      },
      fail: function() {
        console.log("震动反馈失败或不支持");
      }
    });
  } catch (error) {
    console.log("震动API调用失败:", error);
  }
};

// 更新位置信息 - 添加节流优化
const updatePositions = (force = false) => {
  const now = Date.now();
  // APP端优化节流机制
  const throttleDelay = uni.getSystemInfoSync().platform === 'ios' || uni.getSystemInfoSync().platform === 'android' ? 30 : 50;
  if (!force && (now - lastMoveTime.value) < throttleDelay) {
    return;
  }
  
  lastMoveTime.value = now;
  const query = uni.createSelectorQuery();
  query
    .selectAll(".sortable-item")
    .boundingClientRect((rects) => {
      if (!rects || rects.length === 0) return;
      dragState.positions = rects.map((rect, index) => ({
        index,
        top: rect.top,
        height: rect.height,
        bottom: rect.top + rect.height,
      }));
    })
    .exec();
};

// 触摸移动处理，添加节流优化和减少异步操作
const handleTouchMove = (e, item, index) => {
  // APP环境下安全处理事件对象
  if (!e || !e.touches || !e.touches[0]) {
    console.warn('触摸移动事件对象无效');
    return;
  }
  
  const now = Date.now();
  // APP端使用更高的帧率 - 约120fps
  const frameDelay = uni.getSystemInfoSync().platform === 'ios' || uni.getSystemInfoSync().platform === 'android' ? 8 : 16;
  if (throttleTimer.value) {
    return;
  }
  
  throttleTimer.value = setTimeout(() => {
    throttleTimer.value = null;
  }, frameDelay);
  
  // 计算移动距离
  const moveX = Math.abs(e.touches[0].clientX - dragState.touchStartX);
  const moveY = Math.abs(e.touches[0].clientY - dragState.touchStartY);
  // 判断是否是水平方向移动（左右滑动）
  if (
    !isDragging.value &&
    !dragState.isHorizontalMove &&
    moveX > moveY &&
    moveX > 10
  ) {
    dragState.isHorizontalMove = true;
    // 如果是水平方向移动，取消长按
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
    return;
  }
  // 如果不是拖拽状态，检查是否应该取消长按
  if (!isDragging.value) {
    // 如果移动距离超过阈值，取消长按
    if (moveY > 10) {
      if (longPressTimer.value) {
        clearTimeout(longPressTimer.value);
        longPressTimer.value = null;
      }
    }
    return;
  }

  // 如果已经进入拖拽状态，阻止默认行为，防止页面滚动
  try {
    e.preventDefault && e.preventDefault();
    e.stopPropagation && e.stopPropagation();
    // 额外阻止触摸滚动
    if (e.cancelable) {
      e.preventDefault();
    }
  } catch (error) {
    console.warn('阻止滚动行为失败:', error);
  }
  // 更新当前位置
  dragState.currentY = e.touches[0].clientY;

  // 使用缓存的边界信息进行快速检测
  if (dragState.cachedBounds) {
    // 计算扩展的边界，给予一定的容差
    const extendedTop = dragState.cachedBounds.top - 50;
    const extendedBottom = dragState.cachedBounds.bottom + 50;
    // 判断是否超出扩展边界
    if (dragState.currentY < extendedTop || dragState.currentY > extendedBottom) {
      dragState.isOutOfBounds = true;
      return; // 超出范围，直接返回
    } else {
      dragState.isOutOfBounds = false;
    }
  }

  // 计算目标索引
  const targetIndex = findTargetIndex(dragState.currentY);
  // 如果目标索引有效且不等于当前拖拽索引，交换位置
  if (targetIndex !== -1 && targetIndex !== dragIndex.value) {
    // 交换数据
    const temp = dataList.value[dragIndex.value];
    dataList.value.splice(dragIndex.value, 1);
    dataList.value.splice(targetIndex, 0, temp);
    // 更新拖拽索引
    dragIndex.value = targetIndex;
    // 延迟更新位置信息，避免频繁DOM查询
    updatePositions();
  }
  // 处理自动滚动
  handleAutoScroll(e);
};

// 查找目标索引
const findTargetIndex = (y) => {
  // 如果没有位置信息，返回-1
  if (!dragState.positions || dragState.positions.length === 0) return -1;
  // 查找目标位置
  for (let i = 0; i < dragState.positions.length; i++) {
    const pos = dragState.positions[i];
    if (y >= pos.top && y <= pos.bottom) {
      return pos.index;
    }
  }
  // 如果在最顶部
  if (y < dragState.positions[0].top) {
    return 0;
  }
  // 如果在最底部
  if (y > dragState.positions[dragState.positions.length - 1].bottom) {
    return dragState.positions.length - 1;
  }
  return -1;
};

// 处理自动滚动 - 优化性能，减少异步操作
const handleAutoScroll = (e) => {
  // APP环境下安全处理事件对象
  if (!e || !e.touches || !e.touches[0]) {
    console.warn('自动滚动时事件对象无效');
    return;
  }
  // 获取视口高度
  const windowHeight = uni.getSystemInfoSync().windowHeight;
  const touchY = e.touches[0].clientY;
  const topThreshold = 150; // 顶部触发区域
  const bottomThreshold = windowHeight - 150; // 底部触发区域
  let scrollSpeed = 0;
  // 使用缓存的边界信息进行快速检测
  if (dragState.cachedBounds) {
    // 判断是否在列表区域内
    const isInListArea = touchY >= dragState.cachedBounds.top - 20 && 
                         touchY <= dragState.cachedBounds.bottom + 20;
    // 只有在列表区域内才允许自动滚动
    if (isInListArea) {
      // 修改向上滚动的速度计算逻辑，使其与向下滚动保持一致
      if (touchY < topThreshold) {
        // 向上滚动 - 使用线性速度
        scrollSpeed = -5 * ((topThreshold - touchY) / topThreshold);
        // 限制最大速度
        if (scrollSpeed < -8) scrollSpeed = -8;
        // 检查是否已经滚动到顶部
        if (dragState.cachedBounds.scrollContainer && 
            dragState.cachedBounds.scrollContainer.scrollTop <= 0 && 
            scrollSpeed < 0) {
          scrollSpeed = 0; // 已经到顶部，停止向上滚动
        }
      } else if (touchY > bottomThreshold) {
        // 向下滚动 - 使用线性速度
        scrollSpeed = 5 * ((touchY - bottomThreshold) / (windowHeight - bottomThreshold));
        // 限制最大速度
        if (scrollSpeed > 8) scrollSpeed = 8;
        
        // 检查是否已经滚动到底部
        if (dragState.cachedBounds.scrollContainer && 
            dragState.cachedBounds.scrollContainer.scrollTop + dragState.cachedBounds.clientHeight >= 
            dragState.cachedBounds.scrollHeight && 
            scrollSpeed > 0) {
          scrollSpeed = 0; // 已经到底部，停止向下滚动
        }
      }
    }
  }
  // 更新自动滚动速度
  dragState.autoScrollSpeed = scrollSpeed;
  // 如果需要滚动且没有定时器，创建定时器
  if (scrollSpeed !== 0 && !dragState.autoScrollTimer) {
    dragState.autoScrollTimer = setInterval(() => {
      if (pagingRef.value) {
        // 获取当前滚动位置
        const currentScrollTop = pagingRef.value.scrollTop || 0;
        // 更新滚动位置
        const newScrollTop = currentScrollTop + dragState.autoScrollSpeed;
        // 确保滚动位置不会小于0（防止过度向上滚动）
        const safeScrollTop = Math.max(0, newScrollTop);
        // 检查是否真的需要更新滚动位置
        if (safeScrollTop !== currentScrollTop) {
          pagingRef.value.scrollTop = safeScrollTop;
          // 更新当前Y位置，确保拖拽逻辑能正确计算目标位置
          dragState.currentY += dragState.autoScrollSpeed;
          // 减少位置更新频率
          updatePositions();
          // 在滚动后重新计算目标索引并执行交换
          const targetIndex = findTargetIndex(dragState.currentY);
          if (targetIndex !== -1 && targetIndex !== dragIndex.value) {
            // 交换数据
            const temp = dataList.value[dragIndex.value];
            dataList.value.splice(dragIndex.value, 1);
            dataList.value.splice(targetIndex, 0, temp);
            // 更新拖拽索引
            dragIndex.value = targetIndex;
          }
        }
      }
    }, 16); // 约60fps
  } else if (scrollSpeed === 0 && dragState.autoScrollTimer) {
    // 如果不需要滚动但有定时器，清除定时器
    clearInterval(dragState.autoScrollTimer);
    dragState.autoScrollTimer = null;
  }
};

// 修改触摸结束处理函数，简化目标元素查找逻辑
const handleTouchEnd = () => {
  // 清除长按定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
    longPressTimer.value = null;
  }
  
  // 清除自动滚动定时器
  if (dragState.autoScrollTimer) {
    clearInterval(dragState.autoScrollTimer);
    dragState.autoScrollTimer = null;
  }
  
  // 如果不是拖拽状态，重置所有状态并返回
  if (!isDragging.value) {
    // 确保所有状态都被重置
    resetAllDragState();
    return;
  }
  
  // 如果拖拽超出了范围，刷新列表恢复原始状态
  if (dragState.isOutOfBounds) {
    // 显示提示
    uni.showToast({
      title: '该区域不支持拖拽,请在列表内拖拽!',
      icon: 'none',
      duration: 1500
    });
    // 重置所有拖拽状态
    resetAllDragState();
    return;
  }
  
  // 获取当前拖拽项的新位置索引
  const newIndex = dragIndex.value;
  const originalIndex = dragState.originalIndex;
  // 如果位置没有变化，不需要更新
  if (newIndex === originalIndex) {
    resetAllDragState();
    return;
  }
  // 获取拖拽的元素
  const draggedItem = dragState.originalItem; // 使用原始元素信息
  
  let targetSortNo;
  // 重新构建原始列表状态来找到目标位置的元素
  // 先恢复原始顺序
  const originalList = [...dataList.value];
  // 将拖拽元素移回原位置
  const currentDraggedItem = originalList.splice(newIndex, 1)[0];
  originalList.splice(originalIndex, 0, currentDraggedItem);
  
  // 现在从原始列表中获取目标位置的元素
  const targetItem = originalList[newIndex];
  
  if (targetItem && targetItem.sort_no !== undefined) {
    targetSortNo = targetItem.sort_no;
  } else {
    // 如果目标元素没有sort_no，使用备用逻辑
    if (newIndex === 0) {
      // 拖到第一个位置，使用原来第一个元素的sort_no
      const firstItem = originalList[0];
      targetSortNo = firstItem ? firstItem.sort_no : 1;
    } else {
      // 使用相邻元素的sort_no
      const prevItem = originalList[newIndex - 1];
      targetSortNo = prevItem ? prevItem.sort_no + 1 : newIndex + 1;
    }
  }
  // 保存原始的sort_no用于交换
  const originalSortNo = draggedItem.sort_no;
  
  // 重置所有拖拽状态
  resetAllDragState();
  
  // 执行sort_no交换
  saveSortOrderExchange(draggedItem, targetSortNo, originalSortNo);
};

// 重置所有拖拽状态的函数
const resetAllDragState = () => {
  // 清除所有定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
    longPressTimer.value = null;
  }
  if (dragState.autoScrollTimer) {
    clearInterval(dragState.autoScrollTimer);
    dragState.autoScrollTimer = null;
  }
  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
    throttleTimer.value = null;
  }
  
  // 重置拖拽状态
  isDragging.value = false;
  dragIndex.value = -1;
  dragState.dragFeedbackIndex = -1;
  dragState.autoScrollSpeed = 0;
  dragState.isOutOfBounds = false;
  dragState.isHorizontalMove = false;
  dragState.startY = 0;
  dragState.currentY = 0;
  dragState.startTime = 0;
  dragState.touchStartX = 0;
  dragState.touchStartY = 0;
  dragState.originalIndex = -1;
  dragState.originalItem = null;
  // 清除缓存的边界信息
  dragState.cachedBounds = null;
  dragState.boundsUpdateTime = 0;
  // 重新启用z-paging的滚动
  if (pagingRef.value) {
    pagingRef.value.setScrollEnable && pagingRef.value.setScrollEnable(true);
  }
  
  // 设置防止点击标记，短暂时间内阻止点击事件
  preventClick.value = true;
  setTimeout(() => {
    preventClick.value = false;
  }, 300);
};

// 初始化列表
const queryList = async (pageNo, pageSize) => {
  try {
    const res = await getCustomerList({
      page: pageNo || 1,
      limit: pageSize || 50,
    });
    console.log("客户-数据获取成功:", res);
    const pageData = res.data.list || [];
    // 传递给分页组件
    pagingRef.value.complete(pageData);
    // 初始化排序功能
    nextTick(() => {
      initDragSort();
    });
  } catch (err) {
    console.error("请求失败:", err);
    uni.showToast({ title: "加载失败", icon: "none" });
  }
};

// 【右滑】点击删除事件
// const handleItemClick = async (e, item, index) => {
//   try {
//     if (e.position === "right") {
//       // 设置防点击标记
//       preventClick.value = true;
//       const res = await deleteCustomer({
//         customer_id: item.customer_id,
//       });
//       if (res.code === 0) {
//         uni.showToast({
//           title: "删除成功",
//           icon: "success",
//         });
//         queryList();
//         swipeRef.value[index].closeAll();
//         // 关键：直接返回避免后续逻辑
//         return;
//       }
//     }
//   } catch (err) {
//     console.error("请求失败:", err);
//   } finally {
//     // 确保标记最终会被重置
//     setTimeout(() => {
//       preventClick.value = false;
//     }, 700);
//   }
// };

// 打开新建弹框
const handleNewBuildClick = () => {
    showCustomerForm.value = true;
};
// 遇到 URL 中的特殊字符（如 &）未被正确处理的问题。在 URL 中，某些字符（如 &、=、? 等）具有特殊含义，需要进行 URL 编码，以避免引起解析错误。
const handeToEdit = (item) => {
  // 如果正在拖拽中或防点击标记为true，不触发点击事件
  // if (isDragging.value || preventClick.value) return;
  // 对公司简称进行URL编码
  const encodedTitle = encodeURIComponent(item.company_short_name);
  // 跳转到客户详情页面
  uni.navigateTo({
    url: `/pages/template/customer-details/index?customer_id=${item.customer_id}&title=${encodedTitle}`,
  });
};

// 处理表单提交
const handleCustomerSubmit = async (formData) => {
  try {
    const res = await addCustomer({
      ...formData,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "创建成功",
        icon: "success",
      });
      queryList();
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};
// 处理客户更新事件
const handleCustomerUpdated = (todoId) => {
  console.log("客户已更新，刷新列表", todoId);
  // 刷新列表数据
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};
// 保存排序结果到服务器 - 交换两个元素的sort_no
const saveSortOrderExchange = async (draggedItem, targetSortNo, originalSortNo) => {
  if (!draggedItem || !draggedItem.customer_id) {
    console.warn('保存排序时客户数据无效');
    return;
  }
  try {
    // 显示加载提示
    uni.showLoading({
      title: '保存排序中...',
      mask: true
    });
    
    // 更新拖拽元素的sort_no
    const draggedParams = {
      id: draggedItem.customer_id,
      sort_no: targetSortNo
    };
    // 调用排序接口更新拖拽元素
    const res = await updateCustomer(draggedParams);
    uni.hideLoading();
    if (res.code === 0) {
      uni.showToast({
        title: '排序已保存',
        icon: 'success',
        duration: 1500
      });
      pagingRef.value.reload()
    } else {
      uni.showToast({
        title: res.msg || '保存排序失败',
        icon: 'none',
        duration: 2000
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('保存排序失败:', error);
    uni.showToast({
      title: '保存排序失败',
      icon: 'none',
      duration: 2000
    });
  }
};

// 点击 tab 时触发，参数为Object
onTabItemTap(() => {
  uni.setTabBarStyle({
    selectedColor: "#1976D2", // 设置 Tab 栏选中状态下文字为金色
  });
});

// 监听页面显示，页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面
onShow(() => {
  uni.setTabBarStyle({
    selectedColor: "#1976D2",
  });
  // 刷新列表数据，解决切换页面后可能出现的超时问题
  pagingRef.value?.reload();
});

// 组件挂载后初始化
onMounted(() => {
  // 监听待办更新事件
  eventBus.on("customerUpdated", handleCustomerUpdated);
});

// 组件卸载时移除监听
onUnmounted(() => {
  eventBus.off("customerUpdated", handleCustomerUpdated);
})
</script>

<style lang="scss" scoped>
// 全局样式
@import "/styles/public-layout.scss";
.customer-container {
  padding: 20px 20px 0px 20px;
}
.customer-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  .item-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #e2e4e9;
    border-radius: 8px;
    background: #fff;
    text {
      margin-left: 4px;
      font-size: 14px;
      color: #333;
    }
  }
}
.section-box {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  .section-title {
    color: var(---, #787d86);
    font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}

.customer-item {
  display: flex;
  align-items: center;
  margin: 10px 12px 5px 12px;
  .content-wrapper {
    flex: 1;
    margin-left: 6px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 2px;
    overflow: hidden;
  }
  .customer-title {
    overflow: hidden;
    color: var(---, #000);
    text-overflow: ellipsis;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  .business-title {
    color: var(---, #000);
    text-overflow: ellipsis;
    font-size: 14px;
    fweight: 400;
    white-space: nowrap; /* 确保文本不换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    max-width: 100%; /* 确保宽度不超过父容器 */
  }
  .title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4px;
    color: var(---, #adb1ba);
    font-size: 12px;
    .customer-project {
      padding-right: 6px;
    }
  }
}

.sortable-list {
  border-radius: 10px;
  border: 1px solid rgba(226, 228, 233, 1);
  margin: 0 18px;
  background: rgba(255, 255, 255, 1);
}

.sortable-item {
  position: relative;
  transition: transform 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.15s ease,
  background-color 0.15s ease;
  will-change: transform, opacity;
  transform: translateZ(0); /* 强制GPU加速 */
  backface-visibility: hidden; /* 优化渲染性能 */
  :deep(.uni-swipe_box){
    max-height: 56px;
  }
  &:last-child {
    .content-wrapper{
      border-bottom: none;
    }
  }
  &.dragging {
    opacity: 0.9;
    transform: scale(1.02);
    background-color: #e6f7ff;
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.15);
    z-index: 1000;
    border-left: 4px solid #007aff;
    pointer-events: none;
  }

  &.drag-feedback {
    opacity: 0.4;
    transform: translateZ(0) scale(0.98);
    background-color: #e6f7ff;
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
    z-index: 999;
    border-left: 4px solid #007aff;
    position: relative;
    transition: all 0.1s ease;
  }
  &.out-of-bounds {
    border: 1px dashed #ff4d4f !important;
    background-color: rgba(255, 77, 79, 0.1) !important;
    opacity: 0.6 !important;
    transform: translateZ(0) scale(0.95) !important;
    box-shadow: 0 0 8px rgba(255, 77, 79, 0.5) !important;
    filter: grayscale(0.3);
  }

  &.placeholder {
    opacity: 0.4;
    background-color: #f5f5f5;
    border: 1px dashed #ccc;
    transform: translateZ(0);
  }
}
</style>