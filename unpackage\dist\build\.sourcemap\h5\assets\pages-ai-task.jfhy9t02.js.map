{"version": 3, "file": "pages-ai-task.jfhy9t02.js", "sources": ["../../../../../pages/ai-task.vue"], "sourcesContent": null, "names": ["mockApiData", "ref", "displayItems", "reactive", "isLoading", "isCompleted", "<PERSON><PERSON><PERSON><PERSON>", "isStreaming", "toolContents", "streamIndex", "processedIndex", "streamTimer", "currentToolCallId", "pollingAttempts", "async", "fetchMockData", "isInitialLoad", "value", "splice", "length", "clearTimeout", "params", "id", "uni.getStorageSync", "response", "fetchTaskResults", "console", "log", "data", "lastEvent", "event", "warn", "error", "completeStreamWithError", "processStream", "eventData", "eventType", "content", "lastItem", "type", "push", "Date", "now", "newToolId", "tool", "member_name", "toolArgs", "tool_args", "toolContent", "thought", "analysis", "toolItem", "find", "item", "lastLoadingTool", "slice", "reverse", "setTimeout", "schedulePolling", "completeStream", "message", "scrollToBottom", "nextTick", "pageScrollTo", "scrollTop", "duration", "isMarkdownContent", "some", "pattern", "test", "renderMarkdown", "html", "replace", "match", "split", "filter", "line", "map", "join", "firstItemMatch", "parseInt", "startsWith", "trim", "handleLinkClick", "node", "detail", "name", "href", "attrs", "window", "open", "navigateTo", "url", "fail", "err", "switchTab", "showToast", "title", "icon", "makePhoneCall", "phoneNumber", "substring", "location", "getToolContent", "toolId", "onMounted", "onBeforeUnmount"], "mappings": "8bAiGA,qCARM,MAAAA,EAAcC,EAAI,IAClBC,EAAeC,EAAS,IACxBC,EAAYH,GAAI,GAChBI,EAAcJ,GAAI,GAClBK,EAAWL,GAAI,GACfM,EAAcN,GAAI,GAClBO,EAAeP,EAAI,CAAA,GAKzB,IAAIQ,EAAc,EACdC,EAAiB,EACjBC,EAAc,KACdC,EAAoB,KACpBC,EAAkB,EAIPC,eAAAC,EAAcC,GAAgB,GAEtCX,EAAYY,OAAUX,EAASW,QAChCb,EAAUa,OAAQ,GAIlBD,IACWd,EAAAgB,OAAO,EAAGhB,EAAaiB,QACnBT,EAAA,EACCG,EAAA,EAClBL,EAAaS,MAAQ,IAIvBG,aAAaT,GACbJ,EAAYU,OAAQ,EAEhB,IACI,MACAI,EAAS,CAAEC,GADNC,EAAmB,OAAS,IAEjCC,QAAiBC,EAAiBJ,GAQpC,GAPIK,QAAAC,IAAI,gBAAiBH,GACjBxB,EAAAiB,MAAQO,EAASI,MAAQ,GAIrCtB,EAASW,OAAQ,EAEbjB,EAAYiB,MAAME,OAAST,EA+BjCN,EAAUa,OAAQ,EAClBV,EAAYU,OAAQ,EACpBZ,EAAYY,OAAQ,EACpBX,EAASW,OAAQ,EACHR,EAAAC,WAhCD,GAAAV,EAAYiB,MAAME,SAAWT,EAAgB,CAEhD,MAAAmB,EAAY7B,EAAYiB,MAAME,OAAS,EAAInB,EAAYiB,MAAMjB,EAAYiB,MAAME,OAAS,GAAK,KAC/FU,GAAiC,iBAApBA,EAAUC,aAMjC,MAEOJ,QAAQK,KAAK,2BAYjB,OATQC,GACCN,QAAAM,MAAM,UAAWA,KAEpB1B,EAASW,OAASJ,EA1DE,OA2DrBT,EAAUa,OAAQ,GAEtBX,EAASW,OAAQ,EACjBG,aAAaT,GACbsB,EAAwB,SACzB,CACH,CAaA,SAASC,IAEH,IAAC3B,EAAYU,MAEb,YADAG,aAAaT,GAKb,GAAAF,GAAeT,EAAYiB,MAAME,OAAQ,CAE3CZ,EAAYU,OAAQ,EACpBb,EAAUa,OAAQ,EAClBG,aAAaT,GAEP,MAAAkB,EAAY7B,EAAYiB,MAAME,OAAS,EAAInB,EAAYiB,MAAMjB,EAAYiB,MAAME,OAAS,GAAK,KASnG,YAPIU,GAAiC,iBAApBA,EAAUC,cAQ5B,CAGK,MAAAK,EAAYnC,EAAYiB,MAAMR,GAC9B2B,EAAYD,EAAUL,MAG5B,GAAkB,iBAAdM,EAIA,OAHAV,QAAQC,IAAI,2CACZjB,EAAiBD,EAAc,WAMnC,OAAQ2B,GACN,IAAK,aACHV,QAAQC,IAAI,eACZ,MACF,IAAK,cAEH,QAA0B,IAAtBQ,EAAUE,SAA+C,KAAtBF,EAAUE,QAAgB,CACzD,MAAAC,EAAWpC,EAAaiB,OAAS,EAAIjB,EAAaA,EAAaiB,OAAS,GAAK,KAE/EmB,GAA8B,SAAlBA,EAASC,KAEvBD,EAASD,SAAWF,EAAUE,QAG9BnC,EAAasC,KAAK,CAChBlB,GAAI,QAAQmB,KAAKC,SAASjC,IAC1B8B,KAAM,OACNF,QAASF,EAAUE,aAKxB,CACD,MACF,IAAK,kBAEH,MAAMM,EAAY,QAAQF,KAAKC,SAASjC,IAGxC,IAAI0B,EAAUS,MAAmC,OAA1BT,EAAUU,aAAkD,OAA1BV,EAAUU,YA2BjE3C,EAAasC,KAAK,CAChBlB,GAAIqB,EACJJ,KAAM,OACNF,QAASF,EAAUU,aAAe,OAClCzC,WAAW,QA/B2E,CAExF,MAAM0C,EAAWX,EAAUS,KAAKG,WAAa,CAAA,EAC7C,IAAIC,EAAc,GAEY,OAA1Bb,EAAUU,YAEEG,EAAA,GAAGF,EAASG,SAAW,KACF,OAA1Bd,EAAUU,cAELG,EAAA,GAAGF,EAASI,UAAY,MAIxChD,EAAasC,KAAK,CAChBlB,GAAIqB,EACJJ,KAAM,OACNF,QAASF,EAAUU,aAAe,OAClCzC,WAAW,IAIT4C,IACWxC,EAAAS,MAAM0B,GAAaK,EAE1C,CAS0BpC,EAAA+B,MAEpB,MACF,IAAK,oBAEH,MAAMQ,EAAWjD,EAAakD,MAC3BC,GAASA,EAAK/B,KAAOV,GAAqByC,EAAKjD,YAElD,GAAI+C,EACFA,EAAS/C,WAAY,MAChB,CAEJ,MAAMkD,EAAkBpD,EAAaqD,QAAQC,UAAUJ,MAAKC,GAAsB,SAAdA,EAAKd,MAAmBc,EAAKjD,YAC9FkD,GACCA,EAAgBlD,WAAY,EAC5BsB,QAAQK,KAAK,8DAGb7B,EAAasC,KAAK,CAChBlB,GAAI,QAAQmB,KAAKC,SAASjC,IAC1B8B,KAAM,OACNF,QAASF,EAAUU,aAAe,OAClCzC,WAAW,QAGbsB,QAAQK,KAAK,mFAEnB,CACmBnB,EAAA,KACpB,MACF,IAAK,eAEHc,QAAQC,IAAI,iBACZ,MACF,IAAK,eAEuB,IAAtBQ,EAAUE,SAA+C,KAAtBF,EAAUE,UAC/CnC,EAAasC,KAAK,CAChBlB,GAAI,WAAWmB,KAAKC,SAASjC,IAC7B8B,KAAM,OACNF,QAASF,EAAUE,mBAKvB,MACF,QACUX,QAAAK,KAAK,WAAYK,GAK7B1B,EAAiBD,EAAc,EAC/BA,IAGIF,EAAYU,QACEN,EAAA8C,WAAWvB,EA/OP,IAiPxB,CAGA,SAASwB,IAEL,GAAI7C,GApPqB,IAuPrB,OAFQa,QAAAM,MAAM,8BACdC,EAAwB,QAG5BpB,IACQa,QAAAC,IAAI,iBAAiBd,YAC7BT,EAAUa,OAAQ,EAClBV,EAAYU,OAAQ,EACpBG,aAAaT,GAEbA,EAAc8C,YAAW,KACrB1C,GAAc,EAAK,GAjQF,IAmQzB,CAGA,SAAS4C,IACHtD,EAAYY,QAChBS,QAAQC,IAAI,qBACZvB,EAAUa,OAAQ,EAClBV,EAAYU,OAAQ,EACpBZ,EAAYY,OAAQ,EACpBX,EAASW,OAAQ,EACCJ,EAAA,EAClBH,EAAiBV,EAAYiB,MAAME,OACnCC,aAAaT,OAEf,CAGA,SAASsB,EAAwB2B,GACvBlC,QAAAM,MAAM,2BAA4B4B,GAC1CxD,EAAUa,OAAQ,EAClBV,EAAYU,OAAQ,EACpBX,EAASW,OAAQ,EACCJ,EAAA,EAClBO,aAAaT,EACf,CAGA,SAASkD,IACPC,GAAS,KACUC,EAAA,CACfC,UAAW,MACXC,SAAU,IACX,GAEL,CAGA,SAASC,EAAkB7B,GACrB,IAACA,GAA8B,iBAAZA,EAA6B,OAAA,EAiBpD,MAfyB,CACvB,eACA,cACA,UACA,iBACA,iBACA,WACA,kBACA,kBACA,cACA,aACA,kBACA,WAGsB8B,MAAKC,GAAWA,EAAQC,KAAKhC,IACvD,CAEA,SAASiC,EAAejC,GACtB,IAAKA,EAAgB,MAAA,GACjB,IAqBF,IAAIkC,GAnBJlC,EAAUA,EAEPmC,QAAQ,6BAA6B,SAASC,GAK7C,MAAO,OAHWA,EAAMC,MAAM,MAAMC,QAAOC,GAAQ,kBAAkBP,KAAKO,KACvEC,KAAID,GAAQ,OAAOA,EAAKJ,QAAQ,eAAgB,aAAYM,KAAK,UAG5E,IAEON,QAAQ,6BAA6B,SAASC,GAEvC,MAAAM,EAAiBN,EAAMA,MAAM,mBAI5B,MAAA,cAHUM,EAAiBC,SAASD,EAAe,IAAM,MAC9CN,EAAMC,MAAM,MAAMC,QAAOC,GAAQ,kBAAkBP,KAAKO,KACvEC,KAAID,GAAQ,OAAOA,EAAKJ,QAAQ,eAAgB,aAAYM,KAAK,UAE5E,KAION,QAAQ,yBAA0B,SAClCA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QAEdA,QAAQ,2BAA4B,QAEpCA,QAAQ,4BAA6B,QAErCA,QAAQ,kBAAmB,eAC3BA,QAAQ,kBAAmB,eAC3BA,QAAQ,kBAAmB,eAC3BA,QAAQ,kBAAmB,eAC3BA,QAAQ,kBAAmB,eAC3BA,QAAQ,kBAAmB,eAE3BA,QAAQ,qBAAsB,gCAC9BA,QAAQ,iBAAkB,uBAC1BA,QAAQ,aAAc,eAEtBA,QAAQ,aAAc,iBAEtBA,QAAQ,sBAAuB,uBAE/BA,QAAQ,uBAAwB,6BAEhCA,QAAQ,cAAe,mBAEvBA,QAAQ,kBAAmB,+BAE3BA,QAAQ,sBAAuB,UAmB3B,OAhBAD,EAAAA,EAAKC,QAAQ,SAAU,WACvBD,EAAAA,EAAKC,QAAQ,MAAO,UAGtBD,EAAKU,WAAW,MAAwB,KAAhBV,EAAKW,SAChCX,EAAO,MAAMA,SAIfA,EAAOA,EACJC,QAAQ,YAAa,IACrBA,QAAQ,0CAA2C,MACnDA,QAAQ,4CAA6C,MACrDA,QAAQ,iBAAkB,MACrB9C,QAAAC,IAAI,qBAAsBU,GAC1BX,QAAAC,IAAI,qBAAsB4C,GAC3BA,CAIR,OAHQvC,GAEA,OADCN,QAAAM,MAAM,iBAAkBA,GACzBK,CACR,CACH,CAGA,SAAS8C,EAAgB9C,GACjB,MAAA+C,EAAO/C,EAAQgD,OAAOD,KACxB,GAAAA,GAAsB,MAAdA,EAAKE,KAAc,CAE7B,MAAMC,EAAOH,EAAKI,OAASJ,EAAKI,MAAMD,KACtC,IAAKA,EAAM,OACH7D,QAAAC,IAAI,QAAS4D,GAGjBA,EAAKN,WAAW,YAAcM,EAAKN,WAAW,YAMzCQ,OAAAC,KAAKH,EAAM,UAcTA,EAAKN,WAAW,KAEVU,EAAA,CACbC,IAAKL,EACLM,KAAOC,IAESC,EAAA,CACZH,IAAKL,EACLM,KAAM,KACUG,EAAA,CACZC,MAAO,SACPC,KAAM,QACP,GAEJ,IAGIX,EAAKN,WAAW,QAEPkB,EAAA,CAChBC,YAAab,EAAKc,UAAU,KAErBd,EAAKN,WAAW,WAGzBQ,OAAOa,SAASf,KAAOA,EAeRI,EAAA,CACbC,IAAKL,EACLM,KAAM,KACUG,EAAA,CACZC,MAAO,UACPC,KAAM,QACP,GAIR,CACH,CAKA,SAASK,EAAeC,GACf,OAAAhG,EAAaS,MAAMuF,IAAW,EACvC,QAGAC,GAAU,KACR1F,GAAc,EAAI,IAGpB2F,GAAgB,KACdtF,aAAaT,GACbJ,EAAYU,OAAQ,EACFJ,EAAA,EACDH,EAAA,CAAA,inCAhBK8F,OACbhG,EAAaS,MAAMuF,0NAD9B,IAAwBA"}