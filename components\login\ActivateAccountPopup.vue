<template>
  <view class="popup-mask" v-if="modelValue" @click.stop="handleMaskClick">
    <view class="popup-content" @click.stop>
      <view class="popup-header">
        <text class="popup-title">激活账号</text>
        <!-- <view class="close-icon" @click.stop="close">
          <uni-icons type="close" size="20" color="#999"></uni-icons>
        </view> -->
      </view>
      <view class="popup-body">
        <!-- 邀请码输入 -->
        <view class="input-group">
          <input class="input-field" type="text" placeholder="请输入邀请码" v-model="inviteCode" maxlength="8" />
        </view>
        <!-- 激活按钮 -->
        <view class="submit-button" :class="{ disabled: !inviteCode }" @click="handleActivate">
          激活账号
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted } from "vue";
import { activateAccount } from "@/http/auth.js";
import { getUserInfo } from "@/http/user.js";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  token: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue", "activate-success"]);

const inviteCode = ref("");

const close = () => {
  emit("update:modelValue", false);
};

const handleMaskClick = () => {
  // 点击遮罩层不关闭弹窗
};

const handleActivate = async () => {
  if (!inviteCode.value) {
    uni.showToast({
      title: "请输入邀请码",
      icon: "none",
    });
    return;
  }

  try {
    const res = await activateAccount({
      code: inviteCode.value,
      token: props.token,
    });

    if (res.code === 0) {
      try {
        // 获取最新的用户信息
        const userInfoRes = await getUserInfo();
        if (userInfoRes.code === 0) {
          uni.setStorageSync("userInfo", userInfoRes.data);
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
      }
      uni.showToast({
        title: "账号激活成功",
        icon: "success",
      });
      // 激活成功，通知父组件
      emit("activate-success");
      // 关闭弹窗
      close();
    } else {
      uni.showToast({
        title: res.msg || "激活失败",
        icon: "none",
      });
    }
  } catch (error) {
    uni.showToast({
      title: "激活失败",
      icon: "none",
    });
    console.error("激活失败:", error);
  }
};

onMounted(async () => {
  // 已登陆未激活
  const token = uni.getStorageSync("token");
  if (token !== undefined && token !== null && token !== '') {
    // 获取最新的用户信息
    const userInfoRes = await getUserInfo();
    if (userInfoRes.code === 0) {
      uni.setStorageSync("userInfo", userInfoRes.data);
      // 通知登录成功
      emit("activate-login", userInfoRes.data);
    }
  }
});
</script>

<style scoped lang="scss">
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;

  .popup-content {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    max-width: 750px;
    max-width: 750px;
    margin: 0 auto;
  }

  .popup-header {
    position: relative;
    padding: 20px 0;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
  }

  .popup-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .close-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    padding: 5px;
  }

  .popup-body {
    padding: 20px;
  }

  .input-group {
    margin-bottom: 15px;
  }

  .input-field {
    width: 100%;
    height: 45px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 15px;
    box-sizing: border-box;
  }

  .submit-button {
    height: 45px;
    background-color: #000;
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-top: 20px;
  }

  .submit-button.disabled {
    background-color: #ccc;
  }
}

// 响应式设计
@media screen and (max-width: 480px) {
  .login-verify-container {
    .popup-content {
      max-width: 375px;
    }
  }
}
</style>
