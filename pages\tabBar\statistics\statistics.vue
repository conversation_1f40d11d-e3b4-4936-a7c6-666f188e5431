<template>
  <view class="statistics" :style="appSafeAreaStyle">
  <!-- 统计概览卡片 - 每个卡片独占一行 -->
  <view class="stat-overview">
    <view class="stat-card">
      <view class="stat-label">跟进客户</view>
      <view class="stat-value">{{ funnelChartData.customer_count }}</view>
    </view>
    <view class="stat-card">
      <view class="stat-label">商机总额</view>
      <view class="stat-value">{{ funnelChartData.business_sum }}</view>
    </view>
    <view class="stat-card">
      <view class="stat-label">赢单总额</view>
      <view class="stat-value">{{ funnelChartData.business_won_sum }}</view>
    </view>
  </view>
  <!-- 商机转化漏斗 -->
  <view class="funnel-card stat-overview">
    <view class="card-header">
      <view class="card-title">商机转化漏斗</view>
    </view>
    <!-- 将图表和数据列表并排 -->
    <view class="funnel-chart-container">
      <view class="echarts-container" id="funnel-chart" ref="funnelChartRef" 
            :change:prop="shh.receiveData" 
            :prop="chartDataForRenderjs"></view>
    </view>
  </view>
  <!-- 客服入口组件 -->
  <CustomerService />
</view>
</template>

<script>
import { fetchStatistic } from "@/http/statistics.js";
// 按需引入 VChart 漏斗图组件
import { VChart, 
  registerFunnelChart,
  registerTooltip,
  registerLabel,
  registerDomTooltipHandler
 } from "@visactor/vchart";
VChart.useRegisters([
  registerFunnelChart,
  registerTooltip,
  registerLabel,
  registerDomTooltipHandler,
]);
import CustomerService from "@/components/CustomerService.vue";

export default {
components: {
  CustomerService
},
data() {
  return {
    funnelChart: null,
    // 漏斗图数据
    funnelData: [],
    funnelChartData: {},
    // 用于传递给renderjs的数据
    chartDataForRenderjs: {},
    // 定义颜色映射
    stageColorMap: {
      "初次接触": "#5387EE",
      "需求确认": "#48C5DB",
      "竞品分析": "#FF8B2C",
      "商务谈判": "#FFC60A",
      "赢单关闭": "#7BC335",
    }
  };
},
computed: {
  // APP环境下的安全区域样式
  appSafeAreaStyle() {
    // #ifdef APP-PLUS
    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
    return {
      paddingTop: `${statusBarHeight}px`
    };
    // #endif
    return {};
  }
},
methods: {
  /**
   * 过滤漏斗图阶段数据
   * @param {...string} excludeKeys - 要排除的阶段键名
   * @returns {Array} - 过滤后的商机列表
   */
  filterStages(...excludeKeys) {
    // 如果传递了 excludeKeys，过滤掉这些阶段的商机
    if (excludeKeys.length > 0) {
      return Object.entries(this.funnelChartData.business_stage_list)
        .filter(([key]) => !excludeKeys.includes(key))  // 过滤掉指定的阶段
        .map(([key, value]) => value)  // 取出各个阶段的商机列表
        .flat();  // 将嵌套的商机数组展平成一维数组
    }
    // 如果没有传递 excludeKeys，返回所有商机，保持原始的顺序和数量
    return Object.values(this.funnelChartData.business_stage_list).flat();
  },

  /**
   * 初始化漏斗图
   * @param {Object} businessStageData - 业务阶段数据
   */
  initFunnelChart(businessStageData = null) {
    // #ifdef H5
    // 非APP端使用原有逻辑 获取实际的 DOM 元素
      const chartDom = this.$refs.funnelChartRef.$el;
      if (!chartDom) {
        console.error('无法获取图表 DOM 元素');
        return;
      }
    this.createChart(chartDom, businessStageData);
    // #endif
  },
  
  /**
   * 创建图表实例
   * @param {Element} chartDom - 图表容器 DOM 元素
   * @param {Object} businessStageData - 业务阶段数据
   */
  createChart(chartDom, businessStageData = null) {
    // 默认数据
    let chartValues = [];
    // 如果有传入的业务阶段数据，则转换为图表所需的数组格式
    if (businessStageData && typeof businessStageData === "object") {
      // 删除值为0的属性
      Object.keys(businessStageData).forEach((key) => {
        if (businessStageData[key] === 0) {
          delete businessStageData[key];
        }
      });
      // 将对象格式转换为数组格式
      chartValues = Object.entries(businessStageData)
        .map(([stageName, count], index) => {
          // 计算百分比（如果有总数的话可以计算实际百分比）
          // const total = Object.values(businessStageData).reduce(
          //   (sum, val) => sum + val,
          //   0
          // );
          return {
            value: count,
            name: stageName,
          };
        })
        .filter((item) => item !== null); // 过滤掉null值
    } else {
      // 使用默认数据
      chartValues = [
        { value: 0, name: "初次接触"},
        { value: 0, name: "需求确认"},
        { value: 0, name: "竞品分析"},
        { value: 0, name: "商务谈判"},
        { value: 0, name: "赢单关闭"},
      ];
    }
    const spec = {
      type: "funnel",
      maxSize: "85%",
      minSize: "15%",
      isTransform: true,
      shape: "rect",
      color: {
        type: "ordinal",
        range: ["#5387EE", "#48C5DB", "#FF8B2C", "#FFC60A", "#7BC335"],
      },
      // 添加 tooltip 配置
      tooltip: {
        visible: true,
        renderMode: "html",
        mark: {
          title:{
            visible: false,
            // field: 'value',
            // value:'当前阶段商机'
          },
          // shapeFill: (datum,data) => {
          //   // 获取当前阶段在业务阶段数据中的索引
          //   const stageIndex = Object.keys(this.funnelChartData.business_stage_count || {}).findIndex(stage => stage === data.datum.name);
          //   // 定义颜色数组，与漏斗图颜色保持一致
          //   const colors = ["#5387EE", "#48C5DB", "#FF8B2C", "#FFC60A", "#7BC335"];
          //   // 返回对应阶段的颜色，如果找不到则返回第一个颜色
          //   return colors[stageIndex] || colors[0];
          // },
          content: (datum,data) => {
            // 获取当前阶段的商机列表
            // const stageList = this.funnelChartData.business_stage_list?.[data.datum.name] || [];
            // if (stageList.length === 0) {
            //   return [{ key: '', value: '暂无商机' }];
            // }
            // 获取所有阶段的商机
            if (data.datum.name === '初次接触') {
              // 将所有阶段的商机标题提取到一个一维数组
              // const allTitles = this.filterStages();
              const allTitles = this.filterStages('需求确认','竞品分析','商务谈判','赢单关闭');
              console.error('allTitles:', allTitles);
              // 返回商机标题并分配颜色
              return allTitles.map((item, index) => ({
                key: item.title,
                shapeFill: item.color,
                // 根据索引分配颜色，超出数组长度时循环使用
              }));
            }
            if (data.datum.name === '需求确认') {
              const filteredStages = this.filterStages('初次接触','竞品分析','商务谈判','赢单关闭');
              return filteredStages.map((item, index) => ({
                key: item.title,
                // value: '',
                shapeFill: item.color,
              }));
            }
            if (data.datum.name === '竞品分析') {
              const filteredStages = this.filterStages('初次接触','需求确认','商务谈判','赢单关闭');
              return filteredStages.map((item, index) => ({
                key: item.title,
                // value: '',
                shapeFill: item.color,
              }));
            }
            if (data.datum.name === '商务谈判') {
              const filteredStages = this.filterStages('初次接触','需求确认','竞品分析','赢单关闭');
              return filteredStages.map((item, index) => ({
                key: item.title,
                shapeFill: item.color,
              }));
            }
            if (data.datum.name === '赢单关闭') {
              const filteredStages = this.filterStages('初次接触', '需求确认','竞品分析','商务谈判');
              return filteredStages.map((item, index) => ({
                key: item.title,
                shapeFill: item.color,
              }));
            }
          }
        },
        style: {
          panel: {
            padding: 10,
            backgroundColor: '#ffffff',
            border: {
              color: '#e2e4e9',
              width: 1,
              radius: 6
            },
            shadow: {
              x: 0,
              y: 2,
              blur: 8,
              spread: 0,
              color: 'rgba(0, 0, 0, 0.1)'
            },
            maxWidth: 220,
            minWidth: 160
          },
          keyLabel: {
            fontSize: 12,
            fill: '#3e4551',
            fontWeight: '400',
            multiLine: true,
            maxWidth: 200,
            "spacing": 0
          },
          valueLabel: {
            fontSize: 12,
            fill: '#000000',
            fontWeight: '400',
            maxWidth: 100,
            multiLine: true,
          },
        },
      },
      funnel: {
        style: {
          cornerRadius: 4,
          stroke: "white",
          lineWidth: 2,
        },
        state: {
          hover: {
            stroke: "#4e83fd",
            lineWidth: 1,
          },
        },
      },
      title: {
        text: this.generateTitle(this.funnelChartData),
        orient: "top",
        style: {
          textAlign: "center",
          fontSize: 12,
        },
        textStyle: {
          fontSize: 12,
          align: "center",
        },
        subtextStyle: {
          fontSize: 12,
          align: "center",
        },
      },
      totalLabel: {
        visible: true,
        style: {
          fontSize: 12,
          textBaseline: "middle",
          textAlign: "center",
        },
      },
      label: {
        visible: true,
        style: {
          fill: "black",
          lineHeight: 16,
          fontSize: 12,
          limit: Infinity,
          text: (datum) => [`${datum.name}`, `${datum.value}`],
        },
      },
      // transformLabel: {
      //   visible: true,
      //   style: {
      //     fontSize: 10,
      //     fill: "black",
      //   },
      // },
      transform: {
        style: {
          stroke: "white",
          lineWidth: 2,
        },
        state: {
          hover: {
            stroke: "#4e83fd",
            lineWidth: 1,
          },
        },
      },
      data: [
        {
          name: "funnel",
          values: chartValues,
        },
      ],
      categoryField: "name",
      valueField: "value",
    };

    // 创建图表实例
    this.funnelChart = new VChart(spec, { dom: chartDom,mode:'mobile-browser' });
    // 绘制
    this.funnelChart.renderSync();
    // return this.funnelChart;
  },

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    if (this.funnelChart) {
      try {
        console.log('funnelChartRef',this.funnelChart);
        // this.funnelChart.resize();
        this.funnelChart._onResize(); // 正确销毁VChart图表
      } catch (error) {
        console.warn('图表resize失败:', error);
        // 如果resize失败，尝试重新初始化图表
      }
    }
  },

  /**
   * 动态生成 title 文本
   * @param {Object} data - 图表数据
   * @returns {string} - 标题文本
   */
  generateTitle(data) {
    let titleText = '';
    // 如果总转换率不为0，加入标题
    if (data.business_total_trans_rate !== 0) {
      titleText += `总转换率 ${data.business_total_trans_rate}%  `;
    }
    // 如果丢单率不为0，加入标题
    if (data.business_lost_close_rate !== 0) {
      titleText += ` 丢单率 ${data.business_lost_close_rate}%`;
    }
    return titleText;
  },

  /**
   * 处理 business_stage_list
   * @param {Object} businessStageList - 业务阶段列表
   * @returns {Object} - 处理后的业务阶段列表
   */
  addColorToBusinessStages(businessStageList) {
    // 遍历 business_stage_list 中的每个阶段
    Object.keys(businessStageList).forEach(stage => {
      // 获取当前阶段的商机数组
      const opportunities = businessStageList[stage];
      // 遍历每个商机，给它添加颜色
      opportunities.forEach(opportunity => {
        opportunity.color = this.stageColorMap[stage] || "#000000"; // 如果没有匹配的阶段，则默认给个黑色
      });
    });
    return businessStageList;
  },

  /**
   * 初始化获取图表数据
   * @param {boolean} isinit - 是否初始化
   */
  async loadChartData(isinit = true) {
    try {
      const res = await fetchStatistic({});
      if (res.code === 0) {
        console.error("初始化获取图表数据:", res.data);
        // 调用函数处理数据
        this.addColorToBusinessStages(res.data.business_stage_list);
        Object.assign(this.funnelChartData, res.data);
        this.funnelData = res.data.business_stage_count;
        // 使用获取到的业务阶段数据更新漏斗图
        if (this.funnelData && Object.keys(this.funnelData).length > 0) {
          // #ifdef APP-PLUS
          // APP端直接更新renderjs数据
          this.chartDataForRenderjs = {
            funnelData: this.funnelData,
            funnelChartData: this.funnelChartData,
            timestamp: Date.now()
          };
          // #endif

          if (isinit) {
             // 初始化图表并传入业务阶段数据
             this.initFunnelChart(this.funnelData);
           }
        }
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }
},
mounted() {
  // 确保DOM已经渲染
  setTimeout(() => {
    // #ifndef APP-PLUS
    // 非APP端才添加resize监听和初始化图表
    window.addEventListener("resize", this.handleResize);
    // this.loadChartData(true); // 重新加载数据
    // #endif
  }, 500);
},
beforeDestroy() {
  window.removeEventListener("resize", this.handleResize);
  if (this.funnelChart) {
    try {
      // 检查release方法是否存在
      if (typeof this.funnelChart.release === 'function') {
        this.funnelChart.release();
      } else {
        console.warn('VChart实例缺少release方法');
        // 尝试其他销毁方法
        if (typeof this.funnelChart.destroy === 'function') {
          this.funnelChart.destroy();
        }
      }
    } catch (error) {
      console.warn('图表销毁失败:', error);
    } finally {
      this.funnelChart = null;
    }
  }
},
// 点击 tab 时触发，参数为Object
onTabItemTap() {
  uni.setTabBarStyle({
    selectedColor: '#4D5BDE', // 设置 Tab 栏选中状态下文字为金色
  });
},
// 监听页面显示，页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面
onShow() {
  uni.setTabBarStyle({
    selectedColor: '#4D5BDE',
  });
  this.loadChartData(true); // 重新加载数据
  // console.log('====================1222');
},
onHide() {  
  if (this.funnelChart) {
    try {
      // 检查release方法是否存在
      if (typeof this.funnelChart.release === 'function') {
        this.funnelChart.release();
      } else {
        console.warn('VChart实例缺少release方法');
        // 尝试其他销毁方法
        if (typeof this.funnelChart.destroy === 'function') {
          this.funnelChart.destroy();
        }
      }
    } catch (error) {
      console.warn('图表销毁失败:', error);
    } finally {
      this.funnelChart = null;
    }
  }
}
};
</script>

<script module="shh" lang="renderjs">
export default {
  data() {
    return {
      funnelChart: null,
      isVChartLoaded: false,
      // 定义颜色映射
      stageColorMap: {
        "初次接触": "#5387EE",
        "需求确认": "#48C5DB",
        "竞品分析": "#FF8B2C",
        "商务谈判": "#FFC60A",
        "赢单关闭": "#7BC335",
      }
    }
  },
  methods: {
    /**
     * 动态加载VChart库
     */
     loadVChart() {
       return new Promise((resolve, reject) => {
         if (this.isVChartLoaded || window.VChart || window.VChart?.VChart) {
           this.isVChartLoaded = true;
           resolve();
           return;
         }
        const script = document.createElement('script');
        // script.src = 'https://unpkg.com/@visactor/vchart/build/index.min.js';
        script.src = 'static/js/vchart.min.js';
        script.onload = () => {
          console.log('VChart库加载成功');
          this.isVChartLoaded = true;
          resolve();
        };
        script.onerror = () => {
          console.error('VChart库加载失败');
          reject(new Error('VChart库加载失败'));
        };
        document.head.appendChild(script);
      });
    },

    /**
     * 初始化漏斗图
     * @param {Object} funnelData - 漏斗图数据
     * @param {Object} funnelChartData - 完整的图表数据
     */
    async initChart(funnelData, funnelChartData) {
      try {
        // 确保VChart库已加载
        if (!this.isVChartLoaded) {
          await this.loadVChart();
        }
        
        if (!window.VChart && !window.VChart?.VChart) {
           console.error('VChart库未正确加载');
           return;
         }
         
         // 获取VChart构造函数
         const VChart = window.VChart?.VChart || window.VChart;
         if (!VChart) {
           console.error('无法获取VChart构造函数');
           return;
         }

        // 获取图表容器
        const chartDom = document.getElementById('funnel-chart');
        if (!chartDom) {
          console.error('图表容器未找到');
          return;
        }

        // 销毁已存在的图表
        if (this.funnelChart) {
          try {
            this.funnelChart.release();
          } catch (error) {
            console.warn('图表销毁失败:', error);
          } finally {
            this.funnelChart = null;
          }
        }

        // 创建图表
        this.createChart(chartDom, funnelData, funnelChartData);
      } catch (error) {
        console.error('初始化图表失败:', error);
      }
    },

    /**
     * 创建图表实例
     * @param {Element} chartDom - 图表容器 DOM 元素
     * @param {Object} funnelData - 漏斗图数据
     * @param {Object} funnelChartData - 完整的图表数据
     */
    createChart(chartDom, funnelData, funnelChartData) {
      // 默认数据
      let chartValues = [];
      
      // 如果有传入的业务阶段数据，则转换为图表所需的数组格式
      if (funnelData && typeof funnelData === "object") {
        // 删除值为0的属性
        const filteredData = {};
        Object.keys(funnelData).forEach((key) => {
          if (funnelData[key] !== 0) {
            filteredData[key] = funnelData[key];
          }
        });
        
        // 将对象格式转换为数组格式
        chartValues = Object.entries(filteredData)
          .map(([stageName, count]) => {
            // 计算百分比
            // const total = Object.values(filteredData).reduce(
            //   (sum, val) => sum + val,
            //   0
            // );
            return {
              value: count,
              name: stageName,
            };
          })
          .filter((item) => item !== null);
      } else {
        // 使用默认数据
        chartValues = [
          { value: 0, name: "初次接触"},
          { value: 0, name: "需求确认"},
          { value: 0, name: "竞品分析"},
          { value: 0, name: "商务谈判"},
          { value: 0, name: "赢单关闭"},
        ];
      }

      const spec = {
        type: "funnel",
        maxSize: "85%",
        minSize: "15%",
        isTransform: true,
        shape: "rect",
        color: {
          type: "ordinal",
          range: ["#5387EE", "#48C5DB", "#FF8B2C", "#FFC60A", "#7BC335"],
        },
        // 简化的tooltip配置
        tooltip: {
          visible: true,
          renderMode: "html",
          mark: {
            title: {
              visible: false,
            },
            content: (datum, data) => {
              return [{
                key: `${data.datum.name}: ${data.datum.value}`,
                value: ''
              }];
            }
          },
          style: {
            panel: {
              padding: 10,
              backgroundColor: '#ffffff',
              border: {
                color: '#e2e4e9',
                width: 1,
                radius: 6
              },
              shadow: {
                x: 0,
                y: 2,
                blur: 8,
                spread: 0,
                color: 'rgba(0, 0, 0, 0.1)'
              },
              maxWidth: 220,
              minWidth: 160
            },
            keyLabel: {
              fontSize: 12,
              fill: '#3e4551',
              fontWeight: '400',
              multiLine: true,
              maxWidth: 200,
              "spacing": 0
            },
            valueLabel: {
              fontSize: 12,
              fill: '#000000',
              fontWeight: '400',
              maxWidth: 100,
              multiLine: true,
            },
          },
        },
        funnel: {
          style: {
            cornerRadius: 4,
            stroke: "white",
            lineWidth: 2,
          },
          state: {
            hover: {
              stroke: "#4e83fd",
              lineWidth: 1,
            },
          },
        },
        title: {
          text: this.generateTitle(funnelChartData),
          orient: "top",
          style: {
            textAlign: "center",
            fontSize: 12,
          },
          textStyle: {
            fontSize: 12,
            align: "center",
          },
          subtextStyle: {
            fontSize: 12,
            align: "center",
          },
        },
        totalLabel: {
          visible: true,
          style: {
            fontSize: 12,
            textBaseline: "middle",
            textAlign: "center",
          },
        },
        label: {
          visible: true,
          style: {
            fill: "black",
            lineHeight: 16,
            fontSize: 12,
            limit: Infinity,
            text: (datum) => [`${datum.name}`, `${datum.value}`],
          },
        },
        // transformLabel: {
        //   visible: true,
        //   style: {
        //     fontSize: 10,
        //     fill: "black",
        //   },
        // },
        transform: {
          style: {
            stroke: "white",
            lineWidth: 2,
          },
          state: {
            hover: {
              stroke: "#4e83fd",
              lineWidth: 1,
            },
          },
        },
        data: [
          {
            name: "funnel",
            values: chartValues,
          },
        ],
        categoryField: "name",
        valueField: "value",
      };

      // 获取VChart构造函数
       const VChart = window.VChart?.VChart || window.VChart;
       if (!VChart) {
         console.error('无法获取VChart构造函数');
         return;
       }
       
      // 创建图表实例
      this.funnelChart = new VChart(spec, { dom: chartDom,supportsTouchEvents: true});
      // 绘制
      this.funnelChart.renderSync();
      return this.funnelChart;
    },

    /**
     * 动态生成 title 文本
     * @param {Object} data - 图表数据
     * @returns {string} - 标题文本
     */
    generateTitle(data) {
      if (!data) return '';
      
      let titleText = '';
      // 如果总转换率不为0，加入标题
      if (data.business_total_trans_rate !== 0) {
        titleText += `总转换率 ${data.business_total_trans_rate}%  `;
      }
      // 如果丢单率不为0，加入标题
      if (data.business_lost_close_rate !== 0) {
        titleText += ` 丢单率 ${data.business_lost_close_rate}%`;
      }
      return titleText;
    },

    /**
     * 接收数据并更新图表
     * @param {Object} data - 传入的数据
     */
    receiveData(data) {
      console.log('renderjs接收到数据----------:', data);
      // #ifdef APP-PLUS
      if (!data || !data.funnelData) {
        console.warn('接收到的数据无效或缺少funnelData');
        return;
      }
      this.initChart(data.funnelData, data.funnelChartData);
      // #endif
    },

    /**
     * 销毁图表
     */
    destroyChart() {
      if (this.funnelChart) {
        try {
          this.funnelChart.release();
        } catch (error) {
          console.warn('图表销毁失败:', error);
        } finally {
          this.funnelChart = null;
        }
      }
    }
  },
  
  // mounted() {
  //   // console.log('renderjs模块已挂载');
  //   // 预加载VChart库
  // },

  beforeDestroy() {
    this.destroyChart();
  }
}
</script>

<style lang="scss" scoped>
// 媒体查询相关样式
@import '/styles/public-layout.scss';
.statistics {
padding: 5px 10px;
max-width: 750px;
margin: 0 auto;
// height: 95vh;
// background: red;
overflow: hidden;
font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
// 统计概览卡片样式
.stat-overview {
  margin-bottom: 20px;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 10px;
  border: solid 1px rgba(226, 228, 233, 1);
  padding: 0 14px;
}

.stat-card {
  height: 49px;
  max-height: 49px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid 1px rgba(226, 228, 233, 1);
  &:last-child {
    border-bottom: none;
    height: 50px;
    max-height: 50px;
  }
}

.stat-label {
  color: rgba(62, 69, 81, 1);
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
}

.stat-value {
  color: rgba(62, 69, 81, 1);
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;
}

// 漏斗图卡片样式
.funnel-card {
  background-color: #fff;
  padding: 12px 14px;
}

.card-header {
  margin-bottom: 5px;
  font-weight: 400;
  font-size: 14px;
  color: rgba(62, 69, 81, 1);
}
.card-title {
  margin-bottom: 5px;
  font-weight: 400;
  font-size: 14px;
  color: rgba(62, 69, 81, 1);
}

.funnel-chart-container {
  height: 450px;
  .echarts-container {
    width: 100%;
    height: 100%;
  }
}
}
</style>
