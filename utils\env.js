/* 生产环境(master分支):
 * - 生产环境的API URL: https://api.ivy.sale
 * - Web 地址: https://www.ivy.sale
 * - 使用 `checkEnv()` 返回 'production' 来标识生产环境
 *
 * 测试环境(test分支):
 * - 测试环境的API URL: https://apitest.profly.com.cn
 * - Web 地址: https://sale-test.profly.com.cn
 * - 使用 `checkEnv()` 返回 'test' 来标识测试环境
 *
 * 注意：`checkEnv()` 会根据当前URL和环境配置来判断是开发、测试还是生产环境。
 */

let apiUrl, apiPrefix, imgUrl,webUrl;

// 检测当前环境
const checkEnv = () => {
  // 检查是否有明确的环境标记
  // #ifdef H5
  if (window.location.href.includes('test=true') || 
      window.location.hostname.includes('test') || 
      window.location.hostname.includes('staging')) {
    return 'test';
  }
  // #endif
  return process.env.NODE_ENV || 'production';
  return 'production';
};

const currentEnv = checkEnv();
console.error('当前环境:', currentEnv); // 添加日志便于调试

// 使用 uniapp 的环境配置
if (currentEnv === 'development' || currentEnv === 'test') {
  // apiUrl = 'http://localhost:8787';
  apiUrl = 'https://apitest.profly.com.cn';
  webUrl = "https://sale-test.profly.com.cn";
} else if (currentEnv === 'production') {
  apiUrl = 'https://prod-api.ivy.pub';
  webUrl = "https://prod-web.ivy.pub";
}

export default {
  baseURL: apiUrl,
  webUrl: webUrl,
  apiPrefix: "api",
  currentEnv: currentEnv,
};
// 正式api:  https://api.ivy.sale 
// h5地址：  https://www.ivy.sale
// apiUrl = 'https://prod-api.ivy.pub';
// webUrl = "https://prod-web.ivy.pub";
