{
    "name" : "<PERSON>",
    "appid" : "__UNI__E060347",
    "description" : "销售代理Web应用，用于管理销售工作。",
    "versionName" : "1.2.7",
    "versionCode" : 27,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "permissions" : {
            "READ_EXTERNAL_STORAGE" : {
                "description" : "读取外部存储"
            },
            "WRITE_EXTERNAL_STORAGE" : {
                "description" : "写入外部存储"
            },
            "MANAGE_EXTERNAL_STORAGE" : {
                "description" : "管理外部存储"
            }
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 网络安全域名配置 */
        "allowHosts" : [ "*.profly.com.cn", "apitest.profly.com.cn" ],
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "Speech" : {},
            "Push" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.MANAGE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\"/>",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\"/>",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>"
                ],
                "minSdkVersion" : 21,
                "targetSdkVersion" : 30
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSMicrophoneUsageDescription" : "此应用需要使用麦克风进行语音输入功能",
                    "NSUserNotificationsUsageDescription" : "此应用需要推送通知权限以向您发送重要消息和提醒"
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "push" : {
                    "unipush" : {
                        "version" : "2",
                        "offline" : true,
                        "icons" : {
                            "push" : {
                                "ldpi" : "unpackage/res/push/large/48.png", //普通屏设备推送图标路径，分辨率要求48x48
                                "mdpi" : "unpackage/res/push/large/64.png", //大屏设备设备推送图标路径，分辨率要求64x64
                                "hdpi" : "unpackage/res/push/large/96.png", //高分屏设备推送图标路径，分辨率要求96x96
                                "xhdpi" : "unpackage/res/push/large/128.png", //720P高分屏设备推送图标路径，分辨率要求128x128
                                "xxhdpi" : "unpackage/res/push/large/192.png" //1080P高密度屏幕推送图标路径，分辨率要求192x192
                            },
                            "small" : {
                                "ldpi" : "unpackage/res/push/small/18.png", //可选，字符串类型，普通屏设备推送小图标路径，分辨率要求18x18
                                "mdpi" : "unpackage/res/push/small/24.png", //可选，字符串类型，大屏设备设备推送小图标路径，分辨率要求24x24
                                "hdpi" : "unpackage/res/push/small/36.png", //可选，字符串类型，高分屏设备推送小图标路径，分辨率要求36x36
                                "xhdpi" : "unpackage/res/push/small/48.png", //可选，字符串类型，720P高分屏设备推送小图标路径，分辨率要求48x48
                                "xxhdpi" : "unpackage/res/push/small/72.png", //可选，字符串类型，1080P高密度屏幕推送小图标路径，分辨率要求72x72
                                "xxxhdpi" : "unpackage/res/push/small/96.png" //可选，字符串类型，4K屏设备推送小图标路径，分辨率要求96x96
                            }
                        },
                        "hms" : {},
                        "vivo" : {},
                        "honor" : {}
                    }
                },
                "statics" : {},
                "speech" : {
                    "baidu" : {
                        "appid" : "119382824",
                        "apikey" : "lG47VJBl43NJnOZq2Ozf6hWx",
                        "secretkey" : "i3kCw1NYd9q1REZBthH9WQVZJZYI4vLo"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx1cd906ec02972132",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true,
            "postcss" : true
        },
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents", // 按需注入
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "template" : "template.h5.html",
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : ""
                }
            }
        },
        "async" : {
            "timeout" : 100000
        },
        "uniStatistics" : {
            "enable" : true
        },
        "devServer" : {
            "port" : 80,
            "proxy" : {
                "/api" : {
                    "target" : "https://apitest.profly.com.cn",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/api" : ""
                    }
                }
            }
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "title" : "销售助理Ivy",
        "unipush" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "uniStatistics" : {
        "enable" : true,
        "version" : "2",
        "reportInterval" : "10"
    },
    "vueVersion" : "3",
    "locale" : "zh-Hans",
    "fallbackLocale" : "zh-Hans",
    "app-harmony" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-harmony" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-xhs" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-baidu" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-alipay" : {
        "uniStatistics" : {
            "enable" : false
        }
    }
}
// http://local.spaceddd.com/

