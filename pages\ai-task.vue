<template>
  <view class="ai-task-container">

    <!-- 初始加载提示 -->
    <view
      v-if="isLoading && displayItems.length === 0"
      class="loading-indicator"
    >
      <uni-load-more status="loading" :showText="false"></uni-load-more>
      <text>AI 正在生成中...</text>
    </view>

    <!-- 流式内容展示区域 -->
    <view class="content-area">
      <view v-for="item in displayItems" :key="item.id" class="display-item">
        <!-- 工具调用展示 -->
        <view v-if="item.type === 'tool'" class="tool-item">
          <!-- 状态指示器和工具标题在同一行 -->
          <view class="tool-item-header">
            <!-- 状态指示器 -->
            <view v-if="item.isLoading" class="status-icon loading">
              <uni-load-more
                status="loading"
                :iconSize="16"
                :showText="false"
                class="tool-loading"
              ></uni-load-more>
            </view>
            <image v-else src="/static/global/is-completed.svg" class="status-icon completed"></image>
            
            <!-- 工具名称 -->
            <view class="tool-info">
              <!-- 对于思考和分析工具隐藏"使用工具"标签 -->
              <template v-if="item.content !== '思考' && item.content !== '分析'">
                <text class="tool-label">调用工具：</text>
                <text class="tool-name">{{ item.content }}</text>
              </template>
              <template v-else>
                <text class="tool-name">{{ item.content }}</text>
              </template>
            </view>
          </view>
          <!-- 工具详细内容区域，固定显示 -->
          <view 
            v-if="hasToolContent(item.id)" 
            class="tool-detail"
          >
            <view class="tool-content-inner">
              <text selectable="true">{{ getToolContent(item.id) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 文本展示 -->
        <view
          v-else-if="item.type === 'text'"
          class="text-item"
        >
          <!-- 根据内容类型选择渲染方式 -->
          <rich-text 
            v-if="isMarkdownContent(item.content)"
            :nodes="renderMarkdown(item.content)" 
            class="markdown-content"
            @itemclick="handleLinkClick"
          ></rich-text>
          <text v-else style="white-space: pre-wrap" selectable="true">{{ item.content }}</text>
        </view>
      </view>
    </view>

    <!-- 结束提示 -->
    <view v-if="isCompleted" class="completion-indicator">
      <uni-icons type="checkmarkempty" size="20" color="#18bc37"></uni-icons>
      <text>AI 生成完成</text>
    </view>

    <!-- 错误提示 -->
    <view v-if="hasError" class="error-indicator">
      <uni-icons type="closeempty" size="20" color="#e43d33"></uni-icons>
      <text>加载失败，请稍后重试</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from "vue";
import { fetchTaskResults } from "@/http/user";

// --- 状态定义 ---
const mockApiData = ref([]); // 存储从API获取的 *最新* 完整数据
const displayItems = reactive([]); // 存储用于页面展示的流式数据项
const isLoading = ref(true); // 是否正在加载初始数据或处理流
const isCompleted = ref(false); // 是否所有任务已完成
const hasError = ref(false); // 是否发生错误
const isStreaming = ref(false); // 是否正在进行流式处理 *当前* 数据块
const toolContents = ref({}); // 存储工具内容

const STREAM_INTERVAL = 35; // 流式输出间隔（毫秒）
const POLLING_INTERVAL = 1000; // 轮询间隔（毫秒）
const MAX_POLLING_ATTEMPTS = 600; // 最大轮询次数
let streamIndex = 0; // 当前正在处理 *当前* mockApiData 块的索引
let processedIndex = 0; // 已处理并加入 displayItems 的 mockApiData 的索引 (跨轮询)
let streamTimer = null; // 流式处理或轮询定时器
let currentToolCallId = null; // 记录当前正在进行的工具调用的ID
let pollingAttempts = 0; // 当前轮询次数计数器


// 模拟从后端获取数据
async function fetchMockData(isInitialLoad = false) {
  // 标记加载中，重置完成和错误状态 (除非已完成或出错)
  if (!isCompleted.value && !hasError.value) {
      isLoading.value = true;
  }

  // 如果是首次加载，清空显示内容和重置处理索引
  if (isInitialLoad) {
    displayItems.splice(0, displayItems.length);
    processedIndex = 0;
    pollingAttempts = 0; // 重置轮询计数
    toolContents.value = {}; // 重置工具内容
  }

  // 清除可能存在的上一个流处理或轮询定时器
  clearTimeout(streamTimer);
  isStreaming.value = false; // 确保在获取新数据前停止旧的流

  try {
    const id = uni.getStorageSync("id") || ""; // 获取ID或使用默认值
    const params = { id };
    const response = await fetchTaskResults(params);
    console.log("API Response:", response);
    mockApiData.value = response.data || []; // 更新为最新的完整数据
    // const response = await import("./ai-task1.json"); // 本地调试用
    // mockApiData.value = response.default
    // 获取成功后清除错误状态
    hasError.value = false;

    if (mockApiData.value.length > processedIndex) {
      // 如果有新数据 (新数据的长度大于已处理的索引)
      startStreaming(); // 开始流式处理新数据
    } else if (mockApiData.value.length === processedIndex) {
      // 没有新数据，检查是否需要轮询
      const lastEvent = mockApiData.value.length > 0 ? mockApiData.value[mockApiData.value.length - 1] : null;
      if (lastEvent && lastEvent.event === 'allCompleted') {
          completeStream(); // 如果最后一个是完成事件，则完成
      } else {
          // 没有新数据，但未完成，继续轮询
          schedulePolling();
      }
    } else {
       // 获取到空数据或异常情况 (例如 processedIndex > mockApiData.length)
       console.warn("获取到空数据或数据异常，将进行轮询...");
       schedulePolling();
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    // 只有在非轮询超时错误时才设置 isLoading = false
    if (!hasError.value || pollingAttempts < MAX_POLLING_ATTEMPTS) {
        isLoading.value = false;
    }
    hasError.value = true;
    clearTimeout(streamTimer); // 出错时停止轮询
    completeStreamWithError("获取数据失败"); // 传递更具体错误信息
  }
}

// 开始流式处理 (从上次结束的地方开始)
function startStreaming() {
  isLoading.value = true; // 标记开始处理
  isStreaming.value = true; // 标记开始流式处理 *当前* 数据块
  isCompleted.value = false; // 开始处理新数据，可能未完成
  hasError.value = false;
  streamIndex = processedIndex; // 设置开始处理的索引为上次处理结束的位置
  processStream(); // 立即开始处理第一个新事件
}

// 处理单个流事件
function processStream() {
  // 检查流处理是否应停止（例如组件卸载）
  if (!isStreaming.value) {
      clearTimeout(streamTimer);
      return;
  }

  // 检查是否处理完 *当前获取到的* 数据块
  if (streamIndex >= mockApiData.value.length) {
    // 当前数据块处理完毕
    isStreaming.value = false; // 停止处理当前块
    isLoading.value = false; // 处理完当前块，暂时不显示加载（除非轮询）
    clearTimeout(streamTimer); // 清除流处理定时器

    const lastEvent = mockApiData.value.length > 0 ? mockApiData.value[mockApiData.value.length - 1] : null;
    // 检查最后一个事件是否是 allCompleted
    if (lastEvent && lastEvent.event === 'allCompleted') {
      // 是最终数据，完成处理
      completeStream();
    } else {
      // 不是最终数据，需要轮询
      schedulePolling();
    }
    return; // 停止处理当前数据块
  }

  // --- 处理当前事件 ---
  const eventData = mockApiData.value[streamIndex];
  const eventType = eventData.event;

  // 如果在流中提前遇到 allCompleted 事件，直接完成
  if (eventType === 'allCompleted') {
      console.log("All Completed event received mid-stream");
      processedIndex = streamIndex + 1; // 标记处理到此
      completeStream();
      return; // 停止处理
  }

  // --- 根据事件类型更新显示 ---
  switch (eventType) {
    case "RunStarted":
      console.log("Run Started");
      break;
    case "RunResponse":
      // 修改：也接受只含换行符的内容，但过滤空字符串
      if (eventData.content !== undefined && eventData.content !== "") {
        const lastItem = displayItems.length > 0 ? displayItems[displayItems.length - 1] : null;
        // 检查最后一个 *已添加* 的项是否是文本，并且 *不是* 来自之前的轮询数据
        if (lastItem && lastItem.type === "text") {
          // 追加到现有文本项
          lastItem.content += eventData.content;
        } else {
          // 创建新的文本项
          displayItems.push({
            id: `text-${Date.now()}-${streamIndex}`, // 使用 streamIndex 保证 ID 唯一性
            type: "text",
            content: eventData.content,
          });
        }
        // 确保滚动到底部
        scrollToBottom();
      }
      break;
    case "ToolCallStarted":
      // 创建新的工具调用项
      const newToolId = `tool-${Date.now()}-${streamIndex}`;
      
      // 处理特殊工具：思考 和 分析
      if (eventData.tool && (eventData.member_name === "思考" || eventData.member_name === "分析")) {
        // 从工具参数中提取内容
        const toolArgs = eventData.tool.tool_args || {};
        let toolContent = "";
        
        if (eventData.member_name === "思考") {
          // 处理 思考 工具
          toolContent = `${toolArgs.thought || ""}`;
        } else if (eventData.member_name === "分析") {
          // 处理 分析 工具
          toolContent = `${toolArgs.analysis || ""}`;
        }
        
        // 同时创建工具调用项，存储实际工具名称
        displayItems.push({
          id: newToolId,
          type: "tool",
          content: eventData.member_name || "未知工具",
          isLoading: true,
        });
        
        // 存储工具内容
        if (toolContent) {
          toolContents.value[newToolId] = toolContent;
        }
      } else {
        // 处理普通工具调用
        displayItems.push({
          id: newToolId,
          type: "tool",
          content: eventData.member_name || "未知工具",
          isLoading: true,
        });
      }
      currentToolCallId = newToolId;
      scrollToBottom();
      break;
    case "ToolCallCompleted":
      // 找到对应的工具调用项并更新状态
      const toolItem = displayItems.find(
        (item) => item.id === currentToolCallId && item.isLoading // 确保找到的是正在加载的那个
      );
      if (toolItem) {
        toolItem.isLoading = false;
      } else {
         // 尝试查找最后一个未完成的工具调用（可能ID丢失，但不常见）
         const lastLoadingTool = displayItems.slice().reverse().find(item => item.type === 'tool' && item.isLoading);
         if(lastLoadingTool) {
             lastLoadingTool.isLoading = false;
             console.warn("ToolCallCompleted: Matched by finding last loading tool.");
         } else {
             // 如果没找到对应的开始事件或加载项，创建一个已完成的
             displayItems.push({
               id: `tool-${Date.now()}-${streamIndex}`,
               type: "tool",
               content: eventData.member_name || "未知工具",
               isLoading: false,
             });
             scrollToBottom();
             console.warn("ToolCallCompleted: No matching ToolCallStarted found or tool already completed.");
         }
      }
      currentToolCallId = null; // 清除当前工具调用ID
      break;
    case "RunCompleted":
      // 思考运行完成，可以忽略或添加标记
      console.log("Run Completed");
      break;
    case "summary":
      // 总结事件，只有当内容非空时才处理
      if (eventData.content !== undefined && eventData.content !== "") {
        displayItems.push({
          id: `summary-${Date.now()}-${streamIndex}`,
          type: "text",
          content: eventData.content,
        });
        scrollToBottom();
      }
      scrollToBottom();
      break;
    default:
      console.warn("未知的事件类型:", eventType);
      break;
  }

  // 更新已处理索引 *在* 成功处理完当前项之后
  processedIndex = streamIndex + 1;
  streamIndex++; // 移动到下一个要处理的事件

  // 设置处理下一个事件的定时器 (仅当还在流式处理当前块时)
  if (isStreaming.value) {
      streamTimer = setTimeout(processStream, STREAM_INTERVAL);
  }
}

// 安排下一次轮询
function schedulePolling() {
    // 检查是否超过轮询次数限制
    if (pollingAttempts >= MAX_POLLING_ATTEMPTS) {
        console.error(`已达到最大轮询次数 (${MAX_POLLING_ATTEMPTS})，停止轮询。`);
        completeStreamWithError("轮询超时");
        return; // 停止轮询
    }
    pollingAttempts++; // 增加轮询次数
    console.log(`当前数据块处理完成，准备第 ${pollingAttempts} 次轮询...`);
    isLoading.value = true; // 轮询时显示加载状态
    isStreaming.value = false; // 停止处理当前块
    clearTimeout(streamTimer); // 清除流处理定时器
    // 设置轮询定时器
    streamTimer = setTimeout(() => {
        fetchMockData(false); // 调用接口获取新数据，标记为非首次加载
    }, POLLING_INTERVAL);
}

// 正常完成流处理 (收到 allCompleted)
function completeStream() {
  if (isCompleted.value) return; // 防止重复执行
  console.log("Stream completed.");
  isLoading.value = false;
  isStreaming.value = false;
  isCompleted.value = true;
  hasError.value = false;
  pollingAttempts = 0; // 成功完成，重置计数器
  processedIndex = mockApiData.value.length; // 确保处理索引更新到最后
  clearTimeout(streamTimer); // 清除所有定时器
  scrollToBottom(); // 最后再滚动一次
}

// 因错误或数据不完整/超时而结束流处理
function completeStreamWithError(message) {
  console.error("Stream ended with error:", message);
  isLoading.value = false;
  isStreaming.value = false;
  hasError.value = true; // 确保错误状态被设置
  pollingAttempts = 0; // 出错或超时，重置计数器
  clearTimeout(streamTimer); // 清除所有定时器
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    uni.pageScrollTo({
      scrollTop: 99999, // 滚动到一个足够大的值，确保到底部
      duration: 10, // 平滑滚动时间
    });
  });
}

// 判断内容是否为 Markdown
function isMarkdownContent(content) {
  if (!content || typeof content !== 'string') return false;
  // 检测常见的 Markdown 语法特征
  const markdownPatterns = [
    /#{1,6}\s+.+/m,                  // 标题 (# 标题)
    /\*\*.+?\*\*/,                   // 粗体 (**粗体**)
    /\*.+?\*/,                       // 斜体 (*斜体*)
    /\[.+?\]\(.+?\)/,                // 链接 [链接](url)
    /```[\s\S]*?```/,                // 代码块
    /`[^`]+?`/,                      // 行内代码
    /^\s*[-*+]\s+.+/m,               // 无序列表
    /^\s*\d+\.\s+.+/m,               // 有序列表
    /^\s*>\s+.+/m,                   // 引用
    /\|.+\|.+\|/,                    // 表格
    /!\[.+?\]\(.+?\)/,               // 图片
    /~~.+?~~/,                       // 删除线
  ];
  // 如果匹配任一 Markdown 模式，则认为是 Markdown
  return markdownPatterns.some(pattern => pattern.test(content));
}
// 渲染 Markdown 为 HTML (不使用 marked 库)
function renderMarkdown(content) {
  if (!content) return '';
  try {
    // 先处理列表，避免后续处理干扰列表结构
    content = content
      // 处理无序列表块
      .replace(/((^\s*[-*+]\s+.+$\n?)+)/gm, function(match) {
        // 将每个列表项转换为 <li> 标签
        const listItems = match.split(/\n/).filter(line => /^\s*[-*+]\s+.+$/.test(line))
          .map(line => `<li>${line.replace(/^\s*[-*+]\s+/, '')}</li>`).join('');
        // 标记这个块已处理，避免后续的换行符处理
        return `<ul>${listItems}</ul>`;
      })
      // 处理有序列表块，动态获取起始数字
      .replace(/((^\s*\d+\.\s+.+$\n?)+)/gm, function(match) {
        // 获取第一个列表项的起始数字
        const firstItemMatch = match.match(/^\s*(\d+)\.\s+/m);
        const startNum = firstItemMatch ? parseInt(firstItemMatch[1]) : 1;
        const listItems = match.split(/\n/).filter(line => /^\s*\d+\.\s+.+$/.test(line))
          .map(line => `<li>${line.replace(/^\s*\d+\.\s+/, '')}</li>`).join('');
        return `<ol start="${startNum}">${listItems}</ol>`;
      });
    // 简单的 Markdown 转 HTML 实现
    let html = content
      // 转义 HTML 特殊字符，但跳过已处理的列表
      .replace(/&(?!lt;\/?(ul|ol|li))/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      // 还原已处理的列表标签
      .replace(/&lt;(\/?(ul|ol|li))&gt;/g, '<$1>')
      // 单独处理带有start属性的ol标签
      .replace(/&lt;(ol start="\d+")&gt;/g, '<$1>')
      // 处理标题 (# 标题)
      .replace(/^#{6}\s+(.+)$/gm, '<h6>$1</h6>')
      .replace(/^#{5}\s+(.+)$/gm, '<h5>$1</h5>')
      .replace(/^#{4}\s+(.+)$/gm, '<h4>$1</h4>')
      .replace(/^#{3}\s+(.+)$/gm, '<h3>$1</h3>')
      .replace(/^#{2}\s+(.+)$/gm, '<h2>$1</h2>')
      .replace(/^#{1}\s+(.+)$/gm, '<h1>$1</h1>')
      // 处理粗体和斜体
      .replace(/\*\*\*(.+?)\*\*\*/g, '<strong><em>$1</em></strong>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      // 处理删除线
      .replace(/~~(.+?)~~/g, '<del>$1</del>')
      // 处理链接 [文本](链接)
      .replace(/\[(.+?)\]\((.+?)\)/g, '<a href="$2">$1</a>')
      // 处理图片 ![alt](src)
      .replace(/!\[(.+?)\]\((.+?)\)/g, '<img src="$2" alt="$1" />')
      // 处理行内代码
      .replace(/`([^`]+?)`/g, '<code>$1</code>')
      // 处理引用
      .replace(/^\s*>\s+(.+)$/gm, '<blockquote>$1</blockquote>')
      // 处理水平线
      .replace(/^\s*[-*_]{3,}\s*$/gm, '<hr />');
    
    // 处理换行 - 重要改进：连续的换行符视为段落分隔
    html = html.replace(/\n\n+/g, '</p><p>'); // 多个连续换行作为段落分隔
    html = html.replace(/\n/g, '<br />'); // 单个换行转为<br/>
    
    // 处理段落 - 确保内容被包裹在段落标签中
    if (!html.startsWith('<') && html.trim() !== '') {
      html = `<p>${html}</p>`;
    }
    
    // 清理多余段落标签
    html = html
      .replace(/<p><\/p>/g, '') // 移除空段落
      .replace(/<p>(\s*<(h\d|ul|ol|blockquote|pre|hr))/g, '$1') // 移除段落中包含的块元素开始标签
      .replace(/(<\/(h\d|ul|ol|blockquote|pre)>)\s*<\/p>/g, '$1') // 修复块元素后的结束段落标签
      .replace(/<p>(\s*)<\/p>/g, '$1'); // 移除仅包含空白的段落
    console.log("Markdown original:", content);
    console.log("Markdown rendered:", html);
    return html;
  } catch (error) {
    console.error('Markdown 渲染错误:', error);
    return content; // 出错时返回原始内容
  }
}

// 拦截点击事件（只支持 a、img标签），返回当前node信息 event.detail={node}
function handleLinkClick(content) {
  const node = content.detail.node;
  if (node && node.name === 'a') {
    // 获取链接地址
    const href = node.attrs && node.attrs.href;
    if (!href) return;
    console.log('点击链接:', href);
    
    // 判断链接类型并执行相应操作
    if (href.startsWith('http://') || href.startsWith('https://')) {
      // 外部链接处理
      // #ifdef APP-PLUS
      plus.runtime.openURL(href);
      // #endif
      // #ifdef H5
      window.open(href, '_blank');
      // #endif
      // #ifdef MP
      // 小程序环境下复制链接到剪贴板
      uni.setClipboardData({
        data: href,
        success: () => {
          uni.showToast({
            title: '链接已复制，请在浏览器中打开',
            icon: 'none'
          });
        }
      });
      // #endif
    } else if (href.startsWith('/')) {
      // 内部应用路由链接
      uni.navigateTo({
        url: href,
        fail: (err) => {
          // 如果普通页面跳转失败，尝试 Tab 页面跳转
          uni.switchTab({
            url: href,
            fail: () => {
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      });
    } else if (href.startsWith('tel:')) {
      // 电话链接
      uni.makePhoneCall({
        phoneNumber: href.substring(4)
      });
    } else if (href.startsWith('mailto:')) {
      // 邮件链接
      // #ifdef H5
      window.location.href = href;
      // #endif
      // #ifndef H5
      uni.setClipboardData({
        data: href.substring(7),
        success: () => {
          uni.showToast({
            title: '邮箱地址已复制',
            icon: 'none'
          });
        }
      });
      // #endif
    } else {
      // 其他类型链接，尝试作为相对路径处理
      uni.navigateTo({
        url: href,
        fail: () => {
          uni.showToast({
            title: '无法打开此链接',
            icon: 'none'
          });
        }
      });
    }
  }
}
// 工具内容相关函数
function hasToolContent(toolId) {
  return !!toolContents.value[toolId];
}
function getToolContent(toolId) {
  return toolContents.value[toolId] || '';
}

// 生命周期钩子
onMounted(() => {
  fetchMockData(true); // 首次加载
});

onBeforeUnmount(() => {
  clearTimeout(streamTimer); // 组件卸载前清除定时器
  isStreaming.value = false; // 停止流处理和轮询
  pollingAttempts = 0; // 重置计数器
  processedIndex = 0; // 重置处理索引
});
</script>

<style lang="scss" scoped>
.ai-task-container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.loading-indicator,
.completion-indicator,
.error-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  color: #888;
  font-size: 14px;

  uni-icons {
    margin-right: 8px;
  }
}

.error-indicator {
  color: #e43d33;
}

.completion-indicator {
  color: #18bc37;
}

.content-area {
  flex: 1;
  padding: 0 0 5px;
}

.display-item {
  margin-bottom: 1px;
}

// 工具项样式
.tool-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  // 工具项标题行样式
  .tool-item-header {
    display: flex;
    align-items: center;
    
    // 状态图标样式
    .status-icon {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.loading {
        border: 1px solid #8a8a8a;
      }
      
      &.completed {
        width: 20px;
        height: 20px;
      }
    }
    
    // 工具信息样式
    .tool-info {
      display: flex;
      align-items: center;
      
      .tool-label {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(62, 69, 81, 1);
        margin-right: 4px;
        line-height: 20px; // 确保与图标高度一致
      }
      
      .tool-name {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(62, 69, 81, 1);
        line-height: 20px; // 确保与图标高度一致
      }
    }
  }
  
  // 工具详细内容区域
  .tool-detail {
    margin: 8px 0 0 30px; // 左侧与图标对齐，稍微向右偏移
    background-color: rgba(241, 244, 248, 1);
    padding: 8px 6px;
    border-radius: 10px;
    
    .tool-content-inner {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: rgba(120, 125, 134, 1);
      line-height: 1.5;
    }
  }
}

// 文本项样式
.text-item {
  margin: 5px 18px 0 18px;
  padding: 8px 6px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(62, 69, 81, 1);
  
  // Markdown 内容样式
  .markdown-content {
    width: 100%;
    font-size: 14px;
    line-height: 1.5;
    // #ifdef H5
    * {
      user-select: text !important;
      -webkit-user-select: text !important;
    }
    // #endif
    pre {
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 12px;
      overflow-x: auto;
      margin: 10px 0;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 13px;
      border: 1px solid #eee;
    }
    
    code {
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 13px;
      color: #e63c39;
    }
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 14px;
      margin-bottom: 8px;
      font-weight: 500;
      line-height: 1.25;
    }
    
    h1 { font-size: 18px; }
    h2 { font-size: 17px; }
    h3 { font-size: 16px; }
    h4 { font-size: 15px; }
    h5 { font-size: 14px; }
    h6 { font-size: 13px; }
    
    ul, ol {
      padding-left: 20px;
      margin: 10px 0;
    }
    
    li {
      margin: 4px 0;
    }
    
    a {
      color: #4388d6;
      text-decoration: none;
    }
    
    blockquote {
      border-left: 3px solid #ddd;
      padding: 0 16px;
      margin: 10px 0;
      color: #777;
    }
    
    img {
      max-width: 100%;
      margin: 10px 0;
    }
    
    p {
      margin: 10px 0;
      line-height: 1.5;
    }
    
    hr {
      height: 1px;
      background-color: #eee;
      margin: 14px 0;
      border: none;
    }
  }
}

// 工具加载状态样式
.tool-loading {
  margin-right: 0;
}
</style>
