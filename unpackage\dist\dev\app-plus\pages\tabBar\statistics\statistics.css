/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.customer-service[data-v-fec1fb0d] {
  position: fixed;
  z-index: 999;
  border-radius: 50%;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}
.customer-service.is-dragging[data-v-fec1fb0d] {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}
.customer-service.is-dragging .customer-icon[data-v-fec1fb0d] {
  opacity: 0.9;
}
.customer-service.is-pc[data-v-fec1fb0d]:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.2);
}
.customer-service .customer-icon[data-v-fec1fb0d] {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.customer-service .drag-indicator[data-v-fec1fb0d] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: pulse-fec1fb0d 1s infinite;
  pointer-events: none;
}
@keyframes pulse-fec1fb0d {
0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}
100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
}
}
@media (max-width: 768px) {
.customer-service[data-v-fec1fb0d]:active {
    transform: scale(0.95);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@media (max-width: 768px) {
.statistics[data-v-0ae6d406] {
    margin: 0 !important;
}
}
.z-paging-content[data-v-0ae6d406] {
  max-width: 750px;
  margin: 0 auto;
  /* 添加底部占位空间样式 */
}
.z-paging-content .styles-img[data-v-0ae6d406] {
  width: 25px;
  height: 25px;
}
.z-paging-content .bottom-space[data-v-0ae6d406] {
  height: 20px;
  width: 100%;
}
.z-paging-content .customer-img[data-v-0ae6d406] {
  width: 24px;
  height: 24px;
}
.task-detail-container[data-v-0ae6d406] {
  max-width: 750px;
  margin: 0 auto;
}
.uni-navbar[data-v-0ae6d406] .uni-navbar--border {
  border: none;
}
.uni-navbar[data-v-0ae6d406] .uni-nav-bar-text {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.statistics[data-v-0ae6d406] {
  padding: 5px 10px;
  max-width: 750px;
  margin: 0 auto;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.statistics .stat-overview[data-v-0ae6d406] {
  margin-bottom: 20px;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 10px;
  border: solid 1px #e2e4e9;
  padding: 0 14px;
}
.statistics .stat-card[data-v-0ae6d406] {
  height: 49px;
  max-height: 49px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid 1px #e2e4e9;
}
.statistics .stat-card[data-v-0ae6d406]:last-child {
  border-bottom: none;
  height: 50px;
  max-height: 50px;
}
.statistics .stat-label[data-v-0ae6d406] {
  color: #3e4551;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
}
.statistics .stat-value[data-v-0ae6d406] {
  color: #3e4551;
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;
}
.statistics .funnel-card[data-v-0ae6d406] {
  background-color: #fff;
  padding: 12px 14px;
}
.statistics .card-header[data-v-0ae6d406] {
  margin-bottom: 5px;
  font-weight: 400;
  font-size: 14px;
  color: #3e4551;
}
.statistics .card-title[data-v-0ae6d406] {
  margin-bottom: 5px;
  font-weight: 400;
  font-size: 14px;
  color: #3e4551;
}
.statistics .funnel-chart-container[data-v-0ae6d406] {
  height: 450px;
}
.statistics .funnel-chart-container .echarts-container[data-v-0ae6d406] {
  width: 100%;
  height: 100%;
}