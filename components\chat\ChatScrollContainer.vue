<template>
  <!-- @click="handleOuterClickStopVoice" -->
  <view class="chat-scroll-wrapper">
    <!-- :show-scrollbar="false" -->
    <!-- 聊天滚动容器 - 使用 column-reverse 实现自动滚动到底部 -->
    <scroll-view 
      class="chat-scroll-container"
      :scroll-y="true"
      :scroll-into-view="scrollIntoView"
      :scroll-anchoring="true"
      :enhanced="true"
      @scroll="handleScroll"
      @scrolltoupper="handleScrollToTop"
    >
      <!-- 消息列表容器 - 反向排列 :class="{ 'full-height': shouldUseFullHeight }"-->
      <view class="message-list-wrapper">
        <!-- 底部占位元素 - 用于实现 scroll-into-view -->
        <view id="bottom-anchor" class="bottom-anchor"></view>

        <!-- 消息列表 -->
        <view class="message-list" :style="{ paddingBottom: messagePaddingBottom }">
          <!-- 插槽内容 -->
          <slot name="messages"></slot>
        </view>

        <!-- 时间戳 -->
        <view class="timestamp">{{ `${getCurrentDay()} ${getCurrentTime()}` }}</view>
        
        <!-- 加载更多历史按钮 -->
        <view v-if="showLoadMore" class="load-more-container">
          <view
            class="load-more-button"
            @click="handleLoadMore"
            :class="{ 'loading': historyLoading }"
          >
            <uni-icons v-if="!historyLoading" type="reload" size="14" color="#666"></uni-icons>
            <uni-load-more v-else status="loading" :iconSize="14" :showText="false"></uni-load-more>
            <view class="load-more-text">{{ historyLoading ? '加载中...' : '加载更多历史' }}</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { getCurrentTime, getCurrentDay } from '@/utils/formatTime.js'

// Props定义
const props = defineProps({
  // 消息底部边距
  messagePaddingBottom: {
    type: String,
    default: '0'
  },
  // 是否显示加载更多按钮
  showLoadMore: {
    type: Boolean,
    default: false
  },
  // 加载更多是否正在加载
  historyLoading: {
    type: Boolean,
    default: false
  },
})

// Emits定义
const emit = defineEmits(['loadMore', 'scroll','outerClick'])

// 滚动相关状态
const scrollIntoView = ref('')
const isUserScrolling = ref(false)
const scrollTimer = ref(null)

/**
 * 滚动到指定目标元素
 * @param {string} targetId - 目标元素ID
 */
const scrollToTarget = async (targetId) => {
  if (!targetId) return
  console.log('滚动到目标元素:', targetId)

  // 先清空再设置，确保滚动触发
  scrollIntoView.value = ''
  await nextTick()
  // 设置目标ID
  scrollIntoView.value = targetId
}

/**
 * 滚动到底部
 * 优化后的置底逻辑，减少闪动，实现默认置底效果
 */
const scrollToBottom = async () => {
  console.log('ChatScrollContainer: 开始滚动到底部')

  // 方法1: 使用锚点滚动
  scrollIntoView.value = ''
  await nextTick()
  scrollIntoView.value = 'bottom-anchor'

  // 方法2: 延迟再次尝试，确保内容更新后滚动
  setTimeout(() => {
    console.log('ChatScrollContainer: 延迟滚动到底部')
    scrollIntoView.value = ''
    setTimeout(() => {
      scrollIntoView.value = 'bottom-anchor'
    }, 10)
  }, 50)
}

/**
 * 处理滚动事件
 */
const handleScroll = (e) => {
  // 标记用户正在滚动
  isUserScrolling.value = true
  // 清除之前的定时器
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
  
  // 设置新的定时器，在用户停止滚动后重置状态
  scrollTimer.value = setTimeout(() => {
    isUserScrolling.value = false
  }, 150)
  
  emit('scroll', e)
}

/**
 * fix 语音输入后，用户如果没有点“停止语音输入”，而是点到文字编辑区，也自动停止语音输入
 */
//  const handleOuterClickStopVoice = () => {
//   console.warn('语音输入后，用户如果没有点“停止语音输入”')
//   emit('outerClick')
// }

/**
 * 处理滚动到顶部
 */
const handleScrollToTop = () => {
  console.log('滚动到顶部')
  // 触发加载更多
  if (props.showLoadMore && !props.historyLoading) {
    handleLoadMore()
  }
}

/**
 * 处理加载更多
 */
const handleLoadMore = () => {
  if (props.historyLoading) return
  emit('loadMore')
}

/**
 * 恢复滚动位置（历史消息加载后调用）
 */
const restoreScrollPosition = async () => {
  // 使用 column-reverse 后，新消息会自动保持在底部
  // 加载历史消息时，由于是在顶部插入，不会影响当前视图位置
  console.log('历史消息加载完成，视图位置自动保持')
}

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  scrollToTarget,
  restoreScrollPosition
})

// 组件挂载时初始化
onMounted(() => {
  // console.log('ChatScrollContainer 组件已挂载')
  // 初始化自动滚动状态
  // shouldAutoScroll.value = true
  // 延迟初始滚动，确保DOM完全渲染
  nextTick(() => {
    scrollToBottom()
  })
})

</script>

<style scoped lang="scss">
.chat-scroll-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.chat-scroll-container {
  width: 100%;
  height: 100%;
  
  // 关键样式：使用 column-reverse 实现自动滚动到底部
  display: flex;
  flex-direction: column-reverse;
  :deep(.uni-scroll-view){
    .uni-scroll-view{
      // 隐藏滚动条样式
      // #ifdef H5
      &::-webkit-scrollbar {
        width: 4px;
        background: transparent;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 2px;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.2);
        }
      }
      // #endif
    }
  }
  // #ifdef APP-PLUS
  // APP端通过 show-scrollbar="false" 控制
  // #endif
}

.message-list-wrapper {
  display: flex;
  flex-direction: column-reverse;
  max-width: 750px;
  margin: 0 auto;

  // 只在未登录或消息列表为空时使用全高度
  &.full-height {
    min-height: 100%;
  }
}

.bottom-anchor {
  height: 1px;
  opacity: 0;
  flex-shrink: 0;
}

.message-list {
  padding: 10px 15px;
  box-sizing: border-box;
}

.timestamp {
  text-align: center;
  font-size: 12px;
  color: #999;
  padding: 15px 0;
  flex-shrink: 0;
  // #ifdef APP-PLUS
  margin-top: 55px;
  // #endif
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 15px 0;
  flex-shrink: 0;
  // #ifdef APP-PLUS
  margin-top: 55px;
  // #endif
  
  .load-more-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: #ffffff;
    border-radius: 20px;
    font-size: 13px;
    color: #666;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.95);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }
    
    &.loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .load-more-text {
      margin-left: 5px;
    }
  }
}

// 适配不同平台的高度
// #ifdef H5
.chat-scroll-container {
  height: calc(100vh - 183px);
}
// #endif

// #ifdef APP-PLUS
.chat-scroll-container {
  height: calc(100vh - 183px);
}
// #endif

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 新消息动画
.message-list > view {
  animation: fadeIn 0.3s ease-out;
}
</style>