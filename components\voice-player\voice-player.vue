<template>
  <view class="voice-player">
    <!-- 这个组件主要用于提供renderjs运行环境，UI部分为空 -->
    <!-- #ifdef APP-PLUS -->
    <view 
      ref="voicePlayerModule" 
      class="voice-player-module"
      :triggerAction="triggerAction"
      :change:triggerAction="voicePlayerModule.handleAction"
    ></view>
    <!-- #endif -->
  </view>
</template>

<script>
/**
 * 语音播放组件 - 简化版本
 * 专注于语音播放功能，提供清晰的API接口
 */
export default {
  name: 'VoicePlayer',
  props: {
    // 是否自动初始化
    autoInit: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      isPlaying: false,
      currentText: '',
      // #ifdef APP-PLUS
      triggerAction: { action: '', data: null, timestamp: 0 }
      // #endif
    };
  },
  mounted() {
    if (this.autoInit) {
      this.initVoicePlayer();
    }
  },
  methods: {
    /**
     * 初始化语音播放器
     */
    initVoicePlayer() {
      // #ifdef APP-PLUS
      this.$nextTick(() => {
        const token = uni.getStorageSync("aliyunToken") || '14232e35f12743f48f388652949ff050';
        console.log('初始化TTS服务');
        this.triggerRenderAction('initTtsService', token);
      });
      // #endif
      
      // #ifdef H5
      this.initH5TtsService();
      // #endif
    },

    /**
     * H5端TTS服务初始化
     */
    // #ifdef H5
    async initH5TtsService() {
      try {
        const { createAliyunTts } = await import('../../utils/aliyunTts.js');
        const token = uni.getStorageSync("aliyunToken");
        if (!token) {
          console.warn('语音合成token不存在');
          return;
        }
        this.ttsServices = createAliyunTts({ token });
        // 设置播放结束回调
        this.ttsServices.setOnPlaybackEndCallback(() => {
          this.isPlaying = false;
          this.currentText = '';
        });
        // console.log('H5 TTS服务初始化成功');
      } catch (error) {
        console.error('H5 TTS服务初始化失败:', error);
      }
    },
    // #endif

    /**
     * 播放语音 - 主要API接口
     * @param {string} text - 要播放的文本
     * @returns {Promise<boolean>} 播放是否成功
     */
    async playVoice(text) {
      if (!text || typeof text !== 'string') {
        console.error('无效的文本内容');
        return false;
      }

      // 如果正在播放相同文本，则停止播放
      if (this.isPlaying && this.currentText === text) {
        this.stopVoice();
        return true;
      }

      // #ifdef APP-PLUS
      console.log('开始播放语音:', text);
      this.isPlaying = true;
      this.currentText = text;
      this.triggerRenderAction('playVoice', text);
      return true;
      // #endif

      // #ifdef H5
      try {
        if (!this.ttsServices) {
          await this.initH5TtsService();
        }
        
        if (this.ttsServices) {
          // 先停止当前播放
          this.ttsServices.stopAllPlayback();
          
          // 开始新的播放
          this.isPlaying = true;
          this.currentText = text;
          
          // 连接并播放
          this.ttsServices.connectAndStartSynthesis();
          await this.ttsServices.sendRunSynthesis(text);
          await this.ttsServices.sendStopSynthesis();
          
          return true;
        }
        return false;
      } catch (error) {
        console.error('H5语音播放失败:', error);
        this.isPlaying = false;
        this.currentText = '';
        return false;
      }
      // #endif
    },

    /**
     * 停止播放
     */
    stopVoice() {
      // #ifdef APP-PLUS
      this.triggerRenderAction('stopVoice');
      // #endif

      // #ifdef H5
      if (this.ttsServices) {
        this.ttsServices.stopAllPlayback();
      }
      // #endif
      this.isPlaying = false;
      this.currentText = '';
    },

    /**
     * 暂停播放
     */
    pauseVoice() {
      // #ifdef APP-PLUS
      this.triggerRenderAction('pauseVoice');
      // #endif

      // #ifdef H5
      if (this.ttsServices && this.isPlaying) {
        this.ttsServices.pauseAudioPlayback();
        this.isPlaying = false;
      }
      // #endif
    },

    /**
     * 获取播放状态
     * @returns {boolean} 是否正在播放
     */
    getPlayingState() {
      return this.isPlaying;
    },

    /**
     * 获取当前播放文本
     * @returns {string} 当前播放的文本
     */
    getCurrentText() {
      return this.currentText;
    },

    // App端renderjs通信方法
    // #ifdef APP-PLUS
    /**
     * 触发renderjs动作
     */
    triggerRenderAction(action, data = null) {
      console.log('触发renderjs动作:', action);
      this.triggerAction = {
        action,
        data,
        timestamp: Date.now()
      };
    },


    // #endif
  }
};
</script>

<!-- #ifdef APP-PLUS -->
<script module="voicePlayerModule" lang="renderjs">
/**
 * App端renderjs模块 - WebView中的TTS服务
 * 简化版本，专注于语音播放功能
 */

// PCM音频播放器类
class PCMAudioPlayer {
  constructor(sampleRate = 24000) {
    this.sampleRate = sampleRate;
    this.audioContext = null;
    this.audioQueue = [];
    this.isPlaying = false;
    this.currentSource = null;
    this.onPlaybackEnd = null;
  }

  connect() {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
  }

  pushPCM(arrayBuffer) {
    this.audioQueue.push(arrayBuffer);
    this._playNextAudio();
  }

  _bufferPCMData(pcmData) {
    const sampleRate = this.sampleRate;
    const length = pcmData.byteLength / 2;
    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);
    const channelData = audioBuffer.getChannelData(0);
    const int16Array = new Int16Array(pcmData);

    for (let i = 0; i < length; i++) {
      channelData[i] = int16Array[i] / 32768;
    }
    
    return audioBuffer;
  }

  async _playAudio(arrayBuffer) {
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    const audioBuffer = this._bufferPCMData(arrayBuffer);
    this.currentSource = this.audioContext.createBufferSource();
    this.currentSource.buffer = audioBuffer;
    this.currentSource.connect(this.audioContext.destination);

    this.currentSource.onended = () => {
      this.isPlaying = false;
      this.currentSource = null;
      this._playNextAudio();
      
      // 如果队列为空，触发播放结束回调
      if (this.audioQueue.length === 0 && this.onPlaybackEnd) {
        this.onPlaybackEnd();
      }
    };

    this.currentSource.start(0);
    this.isPlaying = true;
  }

  _playNextAudio() {
    if (this.audioQueue.length > 0 && !this.isPlaying) {
      const totalLength = this.audioQueue.reduce((acc, buffer) => acc + buffer.byteLength, 0);
      const combinedBuffer = new Uint8Array(totalLength);
      let offset = 0;

      for (const buffer of this.audioQueue) {
        combinedBuffer.set(new Uint8Array(buffer), offset);
        offset += buffer.byteLength;
      }

      this.audioQueue = [];
      this._playAudio(combinedBuffer.buffer);
    }
  }

  stop() {
    if (this.currentSource) {
      this.currentSource.stop();
      this.currentSource = null;
    }
    this.isPlaying = false;
    this.audioQueue = [];
  }

  setOnPlaybackEndCallback(callback) {
    this.onPlaybackEnd = callback;
  }
}

// TTS配置
const TTS_CONFIG = {
  appkey: 'ZsmxFv9inn9RVE3N',
  url: 'wss://nls-gateway-cn-shenzhen.aliyuncs.com/ws/v1',
  voice: 'zhixiaoxia',
  format: 'PCM',
  sample_rate: 24000,
  volume: 50,
  speech_rate: 100,
  pitch_rate: 50,
  namespace: 'FlowingSpeechSynthesizer',
};

// 生成UUID
function generateUUID() {
  let d = new Date().getTime();
  let d2 = (performance && performance.now && (performance.now() * 1000)) || 0;
  return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16;
    if (d > 0) {
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
}

// 获取消息头
const getHeader = (config, task_id) => ({
  message_id: generateUUID(),
  task_id: task_id,
  namespace: config.namespace,
  name: '',
  appkey: config.appkey
});

module.exports = {
  data() {
    return {
      ttsServices: null,
      isInitialized: false
    };
  },
  methods: {
    /**
     * 处理来自逻辑层的动作
     */
    handleAction(newVal) {
      if (!newVal || !newVal.action) return;
      
      const { action, data } = newVal;
      console.log('renderjs 接收动作:', action);
      
      switch (action) {
        case 'initTtsService':
          this.initTtsService(data);
          break;
        case 'playVoice':
          this.playVoice(data);
          break;
        case 'stopVoice':
          this.stopVoice();
          break;
        case 'pauseVoice':
          this.pauseVoice();
          break;
      }
    },

    /**
     * 初始化TTS服务
     */
    initTtsService(token) {
      try {
        if (!token) {
          console.error('Token不存在，无法初始化TTS服务');
          return;
        }

        this.ttsServices = this.createAliyunTts({ ...TTS_CONFIG, token });
        this.isInitialized = true;
        console.log('TTS服务初始化成功');
      } catch (error) {
        console.error('TTS服务初始化失败:', error);
      }
    },

    /**
     * 创建阿里云TTS服务
     */
    createAliyunTts(config) {
      let ws = null;
      let task_id = null;
      let isSynthesisStarted = false;
      let player = new PCMAudioPlayer(config.sample_rate);
      let currentPlayingText = '';

      const connectAndStartSynthesis = () => {
        return new Promise((resolve, reject) => {
          ws = new WebSocket(`${config.url}?token=${config.token}`);
          ws.binaryType = "arraybuffer";
          
          ws.onopen = () => {
            task_id = generateUUID();
            const header = getHeader(config, task_id);
            const params = {
              header: { ...header, name: 'StartSynthesis' },
              payload: {
                voice: config.voice,
                format: config.format,
                sample_rate: config.sample_rate,
                volume: config.volume,
                speech_rate: config.speech_rate,
                pitch_rate: config.pitch_rate,
                enable_subtitle: true,
                platform: 'javascript'
              }
            };
            ws.send(JSON.stringify(params));
          };
          
          ws.onerror = (err) => {
            console.error('WebSocket错误:', err);
            reject(err);
          };
          
          ws.onmessage = (event) => {
            const data = event.data;
            if (data instanceof ArrayBuffer) {
              player.pushPCM(data);
            } else {
              const body = JSON.parse(data);
              if (body.header.name === 'SynthesisStarted' && body.header.status === 20000000) {
                isSynthesisStarted = true;
                resolve();
              }
              if (body.header.name === 'SynthesisCompleted' && body.header.status === 20000000) {
                ws = null;
                isSynthesisStarted = false;
              }
            }
          };
          
          player.connect();
        });
      };

      const sendRunSynthesis = async (text) => {
        currentPlayingText = text;
        if (ws && isSynthesisStarted) {
          const header = getHeader(config, task_id);
          const params = {
            header: { ...header, name: 'RunSynthesis' },
            payload: { text }
          };
          ws.send(JSON.stringify(params));
        }
      };

      const sendStopSynthesis = async () => {
        if (ws && isSynthesisStarted) {
          const header = getHeader(config, task_id);
          const params = {
            header: { ...header, name: 'StopSynthesis' }
          };
          ws.send(JSON.stringify(params));
        }
      };

      const stopAllPlayback = () => {
        if (ws) {
          ws.close();
          ws = null;
        }
        player.stop();
        currentPlayingText = '';
        isSynthesisStarted = false;
      };

      const setOnPlaybackEndCallback = (callback) => {
        player.setOnPlaybackEndCallback(() => {
          currentPlayingText = '';
          if (callback) callback();
        });
      };

      return {
        connectAndStartSynthesis,
        sendRunSynthesis,
        sendStopSynthesis,
        setOnPlaybackEndCallback,
        stopAllPlayback
      };
    },

    /**
     * 播放语音
     */
    async playVoice(text) {
      try {
        if (!this.isInitialized || !this.ttsServices) {
          console.error('TTS服务未初始化');
          return;
        }

        // 先停止当前播放
        this.ttsServices.stopAllPlayback();

        // 设置播放结束回调，自动更新逻辑层状态
        this.ttsServices.setOnPlaybackEndCallback(() => {
          console.log('语音播放结束');
        });

        // 开始播放流程
        await this.ttsServices.connectAndStartSynthesis();
        await this.ttsServices.sendRunSynthesis(text);
        await this.ttsServices.sendStopSynthesis();

      } catch (error) {
        console.error('播放语音失败:', error);
      }
    },

    /**
     * 停止播放
     */
    stopVoice() {
      if (this.ttsServices) {
        this.ttsServices.stopAllPlayback();
      }
    },

    /**
     * 暂停播放
     */
    pauseVoice() {
      // 暂停功能可以根据需要实现
      this.stopVoice();
    }
  }
};
</script>
<!-- #endif -->

<style lang="scss" scoped>
.voice-player {
  display: none; // 隐藏组件，只提供功能
}

.voice-player-module {
  display: none; // renderjs运行环境，不需要显示
}
</style> 