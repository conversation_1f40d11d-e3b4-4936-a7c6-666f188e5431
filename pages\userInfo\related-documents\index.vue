<!-- 相关文件详情 -->
<template>
  <z-paging
    ref="pagingRef"
    v-model="dataList"
    @query="queryFileList"
    :refresherEnabled="false"
    :defaultPageSize="30"
    :showLoadingMoreNoMoreView="false"
    :style="appSafeAreaStyle"
  >
    <template #top>
      <uni-nav-bar
        left-icon="left"
        :title="title"
        title-text-align="center"
        backgroundColor="#fff"
        color="#000"
        height="60px"
        @clickLeft="handerLeftBack"
      />
      <z-tabs :list="tabList" @change="tabsChange" />
    </template>
    <!-- 最近上传 -->
    <recentlyUploaded
      v-if="tabIndex === 0"
      :fileList="dataList"
      @refresh="refreshCurrentPage"
    />
    <!-- 销售物料 -->
    <productIntroduction
      v-if="tabIndex === 1"
      :fileList="dataList"
      @refresh="refreshCurrentPage"
    />
    <!-- 客户资料 -->
    <projectData
      v-if="tabIndex === 2"
      :fileList="dataList"
      @refresh="refreshCurrentPage"
    />
    <!-- AI 对话组件 -->
    <CustomerService />    
  </z-paging>
</template>
<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import recentlyUploaded from "./recently-uploaded.vue";
import productIntroduction from "./product-Introduction.vue";
import projectData from "./project-data.vue";
import CustomerService from "@/components/CustomerService.vue";
import {
  fetchAttachmentList,
  saveOrEditAttachment,
} from "@/http/attachment.js";

const pagingRef = ref(null);
// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
const dataList = ref([]);
const tabIndex = ref(0);
const title = ref("相关文件");
const tabList = ref(["最近上传", "销售物料", "客户资料"]);
// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`,
  };
  // #endif
  return {};
});
// 【顶部导航栏】左侧按钮点击时触发
const handerLeftBack = () => {
  uni.switchTab({
    url: '/pages/tabBar/more/more',
  });
};
// tab 切换事件处理方法
const tabsChange = (index) => {
  tabIndex.value = index;
  // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
  pagingRef.value.reload();
};

// 初始化文件列表
const queryFileList = async (pageNo, pageSize) => {
  try {
    // 获取最近上传列表数据 在获取数据后添加过滤逻辑
    if (tabIndex.value === 0) {
      const res = await fetchAttachmentList({
        page: pageNo || 1,
        limit: pageSize || 30,
        types: tabIndex.value + 1,
        type:'file',
      });
      console.log("最近上传---:", res);
      let pageData = res.data.list || [];
      // 递归过滤函数
      const filterDirs = (data) => {
        return data.reduce((acc, item) => {
          // 如果是目录类型
          if (item.type === 'dir') {
            // 只处理子节点
            if (item.children && item.children.length) {
              // 递归过滤子节点并展开到当前层级
              acc.push(...filterDirs(item.children));
            }
          } 
          // 非目录类型
          else {
            // 克隆对象避免污染原数据
            const newItem = { ...item };
            // 递归处理子节点
            if (newItem.children) {
              newItem.children = filterDirs(newItem.children);
            }
            acc.push(newItem);
          }
          return acc;
        }, []);
      };
      // 应用过滤
      pageData = filterDirs(pageData);
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
    // 获取销售物料列表数据
    if (tabIndex.value === 1) {
      const res = await fetchAttachmentList({
        classify: 3,
        page: pageNo || 1,
        limit: pageSize || 30,
        // type: tabIndex.value + 1,
      });
      console.log("销售物料----------:", res);
      const pageData = res.data.list || [];
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
    // 获取项目资料文件列表数据
    if (tabIndex.value === 2) {
      const res = await fetchAttachmentList({
        // 1 客户文件，2 商机文件，3 销售物料，4 客户资料
        classify: 4,
        page: pageNo || 1,
        limit: pageSize || 30,
      });
      console.log("客户资料:", res);
      const pageData = res.data.list || [];
      // 传递给分页组件
      pagingRef.value.complete(pageData);
    }
  } catch (err) {
    console.error("客户请求失败:", err);
    // 请求失败时也要调用complete，传入空数组
    pagingRef.value.complete([]);
  }
};

// 刷新当前页面数据的方法
const refreshCurrentPage = () => {
  // 使用refresh方法刷新数据，保持当前页码不变
  pagingRef.value.refresh();
};

onMounted(() => {});
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/public-layout.scss';
</style>
