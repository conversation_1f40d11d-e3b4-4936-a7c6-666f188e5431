<template>
  <view class="debug-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">用户日志调试</text>
      <text class="page-subtitle">CID & 系统信息</text>
    </view>

    <!-- 基础信息卡片 -->
    <view class="info-card">
      <text class="card-title">推送客户端信息</text>
      <view class="info-item">
        <text class="info-label">推送CID:</text>
        <text class="info-value">{{ pushClientId || '获取中...' }}</text>
        <button @click="copyToClipboard(pushClientId)" class="copy-btn" size="mini" type="primary">复制</button>
      </view>
      <view class="info-item">
        <text class="info-label">获取状态:</text>
        <text class="info-value" :class="pushClientId ? 'status-success' : 'status-pending'">
          {{ pushClientId ? '已获取' : '获取中' }}
        </text>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="info-card">
      <text class="card-title">用户信息</text>
      <view class="info-item">
        <text class="info-label">用户ID:</text>
        <text class="info-value">{{ userInfo.user_id || '-' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户名:</text>
        <text class="info-value">{{ userInfo.name || '-' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">手机号:</text>
        <text class="info-value">{{ userInfo.mobile || '-' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">登录状态:</text>
        <text class="info-value" :class="isLoggedIn ? 'status-success' : 'status-error'">
          {{ isLoggedIn ? '已登录' : '未登录' }}
        </text>
      </view>
    </view>

    <!-- 系统信息卡片 -->
    <view class="info-card">
      <text class="card-title">系统信息</text>
      <view class="info-item">
        <text class="info-label">平台:</text>
        <text class="info-value">{{ systemInfo.platform || '-' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">应用版本:</text>
        <text class="info-value">{{ systemInfo.appVersion || '-' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">系统版本:</text>
        <text class="info-value">{{ systemInfo.system || '-' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">设备型号:</text>
        <text class="info-value">{{ systemInfo.model || '-' }}</text>
      </view>
    </view>

    <!-- 存储信息卡片 -->
    <view class="info-card">
      <text class="card-title">本地存储</text>
      <view class="info-item">
        <text class="info-label">Token:</text>
        <text class="info-value">{{ storageInfo.token ? '已存储' : '未存储' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户信息:</text>
        <text class="info-value">{{ storageInfo.userInfo ? '已存储' : '未存储' }}</text>
      </view>
    </view>

    <!-- 调试日志 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">调试日志</text>
        <button @click="clearLogs" class="clear-btn" size="mini" type="warn">清空日志</button>
      </view>
      <scroll-view class="log-container" scroll-y="true">
        <view v-for="(log, index) in debugLogs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content" :class="log.type">{{ log.message }}</text>
        </view>
        <view v-if="debugLogs.length === 0" class="no-logs">
          <text>暂无日志</text>
        </view>
      </scroll-view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button @click="refreshData" class="action-btn" type="primary">刷新数据</button>
      <button @click="exportDebugInfo" class="action-btn" type="default">导出信息</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { getUserInfo } from '@/http/user'

/**
 * 响应式数据定义
 */
const pushClientId = ref('')
const userInfo = reactive({
  user_id: '',
  name: '',
  mobile: '',
  portrait: ''
})
const systemInfo = reactive({
  platform: '',
  appVersion: '',
  system: '',
  model: ''
})
const storageInfo = reactive({
  token: false,
  userInfo: false
})
const debugLogs = ref([])
const isLoggedIn = ref(false)

/**
 * 添加调试日志
 * @param {string} message - 日志消息
 * @param {string} type - 日志类型：info, success, error, warn
 */
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  debugLogs.value.unshift({
    time,
    message,
    type
  })
  
  // 限制日志数量，最多保留100条
  if (debugLogs.value.length > 100) {
    debugLogs.value = debugLogs.value.slice(0, 100)
  }
}

/**
 * 获取推送客户端ID
 */
const getPushClientId = () => {
  addLog('开始获取推送客户端ID...', 'info')
  
  // 先尝试从存储中获取
  const storedCid = uni.getStorageSync('pushClientId')
  if (storedCid) {
    pushClientId.value = storedCid
    addLog(`从存储获取CID: ${storedCid}`, 'success')
    return
  }

  // 如果存储中没有，则重新获取
  // #ifdef APP-PLUS
  uni.getPushClientId({
    success: (res) => {
      pushClientId.value = res.cid
      uni.setStorageSync('pushClientId', res.cid)
      addLog(`获取CID成功: ${res.cid}`, 'success')
    },
    fail: (err) => {
      addLog(`获取CID失败: ${JSON.stringify(err)}`, 'error')
    }
  })
  // #endif
  
  // #ifdef H5 || MP
  addLog('当前平台不支持获取推送CID', 'warn')
  // #endif
}

/**
 * 获取用户信息
 */
const fetchUserInfo = async () => {
  try {
    addLog('开始获取用户信息...', 'info')
    const res = await getUserInfo()
    
    if (res.code === 0 && res.data) {
      Object.assign(userInfo, res.data)
      isLoggedIn.value = true
      addLog('获取用户信息成功', 'success')
    } else {
      addLog(`获取用户信息失败: ${res.message || '未知错误'}`, 'error')
      isLoggedIn.value = false
    }
  } catch (error) {
    addLog(`获取用户信息异常: ${error.message}`, 'error')
    isLoggedIn.value = false
  }
}

/**
 * 获取系统信息
 */
const getSystemInfo = () => {
  try {
    addLog('开始获取系统信息...', 'info')
    const sysInfo = uni.getSystemInfoSync()
    
    Object.assign(systemInfo, {
      platform: sysInfo.platform,
      appVersion: sysInfo.appVersion,
      system: sysInfo.system,
      model: sysInfo.model
    })
    
    addLog('获取系统信息成功', 'success')
  } catch (error) {
    addLog(`获取系统信息失败: ${error.message}`, 'error')
  }
}

/**
 * 获取存储信息
 */
const getStorageInfo = () => {
  try {
    addLog('检查本地存储...', 'info')
    
    const token = uni.getStorageSync('token')
    const userData = uni.getStorageSync('userInfo')
    
    storageInfo.token = !!token
    storageInfo.userInfo = !!userData
    
    addLog(`存储检查完成 - Token: ${storageInfo.token ? '存在' : '不存在'}, 用户信息: ${storageInfo.userInfo ? '存在' : '不存在'}`, 'info')
  } catch (error) {
    addLog(`检查存储失败: ${error.message}`, 'error')
  }
}

/**
 * 复制到剪贴板
 * @param {string} text - 要复制的文本
 */
const copyToClipboard = (text) => {
  if (!text) {
    uni.showToast({
      title: '没有可复制的内容',
      icon: 'none'
    })
    return
  }

  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
      addLog(`复制CID到剪贴板: ${text}`, 'success')
    },
    fail: (err) => {
      addLog(`复制失败: ${JSON.stringify(err)}`, 'error')
    }
  })
}

/**
 * 清空日志
 */
const clearLogs = () => {
  debugLogs.value = []
  addLog('日志已清空', 'info')
}

/**
 * 刷新所有数据
 */
const refreshData = () => {
  addLog('======== 开始刷新数据 ========', 'info')
  getPushClientId()
  fetchUserInfo()
  getSystemInfo()
  getStorageInfo()
  addLog('======== 数据刷新完成 ========', 'info')
}

/**
 * 导出调试信息
 */
const exportDebugInfo = () => {
  const debugInfo = {
    timestamp: new Date().toISOString(),
    pushClientId: pushClientId.value,
    userInfo: userInfo,
    systemInfo: systemInfo,
    storageInfo: storageInfo,
    logs: debugLogs.value.slice(0, 20) // 只导出最近20条日志
  }
  
  const debugStr = JSON.stringify(debugInfo, null, 2)
  
  // 复制到剪贴板
  uni.setClipboardData({
    data: debugStr,
    success: () => {
      uni.showToast({
        title: '调试信息已复制',
        icon: 'success'
      })
      addLog('调试信息已导出到剪贴板', 'success')
    },
    fail: (err) => {
      addLog(`导出失败: ${JSON.stringify(err)}`, 'error')
    }
  })
}

/**
 * 页面生命周期
 */
onMounted(() => {
  addLog('页面加载完成，开始初始化...', 'info')
  refreshData()
})

onUnmounted(() => {
  // 清理定时器等资源
})
</script>

<style lang="scss" scoped>
.debug-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  
  .page-title {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
  }
  
  .page-subtitle {
    font-size: 14px;
    color: #666;
  }
}

.info-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  .card-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    
    .card-title {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f9f9f9;
  
  &:last-child {
    border-bottom: none;
  }
  
  .info-label {
    flex: 0 0 100px;
    font-size: 14px;
    color: #666;
    font-weight: 500;
  }
  
  .info-value {
    flex: 1;
    font-size: 14px;
    color: #333;
    word-break: break-all;
    margin-right: 8px;
    
    &.status-success {
      color: #52c41a;
      font-weight: 500;
    }
    
    &.status-error {
      color: #f5222d;
      font-weight: 500;
    }
    
    &.status-pending {
      color: #faad14;
      font-weight: 500;
    }
  }
  
  .copy-btn {
    flex: 0 0 auto;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.2;
  }
}

.log-container {
  height: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  background-color: #fafafa;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  .log-time {
    flex: 0 0 60px;
    font-size: 12px;
    color: #999;
    margin-right: 8px;
  }
  
  .log-content {
    flex: 1;
    font-size: 12px;
    word-break: break-all;
    
    &.info {
      color: #666;
    }
    
    &.success {
      color: #52c41a;
    }
    
    &.error {
      color: #f5222d;
    }
    
    &.warn {
      color: #faad14;
    }
  }
}

.no-logs {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  
  .action-btn {
    flex: 1;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}

.clear-btn {
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  line-height: 1.2;
}
</style>
