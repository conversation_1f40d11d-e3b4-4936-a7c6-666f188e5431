/**
 * 全局搜索相关的通用样式
 * 减少重复代码，统一样式规范
 */

// 通用字体族
$font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;

// 通用颜色变量
$primary-color: #333333;
$secondary-color: #787d86;
$text-light: #adb1ba;
$border-color: #e2e4e9;
$divider-color: #f0f0f0;
$success-color: #4caf50;
$error-color: #ff4d4f;
$background-white: #ffffff;

// 通用尺寸
$icon-size-sm: 20px;
$icon-size-md: 24px;
$icon-size-lg: 25px;
$border-radius: 10px;
$spacing-xs: 2px;
$spacing-sm: 5px;
$spacing-md: 8px;
$spacing-lg: 10px;
$spacing-xl: 12px;
$spacing-xxl: 18px; // 新增18px间距变量

// 文本截断 mixin
@mixin text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

// Flex 居中 mixin
@mixin flex-center {
  display: flex;
  align-items: center;
}

// 卡片样式 mixin
@mixin card-style {
  border-radius: $border-radius;
  border: 1px solid $border-color;
  background: $background-white;
}

// 过渡动画 mixin
@mixin slide-transition {
  transition: all 0.3s ease;
}

// 通用搜索容器样式
.search-container-base {
  max-width: 750px;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  font-family: $font-family;
}

// 通用搜索头部样式
.search-header-base {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  @include flex-center;
  height: 60px;
  padding: $spacing-lg 18px;
  gap: 20px;
  background-color: $background-white;
  border-bottom: 1px solid $border-color;
  max-width: 750px;
  margin: 0 auto;

  .search-input {
    flex: 1;
  }
}

// 通用内容区域样式
.search-content-base {
  margin-top: 85px;
  flex: 1;
  overflow-y: auto;

  .loading-state {
    @include flex-center;
    justify-content: center;
    padding: 60px 20px;
    min-height: 200px;
  }
}

// 通用空状态样式
.empty-state-base {
  @include flex-center;
  flex-direction: column;
  justify-content: center;
  height: 70vh;
  font-family: $font-family;

  .empty-image {
    width: 113px;
    height: auto;
    margin-bottom: 20px;
  }

  .empty-title {
    color: #3e4551;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: $spacing-md;
  }

  .empty-description {
    color: $text-light;
    font-size: 12px;
    line-height: 1.5;
  }
}

// 通用结果区域样式
.result-section-base {
  padding: $spacing-lg;
  font-size: 14px;

  .section-header {
    @include flex-center;
    margin-top: $spacing-lg;
    margin-bottom: $spacing-md;
    color: $secondary-color;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;

    .expand-icon {
      width: $icon-size-lg;
      height: $icon-size-lg;
      @include slide-transition;
    }

    .section-title {
      margin-left: $spacing-sm;
    }
  }

  .section-content {
    @include card-style;
  }
}

// 通用结果项样式（已废弃，由各组件自定义）
// 保留作为参考，实际使用中各组件会自定义样式
.result-item-base-deprecated {
  @include flex-center;
  margin: $spacing-lg $spacing-xl $spacing-sm $spacing-xl;
  cursor: pointer;
  min-height: 44px;

  .item-left {
    @include flex-center;
    margin-right: 6px;

    .item-icon {
      width: $icon-size-md;
      height: $icon-size-md;
    }
  }

  .item-content {
    flex: 1;
    // 移除 border-bottom，改用专门的下划线DOM
    padding-bottom: $spacing-xs;
    overflow: hidden;

    .item-title {
      color: $primary-color;
      font-size: 14px;
      @include text-ellipsis;
      margin-bottom: $spacing-xs;
    }

    .item-subtitle {
      @include flex-center;
      color: $text-light;
      font-size: 12px;
      line-height: 16px;

      .time-info {
        @include flex-center;
        margin-right: $spacing-sm;

        .time-text {
          min-width: 95px;
        }

        .overdue-text {
          color: $error-color;
          margin-left: $spacing-sm;
          min-width: 50px;
        }
      }

      .related-info {
        @include text-ellipsis;
      }

      .business-info {
        @include flex-center;
        max-width: 62vw;
        margin-right: $spacing-md;

        .business-time {
          min-width: 95px;
          margin-right: 4px;
        }

        .todo-text {
          @include text-ellipsis;
        }
      }

      .status-text {
        margin-left: auto;
      }
    }
  }

  .item-action {
    @include flex-center;
    margin-left: $spacing-md;
  }
}

// 通用下划线样式
.item-divider-base {
  // width: calc(100% - 36px); // 左右各18px边距
  height: 1px;
  background-color: $divider-color;
  margin: 0 $spacing-xxl $spacing-sm $spacing-xxl;
}

// 通用过渡动画
.slide-fade-enter-active,
.slide-fade-leave-active {
  @include slide-transition;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 响应式设计
@media (max-width: 750px) {
  .search-container-base {
    max-width: 100%;
  }

  .search-header-base {
    max-width: 100%;
  }

  .result-item-base {
    .item-content {
      .item-subtitle {
        .business-info {
          max-width: 60vw;
        }
      }
    }
  }
}

// 深色模式支持（预留）
@media (prefers-color-scheme: dark) {
  .search-container-base {
    // 深色模式样式
  }
}
