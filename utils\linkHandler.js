/**
 * 链接处理工具
 * 用于解析URL并根据平台执行不同的跳转逻辑
 */
import { clearLinkTapStatus } from '@/utils/globalState.js';

// 从pages.json中提取的所有可用路由
const APP_ROUTES = [
  'pages/ai-chat',
  'pages/tabBar/todo/todo',
  'pages/tabBar/statistics/statistics',
  'pages/tabBar/customer/customer',
  'pages/tabBar/business/business',
  'pages/tabBar/more/more',
  'pages/ai-task',
  'pages/ppt-editor/index',
  'pages/error/401',
  'pages/error/404',
  'pages/template/customer-details/index',
  'pages/template/todo-details/index',
  'pages/template/business-detail/index',
  'pages/userInfo/user/user-settings',
  'pages/userInfo/related-documents/index',
  'pages/userInfo/global-search/index',
  'pages/preview/weboffice-preview'
];

// TabBar页面列表
const TAB_BAR_PAGES = [
  'pages/tabBar/statistics/statistics',
  'pages/tabBar/todo/todo',
  'pages/tabBar/customer/customer',
  'pages/tabBar/business/business',
  'pages/tabBar/more/more'
];

/**
 * 兼容的URL解析函数
 * 解决APP端没有URL构造函数的问题
 * @param {string} url - URL字符串
 * @returns {Object} 解析后的URL对象
 */
const parseUrl = (url) => {
  try {
    // #ifdef H5
    // H5端使用原生URL构造函数
    return new URL(url);
    // #endif
    
    // #ifdef APP-PLUS
    // APP端手动解析URL
    const urlPattern = /^(https?:\/\/)([^\/]+)(\/[^?#]*)?(\?[^#]*)?(#.*)?$/;
    const match = url.match(urlPattern);
    
    if (!match) {
      throw new Error('Invalid URL format');
    }
    
    const [, protocol, hostname, pathname = '/', search = '', hash = ''] = match;
    
    return {
      protocol: protocol.slice(0, -2), // 去掉 //
      hostname,
      pathname,
      search,
      hash
    };
    // #endif
    
    // #ifndef H5 || APP-PLUS
    // 其他平台的降级处理
    throw new Error('URL parsing not supported in this environment');
    // #endif
  } catch (error) {
    console.error('URL解析错误:', error);
    throw error;
  }
};

/**
 * 解析URL并提取路由信息
 * @param {string} url - 完整的URL地址
 * @returns {Object|null} 解析结果
 */
const parseRouteFromUrl = (url) => {
  try {
    console.log('🔍 开始解析URL:', url);
    
    const urlObj = parseUrl(url);
    let pathname = urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
    
    console.log('📄 提取的路径:', pathname);
    console.log('🔗 URL参数:', urlObj.search);
    
    const matchedRoute = APP_ROUTES.find(route => route === pathname);
    
    if (matchedRoute) {
      console.log('✅ 路由匹配成功:', matchedRoute);
      return {
        route: matchedRoute,
        fullRoute: `/${matchedRoute}${urlObj.search}`,
        isTabBarPage: TAB_BAR_PAGES.includes(matchedRoute),
        isInternalRoute: true
      };
    }
    
    console.warn('⚠️ 未找到匹配的路由:', pathname);
    return { isInternalRoute: false, originalUrl: url };
  } catch (error) {
    console.error('❌ URL解析失败:', error);
    return null;
  }
};

/**
 * 执行路由跳转
 * @param {Object} routeInfo - 路由信息
 */
const navigateToRoute = (routeInfo) => {
  console.log('🚀 执行路由跳转:', routeInfo);
  if (routeInfo.isTabBarPage) {
    console.log('📱 TabBar页面跳转:', routeInfo.fullRoute);
    clearLinkTapStatus()
    uni.switchTab({
      url: routeInfo.fullRoute,
      success: () => {
        console.log('✅ TabBar页面跳转成功');
      },
      fail: (error) => {
        console.error('❌ TabBar页面跳转失败:', error);
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  } else {
    console.log('📄 普通页面跳转:', routeInfo.fullRoute);
    uni.navigateTo({
      url: routeInfo.fullRoute,
      success: () => {
        console.log('✅ 普通页面跳转成功');
      },
      fail: (error) => {
        console.error('❌ 普通页面跳转失败，尝试reLaunch:', error);
      }
    });
  }
};

/**
 * 统一的链接处理入口
 * @param {string} url - 要处理的URL
 * @returns {Promise<boolean>} 处理是否成功
 */
export const handleLink = async (url) => {
  if (!url || typeof url !== 'string') {
    console.warn('⚠️ 无效的URL:', url);
    return false;
  }
  console.log('🎯 开始处理链接:', url);
  try {
    // 判断链接类型
    if (url.startsWith('#')) {
      console.log('🔗 锚点链接，暂不处理');
      return false;
    }

    // #ifdef H5
    // H5端：直接打开所有链接
    console.log('🌐 H5端直接打开链接');
    window.open(url, '_blank');
    return true;
    // #endif

    // #ifdef APP-PLUS
    // APP端：进行路由匹配和跳转
    console.log('📱 APP端开始路由匹配');
    if (!url.includes('://')) {
      // 内部相对路径链接
      console.log('📱 内部相对路径链接');
      const cleanUrl = url.replace(/^\/+/, ''); // 去掉开头的斜杠
      navigateToRoute({ 
        fullRoute: url, 
        isTabBarPage: TAB_BAR_PAGES.includes(cleanUrl) 
      });
      return true;
    }
    // 解析完整URL
    const routeInfo = parseRouteFromUrl(url);
    if (!routeInfo) {
      throw new Error('URL解析失败');
    }
    if (routeInfo.isInternalRoute) {
      // 内部路由处理 - APP端应用内跳转
      console.log('📱 APP端应用内跳转:', routeInfo.fullRoute);
      navigateToRoute(routeInfo);
      return true;
    } else {
      // 外部链接处理 - APP端通过浏览器打开
      console.log('📱 APP端通过浏览器打开外部链接:', routeInfo.originalUrl);
      try {
        // 使用系统浏览器打开链接
        plus.runtime.openURL(routeInfo.originalUrl);
        console.log('✅ 外部链接已通过浏览器打开');
        return true;
      } catch (error) {
        console.error('❌ 打开外部链接失败:', error);
        uni.showModal({
          title: '提示',
          content: '无法打开链接，请检查网络连接',
          showCancel: false
        });
        return false;
      }
    }
    // #endif
  } catch (error) {
    console.error('❌ 链接处理失败:', error);
    return false;
  } finally {
    uni.hideLoading();
  }
};
export default { handleLink }; 