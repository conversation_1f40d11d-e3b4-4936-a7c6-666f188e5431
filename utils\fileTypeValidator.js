/**
 * 文件类型验证器
 * 提供统一的文件格式限制和验证功能
 */

/**
 * 预定义的文件类型配置
 */
export const FILE_TYPE_CONFIGS = {
  // 图片文件
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml'],
    maxSize: 10 * 1024 * 1024, // 10MB
    description: '图片文件'
  },
  
  // 文档文件
  document: {
    extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf'],
    mimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'application/rtf'
    ],
    maxSize: 50 * 1024 * 1024, // 50MB
    description: '文档文件'
  },
  
  // 视频文件
  video: {
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'],
    mimeTypes: ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv', 'video/x-matroska', 'video/webm'],
    maxSize: 100 * 1024 * 1024, // 100MB
    description: '视频文件'
  },
  
  // 音频文件
  audio: {
    extensions: ['.mp3', '.wav', '.aac', '.flac', '.ogg'],
    mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/aac', 'audio/flac', 'audio/ogg'],
    maxSize: 20 * 1024 * 1024, // 20MB
    description: '音频文件'
  },
  
  // 压缩文件
  archive: {
    extensions: ['.zip', '.rar', '.7z', '.tar', '.gz'],
    mimeTypes: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip'],
    maxSize: 100 * 1024 * 1024, // 100MB
    description: '压缩文件'
  },
  
  // 客户文件专用配置（更严格的限制）
  customerFile: {
    extensions: [
      // 图片
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
      // 文档
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
      // 压缩包
      '.zip', '.rar'
    ],
    mimeTypes: [
      // 图片
      'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
      // 文档
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      // 压缩包
      'application/zip',
      'application/x-rar-compressed'
    ],
    maxSize: 50 * 1024 * 1024, // 50MB
    description: '客户文件'
  }
};

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 扩展名（包含点号）
 */
export function getFileExtension(fileName) {
  if (!fileName || typeof fileName !== 'string') return '';
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex !== -1 ? fileName.substring(lastDotIndex).toLowerCase() : '';
}

/**
 * 验证单个文件
 * @param {Object} file - 文件对象
 * @param {string|Array} allowedTypes - 允许的文件类型配置名称或自定义扩展名数组
 * @param {Object} options - 额外选项
 * @returns {Object} 验证结果
 */
export function validateFile(file, allowedTypes = 'customerFile', options = {}) {
  const result = {
    valid: false,
    error: null,
    errorType: null,
    file: file
  };

  // 基本文件检查
  if (!file) {
    result.error = '文件不存在';
    result.errorType = 'missing';
    return result;
  }

  if (!file.name) {
    result.error = '文件名不能为空';
    result.errorType = 'name';
    return result;
  }

  // 获取文件配置
  let config;
  if (Array.isArray(allowedTypes)) {
    // 自定义扩展名数组
    config = {
      extensions: allowedTypes,
      mimeTypes: [],
      maxSize: options.maxSize || 50 * 1024 * 1024,
      description: '自定义文件类型'
    };
  } else if (typeof allowedTypes === 'string' && FILE_TYPE_CONFIGS[allowedTypes]) {
    // 预定义配置
    config = FILE_TYPE_CONFIGS[allowedTypes];
  } else {
    result.error = '无效的文件类型配置';
    result.errorType = 'config';
    return result;
  }

  // 文件扩展名检查
  const fileExtension = getFileExtension(file.name);
  if (!fileExtension) {
    result.error = '文件必须有扩展名';
    result.errorType = 'extension';
    return result;
  }

  if (!config.extensions.includes(fileExtension)) {
    result.error = `不支持的文件格式 ${fileExtension}，支持的格式：${config.extensions.join(', ')}`;
    result.errorType = 'extension';
    return result;
  }

  // MIME类型检查（如果有）
  if (file.type && config.mimeTypes.length > 0) {
    if (!config.mimeTypes.includes(file.type)) {
      result.error = `不支持的文件类型 ${file.type}`;
      result.errorType = 'mimetype';
      return result;
    }
  }

  // 文件大小检查
  if (file.size && file.size > config.maxSize) {
    const maxSizeMB = Math.round(config.maxSize / (1024 * 1024));
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    result.error = `文件大小 ${fileSizeMB}MB 超过限制 ${maxSizeMB}MB`;
    result.errorType = 'size';
    return result;
  }

  // 验证通过
  result.valid = true;
  return result;
}

/**
 * 批量验证文件
 * @param {Array} files - 文件数组
 * @param {string|Array} allowedTypes - 允许的文件类型
 * @param {Object} options - 额外选项
 * @returns {Object} 验证结果
 */
export function validateFiles(files, allowedTypes = 'customerFile', options = {}) {
  const results = {
    valid: true,
    validFiles: [],
    invalidFiles: [],
    errors: []
  };

  if (!Array.isArray(files)) {
    results.valid = false;
    results.errors.push('文件列表必须是数组');
    return results;
  }

  for (const file of files) {
    const validation = validateFile(file, allowedTypes, options);
    
    if (validation.valid) {
      results.validFiles.push(file);
    } else {
      results.valid = false;
      results.invalidFiles.push(file);
      results.errors.push({
        fileName: file.name,
        error: validation.error,
        errorType: validation.errorType
      });
    }
  }

  return results;
}

/**
 * 获取用于xe-upload组件的扩展名配置
 * @param {string|Array} allowedTypes - 允许的文件类型
 * @returns {Array} 扩展名数组
 */
export function getExtensionsForUpload(allowedTypes = 'customerFile') {
  if (Array.isArray(allowedTypes)) {
    return allowedTypes;
  }
  
  if (typeof allowedTypes === 'string' && FILE_TYPE_CONFIGS[allowedTypes]) {
    return FILE_TYPE_CONFIGS[allowedTypes].extensions;
  }
  
  return FILE_TYPE_CONFIGS.customerFile.extensions;
}

/**
 * 生成用户友好的文件类型说明
 * @param {string|Array} allowedTypes - 允许的文件类型
 * @returns {string} 说明文本
 */
export function getFileTypeDescription(allowedTypes = 'customerFile') {
  if (Array.isArray(allowedTypes)) {
    return `支持格式：${allowedTypes.join(', ')}`;
  }
  
  if (typeof allowedTypes === 'string' && FILE_TYPE_CONFIGS[allowedTypes]) {
    const config = FILE_TYPE_CONFIGS[allowedTypes];
    const maxSizeMB = Math.round(config.maxSize / (1024 * 1024));
    return `${config.description}，支持格式：${config.extensions.join(', ')}，最大${maxSizeMB}MB`;
  }
  
  return '支持常见文件格式';
}

/**
 * 预验证文件（在上传前检查）
 * @param {Array} files - 文件数组
 * @param {string|Array} allowedTypes - 允许的文件类型
 * @returns {Promise<Object>} 验证结果
 */
export async function preValidateFiles(files, allowedTypes = 'customerFile') {
  return new Promise((resolve) => {
    const validation = validateFiles(files, allowedTypes);
    
    if (!validation.valid) {
      // 显示详细的错误信息
      const errorMessages = validation.errors.map(err => 
        `${err.fileName}: ${err.error}`
      ).join('\n');
      
      uni.showModal({
        title: '文件格式检查',
        content: `以下文件不符合要求：\n${errorMessages}\n\n${getFileTypeDescription(allowedTypes)}`,
        showCancel: false,
        confirmText: '知道了'
      });
    }
    
    resolve(validation);
  });
}
