/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-d31e1c47]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-d31e1c47]:before {
  content: "\e6c4";
}
.uniui-color[data-v-d31e1c47]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-d31e1c47]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-d31e1c47]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-d31e1c47]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-d31e1c47]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-d31e1c47]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-d31e1c47]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-d31e1c47]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-d31e1c47]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-d31e1c47]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-d31e1c47]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-d31e1c47]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-d31e1c47]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-d31e1c47]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-d31e1c47]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-d31e1c47]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-d31e1c47]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-d31e1c47]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-d31e1c47]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-d31e1c47]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-d31e1c47]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-d31e1c47]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-d31e1c47]:before {
  content: "\e6d4";
}
.uniui-back[data-v-d31e1c47]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-d31e1c47]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-d31e1c47]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-d31e1c47]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-d31e1c47]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-d31e1c47]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-d31e1c47]:before {
  content: "\e6d1";
}
.uniui-down[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-d31e1c47]:before {
  content: "\e6d5";
}
.uniui-right[data-v-d31e1c47]:before {
  content: "\e6b5";
}
.uniui-up[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-top[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-left[data-v-d31e1c47]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-d31e1c47]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-d31e1c47]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-d31e1c47]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-d31e1c47]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-d31e1c47]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-d31e1c47]:before {
  content: "\e649";
}
.uniui-reload[data-v-d31e1c47]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-d31e1c47]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-d31e1c47]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-d31e1c47]:before {
  content: "\e6ad";
}
.uniui-location[data-v-d31e1c47]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-d31e1c47]:before {
  content: "\e683";
}
.uniui-star[data-v-d31e1c47]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-d31e1c47]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-d31e1c47]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-d31e1c47]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-d31e1c47]:before {
  content: "\e6a2";
}
.uniui-font[data-v-d31e1c47]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-d31e1c47]:before {
  content: "\e6a4";
}
.uniui-link[data-v-d31e1c47]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-d31e1c47]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-d31e1c47]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-d31e1c47]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-d31e1c47]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-d31e1c47]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-d31e1c47]:before {
  content: "\e6ab";
}
.uniui-person[data-v-d31e1c47]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-d31e1c47]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-d31e1c47]:before {
  content: "\e69b";
}
.uniui-phone[data-v-d31e1c47]:before {
  content: "\e69c";
}
.uniui-email[data-v-d31e1c47]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-d31e1c47]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-d31e1c47]:before {
  content: "\e692";
}
.uniui-contact[data-v-d31e1c47]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-d31e1c47]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-d31e1c47]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-d31e1c47]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-d31e1c47]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-d31e1c47]:before {
  content: "\e68e";
}
.uniui-upload[data-v-d31e1c47]:before {
  content: "\e690";
}
.uniui-weixin[data-v-d31e1c47]:before {
  content: "\e691";
}
.uniui-compose[data-v-d31e1c47]:before {
  content: "\e67f";
}
.uniui-qq[data-v-d31e1c47]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-d31e1c47]:before {
  content: "\e681";
}
.uniui-pyq[data-v-d31e1c47]:before {
  content: "\e682";
}
.uniui-sound[data-v-d31e1c47]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-d31e1c47]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-d31e1c47]:before {
  content: "\e686";
}
.uniui-trash[data-v-d31e1c47]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-d31e1c47]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-d31e1c47]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-d31e1c47]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-d31e1c47]:before {
  content: "\e68c";
}
.uniui-download[data-v-d31e1c47]:before {
  content: "\e68d";
}
.uniui-help[data-v-d31e1c47]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-d31e1c47]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-d31e1c47]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-d31e1c47]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-d31e1c47]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-d31e1c47]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-d31e1c47]:before {
  content: "\e66c";
}
.uniui-clear[data-v-d31e1c47]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-d31e1c47]:before {
  content: "\e66e";
}
.uniui-minus[data-v-d31e1c47]:before {
  content: "\e66f";
}
.uniui-image[data-v-d31e1c47]:before {
  content: "\e670";
}
.uniui-mic[data-v-d31e1c47]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-d31e1c47]:before {
  content: "\e672";
}
.uniui-close[data-v-d31e1c47]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-d31e1c47]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-d31e1c47]:before {
  content: "\e675";
}
.uniui-plus[data-v-d31e1c47]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-d31e1c47]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-d31e1c47]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-d31e1c47]:before {
  content: "\e668";
}
.uniui-info[data-v-d31e1c47]:before {
  content: "\e669";
}
.uniui-locked[data-v-d31e1c47]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-d31e1c47]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-d31e1c47]:before {
  content: "\e659";
}
.uniui-camera[data-v-d31e1c47]:before {
  content: "\e65a";
}
.uniui-circle[data-v-d31e1c47]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-d31e1c47]:before {
  content: "\e65c";
}
.uniui-chat[data-v-d31e1c47]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-d31e1c47]:before {
  content: "\e65e";
}
.uniui-flag[data-v-d31e1c47]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-d31e1c47]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-d31e1c47]:before {
  content: "\e661";
}
.uniui-home[data-v-d31e1c47]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-d31e1c47]:before {
  content: "\e663";
}
.uniui-gear[data-v-d31e1c47]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-d31e1c47]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-d31e1c47]:before {
  content: "\e666";
}
.uniui-map[data-v-d31e1c47]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-d31e1c47]:before {
  content: "\e656";
}
.uniui-refresh[data-v-d31e1c47]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-d31e1c47]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-d31e1c47]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-d31e1c47]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-d31e1c47]:before {
  content: "\e648";
}
.uniui-redo[data-v-d31e1c47]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-d31e1c47]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-d31e1c47]:before {
  content: "\e64c";
}
.uniui-more[data-v-d31e1c47]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-d31e1c47]:before {
  content: "\e64e";
}
.uniui-undo[data-v-d31e1c47]:before {
  content: "\e64f";
}
.uniui-images[data-v-d31e1c47]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-d31e1c47]:before {
  content: "\e652";
}
.uniui-settings[data-v-d31e1c47]:before {
  content: "\e653";
}
.uniui-search[data-v-d31e1c47]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-d31e1c47]:before {
  content: "\e655";
}
.uniui-list[data-v-d31e1c47]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-d31e1c47]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-d31e1c47]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-d31e1c47]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-d31e1c47]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-d31e1c47]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-d31e1c47]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-d31e1c47]:before {
  content: "\e643";
}
.uniui-heart[data-v-d31e1c47]:before {
  content: "\e639";
}
.uniui-loop[data-v-d31e1c47]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-d31e1c47]:before {
  content: "\e632";
}
.uniui-scan[data-v-d31e1c47]:before {
  content: "\e62a";
}
.uniui-bars[data-v-d31e1c47]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-d31e1c47]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-d31e1c47]:before {
  content: "\e62c";
}
.uniui-shop[data-v-d31e1c47]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-d31e1c47]:before {
  content: "\e630";
}
.uniui-cart[data-v-d31e1c47]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-d31e1c47] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-easyinput[data-v-09fd5285] {
  width: 100%;
  flex: 1;
  position: relative;
  text-align: left;
  color: #333;
  font-size: 14px;
}
.uni-easyinput__content[data-v-09fd5285] {
  flex: 1;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  border-color: #fff;
  transition-property: border-color;
  transition-duration: 0.3s;
}
.uni-easyinput__content-input[data-v-09fd5285] {
  width: auto;
  position: relative;
  overflow: hidden;
  flex: 1;
  line-height: 1;
  font-size: 14px;
  height: 35px;
  /*ifdef H5*/
  /*endif*/
}
.uni-easyinput__content-input[data-v-09fd5285] ::-ms-reveal {
  display: none;
}
.uni-easyinput__content-input[data-v-09fd5285] ::-ms-clear {
  display: none;
}
.uni-easyinput__content-input[data-v-09fd5285] ::-o-clear {
  display: none;
}
.uni-easyinput__placeholder-class[data-v-09fd5285] {
  color: #999;
  font-size: 12px;
}
.is-textarea[data-v-09fd5285] {
  align-items: flex-start;
}
.is-textarea-icon[data-v-09fd5285] {
  margin-top: 5px;
}
.uni-easyinput__content-textarea[data-v-09fd5285] {
  position: relative;
  overflow: hidden;
  flex: 1;
  line-height: 1.5;
  font-size: 14px;
  margin: 6px;
  margin-left: 0;
  height: 80px;
  min-height: 80px;
  min-height: 80px;
  width: auto;
}
.input-padding[data-v-09fd5285] {
  padding-left: 10px;
}
.content-clear-icon[data-v-09fd5285] {
  padding: 0 5px;
}
.label-icon[data-v-09fd5285] {
  margin-right: 5px;
  margin-top: -1px;
}
.is-input-border[data-v-09fd5285] {
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.uni-error-message[data-v-09fd5285] {
  position: absolute;
  bottom: -17px;
  left: 0;
  line-height: 12px;
  color: #e43d33;
  font-size: 12px;
  text-align: left;
}
.uni-error-msg--boeder[data-v-09fd5285] {
  position: relative;
  bottom: 0;
  line-height: 22px;
}
.is-input-error-border[data-v-09fd5285] {
  border-color: #e43d33;
}
.is-input-error-border .uni-easyinput__placeholder-class[data-v-09fd5285] {
  color: #f29e99;
}
.uni-easyinput--border[data-v-09fd5285] {
  margin-bottom: 0;
  padding: 10px 15px;
  border-top: 1px #eee solid;
}
.uni-easyinput-error[data-v-09fd5285] {
  padding-bottom: 0;
}
.is-first-border[data-v-09fd5285] {
  border: none;
}
.is-disabled[data-v-09fd5285] {
  background-color: #f7f6f6;
  color: #d5d5d5;
}
.is-disabled .uni-easyinput__placeholder-class[data-v-09fd5285] {
  color: #d5d5d5;
  font-size: 12px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #666666;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0deg);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(90deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(270deg);
}
100% {
    transform: rotate(360deg);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局搜索相关的通用样式
 * 减少重复代码，统一样式规范
 */
.search-container-base[data-v-53ac2552] {
  max-width: 750px;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.search-header-base[data-v-53ac2552] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 10px 18px;
  gap: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e2e4e9;
  max-width: 750px;
  margin: 0 auto;
}
.search-header-base .search-input[data-v-53ac2552] {
  flex: 1;
}
.search-content-base[data-v-53ac2552] {
  margin-top: 85px;
  flex: 1;
  overflow-y: auto;
}
.search-content-base .loading-state[data-v-53ac2552] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;
}
.empty-state-base[data-v-53ac2552], .empty-state[data-v-53ac2552] {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 70vh;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.empty-state-base .empty-image[data-v-53ac2552], .empty-state .empty-image[data-v-53ac2552] {
  width: 113px;
  height: auto;
  margin-bottom: 20px;
}
.empty-state-base .empty-title[data-v-53ac2552], .empty-state .empty-title[data-v-53ac2552] {
  color: #3e4551;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 8px;
}
.empty-state-base .empty-description[data-v-53ac2552], .empty-state .empty-description[data-v-53ac2552] {
  color: #adb1ba;
  font-size: 12px;
  line-height: 1.5;
}
.result-section-base[data-v-53ac2552] {
  padding: 10px;
  font-size: 14px;
}
.result-section-base .section-header[data-v-53ac2552] {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 8px;
  color: #787d86;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}
.result-section-base .section-header .expand-icon[data-v-53ac2552] {
  width: 25px;
  height: 25px;
  transition: all 0.3s ease;
}
.result-section-base .section-header .section-title[data-v-53ac2552] {
  margin-left: 5px;
}
.result-section-base .section-content[data-v-53ac2552] {
  border-radius: 10px;
  border: 1px solid #e2e4e9;
  background: #ffffff;
}
.result-item-base-deprecated[data-v-53ac2552] {
  display: flex;
  align-items: center;
  margin: 10px 12px 5px 12px;
  cursor: pointer;
  min-height: 44px;
}
.result-item-base-deprecated .item-left[data-v-53ac2552] {
  display: flex;
  align-items: center;
  margin-right: 6px;
}
.result-item-base-deprecated .item-left .item-icon[data-v-53ac2552] {
  width: 24px;
  height: 24px;
}
.result-item-base-deprecated .item-content[data-v-53ac2552] {
  flex: 1;
  padding-bottom: 2px;
  overflow: hidden;
}
.result-item-base-deprecated .item-content .item-title[data-v-53ac2552] {
  color: #333333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}
.result-item-base-deprecated .item-content .item-subtitle[data-v-53ac2552] {
  display: flex;
  align-items: center;
  color: #adb1ba;
  font-size: 12px;
  line-height: 16px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info[data-v-53ac2552] {
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .time-text[data-v-53ac2552] {
  min-width: 95px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .overdue-text[data-v-53ac2552] {
  color: #ff4d4f;
  margin-left: 5px;
  min-width: 50px;
}
.result-item-base-deprecated .item-content .item-subtitle .related-info[data-v-53ac2552] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info[data-v-53ac2552] {
  display: flex;
  align-items: center;
  max-width: 62vw;
  margin-right: 8px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .business-time[data-v-53ac2552] {
  min-width: 95px;
  margin-right: 4px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .todo-text[data-v-53ac2552] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .status-text[data-v-53ac2552] {
  margin-left: auto;
}
.result-item-base-deprecated .item-action[data-v-53ac2552] {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.item-divider-base[data-v-53ac2552] {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 18px 5px 18px;
}
.slide-fade-enter-active[data-v-53ac2552],
.slide-fade-leave-active[data-v-53ac2552] {
  transition: all 0.3s ease;
}
.slide-fade-enter-from[data-v-53ac2552],
.slide-fade-leave-to[data-v-53ac2552] {
  opacity: 0;
  transform: translateY(-10px);
}
@media (max-width: 750px) {
.search-container-base[data-v-53ac2552] {
    max-width: 100%;
}
.search-header-base[data-v-53ac2552] {
    max-width: 100%;
}
.result-item-base .item-content .item-subtitle .business-info[data-v-53ac2552] {
    max-width: 60vw;
}
}
/* 空状态 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局搜索相关的通用样式
 * 减少重复代码，统一样式规范
 */
.search-container-base[data-v-4e2d2d31] {
  max-width: 750px;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.search-header-base[data-v-4e2d2d31] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 10px 18px;
  gap: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e2e4e9;
  max-width: 750px;
  margin: 0 auto;
}
.search-header-base .search-input[data-v-4e2d2d31] {
  flex: 1;
}
.search-content-base[data-v-4e2d2d31] {
  margin-top: 85px;
  flex: 1;
  overflow-y: auto;
}
.search-content-base .loading-state[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;
}
.empty-state-base[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 70vh;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.empty-state-base .empty-image[data-v-4e2d2d31] {
  width: 113px;
  height: auto;
  margin-bottom: 20px;
}
.empty-state-base .empty-title[data-v-4e2d2d31] {
  color: #3e4551;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 8px;
}
.empty-state-base .empty-description[data-v-4e2d2d31] {
  color: #adb1ba;
  font-size: 12px;
  line-height: 1.5;
}
.result-section-base[data-v-4e2d2d31] {
  padding: 10px;
  font-size: 14px;
}
.result-section-base .section-header[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 8px;
  color: #787d86;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}
.result-section-base .section-header .expand-icon[data-v-4e2d2d31] {
  width: 25px;
  height: 25px;
  transition: all 0.3s ease;
}
.result-section-base .section-header .section-title[data-v-4e2d2d31] {
  margin-left: 5px;
}
.result-section-base .section-content[data-v-4e2d2d31] {
  border-radius: 10px;
  border: 1px solid #e2e4e9;
  background: #ffffff;
}
.result-item-base-deprecated[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin: 10px 12px 5px 12px;
  cursor: pointer;
  min-height: 44px;
}
.result-item-base-deprecated .item-left[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-right: 6px;
}
.result-item-base-deprecated .item-left .item-icon[data-v-4e2d2d31] {
  width: 24px;
  height: 24px;
}
.result-item-base-deprecated .item-content[data-v-4e2d2d31] {
  flex: 1;
  padding-bottom: 2px;
  overflow: hidden;
}
.result-item-base-deprecated .item-content .item-title[data-v-4e2d2d31] {
  color: #333333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}
.result-item-base-deprecated .item-content .item-subtitle[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  color: #adb1ba;
  font-size: 12px;
  line-height: 16px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .time-text[data-v-4e2d2d31] {
  min-width: 95px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .overdue-text[data-v-4e2d2d31] {
  color: #ff4d4f;
  margin-left: 5px;
  min-width: 50px;
}
.result-item-base-deprecated .item-content .item-subtitle .related-info[data-v-4e2d2d31] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  max-width: 62vw;
  margin-right: 8px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .business-time[data-v-4e2d2d31] {
  min-width: 95px;
  margin-right: 4px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .todo-text[data-v-4e2d2d31] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .status-text[data-v-4e2d2d31] {
  margin-left: auto;
}
.result-item-base-deprecated .item-action[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.item-divider-base[data-v-4e2d2d31] {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 18px 5px 18px;
}
.slide-fade-enter-active[data-v-4e2d2d31],
.slide-fade-leave-active[data-v-4e2d2d31] {
  transition: all 0.3s ease;
}
.slide-fade-enter-from[data-v-4e2d2d31],
.slide-fade-leave-to[data-v-4e2d2d31] {
  opacity: 0;
  transform: translateY(-10px);
}
@media (max-width: 750px) {
.search-container-base[data-v-4e2d2d31] {
    max-width: 100%;
}
.search-header-base[data-v-4e2d2d31] {
    max-width: 100%;
}
.result-item-base .item-content .item-subtitle .business-info[data-v-4e2d2d31] {
    max-width: 60vw;
}
}
.result-item[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
  min-height: 44px;
  margin: 0 8px;
}
.result-item .item-main[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 10px 18px 2px 18px;
}
.result-item .item-main .item-left[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-right: 6px;
}
.result-item .item-main .item-left .item-icon[data-v-4e2d2d31] {
  width: 24px;
  height: 24px;
}
.result-item .item-main .item-content[data-v-4e2d2d31] {
  flex: 1;
  padding-bottom: 2px;
  overflow: hidden;
}
.result-item .item-main .item-content .item-title[data-v-4e2d2d31] {
  color: #333333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}
.result-item .item-main .item-content .item-subtitle[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  color: #adb1ba;
  font-size: 12px;
  line-height: 16px;
}
.result-item .item-main .item-content .item-subtitle .time-info[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.result-item .item-main .item-content .item-subtitle .time-info .time-text[data-v-4e2d2d31] {
  min-width: 95px;
}
.result-item .item-main .item-content .item-subtitle .time-info .overdue-text[data-v-4e2d2d31] {
  color: #ff4d4f;
  margin-left: 5px;
  min-width: 50px;
}
.result-item .item-main .item-content .item-subtitle .notify-info[data-v-4e2d2d31] {
  margin-right: 5px;
}
.result-item .item-main .item-content .item-subtitle .related-info[data-v-4e2d2d31] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item .item-main .item-content .item-subtitle .contact-name[data-v-4e2d2d31] {
  margin-right: 8px;
}
.result-item .item-main .item-content .item-subtitle .business-info[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  max-width: 62vw;
  margin-right: 8px;
}
.result-item .item-main .item-content .item-subtitle .business-info .business-time[data-v-4e2d2d31] {
  min-width: 95px;
  margin-right: 4px;
}
.result-item .item-main .item-content .item-subtitle .business-info .todo-text[data-v-4e2d2d31] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item .item-main .item-content .item-subtitle .status-text[data-v-4e2d2d31] {
  margin-left: auto;
}
.result-item .item-main .item-content .item-subtitle .file-time[data-v-4e2d2d31] {
  color: #adb1ba;
}
.result-item .item-main .item-action[data-v-4e2d2d31] {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.result-item .item-divider[data-v-4e2d2d31] {
  width: calc(100% - 18px);
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 0 0 35px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标过渡动画 */
.icon-down[data-v-6241e859] {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
/* 列表过渡动画 */
.slide-fade-enter-active[data-v-6241e859] {
  transition: opacity 0.2s ease-out, transform 0.25s ease-out;
}
.slide-fade-leave-active[data-v-6241e859] {
  transition: opacity 0.15s ease-in, transform 0.2s ease-in;
}
.slide-fade-enter-from[data-v-6241e859],
.slide-fade-leave-to[data-v-6241e859] {
  opacity: 0;
  transform: translateY(-10px);
}
.public-icon-img[data-v-6241e859] {
  width: 25px;
  height: 25px;
}
.s-p-b[data-v-6241e859] {
  justify-content: space-between;
}
.section-box[data-v-6241e859] {
  display: flex;
  align-items: center;
  margin: 15px 5px 15px 5px;
  padding: 0px 10px;
}
.section-box .section-left[data-v-6241e859] {
  display: flex;
  align-items: center;
}
.section-box .section-left .icon-down[data-v-6241e859] {
  width: 25px;
  height: 25px;
}
.section-box .section-title[data-v-6241e859] {
  color: var(---, #787d86);
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
/* 分割线 */
.item-divider[data-v-6241e859] {
  height: 1px;
  background-color: #f0f0f0;
  margin-left: 44px;
  /* 14px 左外边距 + 24px 缩略图 + 6px 间距 */
  margin-right: 16px;
}
/**
 * 全局搜索相关的通用样式
 * 减少重复代码，统一样式规范
 */
.search-container-base[data-v-6241e859] {
  max-width: 750px;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.search-header-base[data-v-6241e859] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 10px 18px;
  gap: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e2e4e9;
  max-width: 750px;
  margin: 0 auto;
}
.search-header-base .search-input[data-v-6241e859] {
  flex: 1;
}
.search-content-base[data-v-6241e859] {
  margin-top: 85px;
  flex: 1;
  overflow-y: auto;
}
.search-content-base .loading-state[data-v-6241e859] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;
}
.empty-state-base[data-v-6241e859] {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 70vh;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.empty-state-base .empty-image[data-v-6241e859] {
  width: 113px;
  height: auto;
  margin-bottom: 20px;
}
.empty-state-base .empty-title[data-v-6241e859] {
  color: #3e4551;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 8px;
}
.empty-state-base .empty-description[data-v-6241e859] {
  color: #adb1ba;
  font-size: 12px;
  line-height: 1.5;
}
.result-section-base[data-v-6241e859], .result-section[data-v-6241e859] {
  padding: 10px;
  font-size: 14px;
}
.result-section-base .section-header[data-v-6241e859], .result-section .section-header[data-v-6241e859] {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 8px;
  color: #787d86;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}
.result-section-base .section-header .expand-icon[data-v-6241e859], .result-section .section-header .expand-icon[data-v-6241e859] {
  width: 25px;
  height: 25px;
  transition: all 0.3s ease;
}
.result-section-base .section-header .section-title[data-v-6241e859], .result-section .section-header .section-title[data-v-6241e859] {
  margin-left: 5px;
}
.result-section-base .section-content[data-v-6241e859], .result-section .section-content[data-v-6241e859] {
  border-radius: 10px;
  border: 1px solid #e2e4e9;
  background: #ffffff;
}
.result-item-base-deprecated[data-v-6241e859] {
  display: flex;
  align-items: center;
  margin: 10px 12px 5px 12px;
  cursor: pointer;
  min-height: 44px;
}
.result-item-base-deprecated .item-left[data-v-6241e859] {
  display: flex;
  align-items: center;
  margin-right: 6px;
}
.result-item-base-deprecated .item-left .item-icon[data-v-6241e859] {
  width: 24px;
  height: 24px;
}
.result-item-base-deprecated .item-content[data-v-6241e859] {
  flex: 1;
  padding-bottom: 2px;
  overflow: hidden;
}
.result-item-base-deprecated .item-content .item-title[data-v-6241e859] {
  color: #333333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}
.result-item-base-deprecated .item-content .item-subtitle[data-v-6241e859] {
  display: flex;
  align-items: center;
  color: #adb1ba;
  font-size: 12px;
  line-height: 16px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info[data-v-6241e859] {
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .time-text[data-v-6241e859] {
  min-width: 95px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .overdue-text[data-v-6241e859] {
  color: #ff4d4f;
  margin-left: 5px;
  min-width: 50px;
}
.result-item-base-deprecated .item-content .item-subtitle .related-info[data-v-6241e859] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info[data-v-6241e859] {
  display: flex;
  align-items: center;
  max-width: 62vw;
  margin-right: 8px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .business-time[data-v-6241e859] {
  min-width: 95px;
  margin-right: 4px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .todo-text[data-v-6241e859] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .status-text[data-v-6241e859] {
  margin-left: auto;
}
.result-item-base-deprecated .item-action[data-v-6241e859] {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.item-divider-base[data-v-6241e859] {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 18px 5px 18px;
}
.slide-fade-enter-active[data-v-6241e859],
.slide-fade-leave-active[data-v-6241e859] {
  transition: all 0.3s ease;
}
.slide-fade-enter-from[data-v-6241e859],
.slide-fade-leave-to[data-v-6241e859] {
  opacity: 0;
  transform: translateY(-10px);
}
@media (max-width: 750px) {
.search-container-base[data-v-6241e859] {
    max-width: 100%;
}
.search-header-base[data-v-6241e859] {
    max-width: 100%;
}
.result-item-base .item-content .item-subtitle .business-info[data-v-6241e859] {
    max-width: 60vw;
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.customer-service[data-v-fec1fb0d] {
  position: fixed;
  z-index: 999;
  border-radius: 50%;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}
.customer-service.is-dragging[data-v-fec1fb0d] {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}
.customer-service.is-dragging .customer-icon[data-v-fec1fb0d] {
  opacity: 0.9;
}
.customer-service.is-pc[data-v-fec1fb0d]:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.2);
}
.customer-service .customer-icon[data-v-fec1fb0d] {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.customer-service .drag-indicator[data-v-fec1fb0d] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: pulse-fec1fb0d 1s infinite;
  pointer-events: none;
}
@keyframes pulse-fec1fb0d {
0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}
100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
}
}
@media (max-width: 768px) {
.customer-service[data-v-fec1fb0d]:active {
    transform: scale(0.95);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 图标过渡动画 */
.icon-down[data-v-b2d15cde] {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
/* 列表过渡动画 */
.slide-fade-enter-active[data-v-b2d15cde] {
  transition: opacity 0.2s ease-out, transform 0.25s ease-out;
}
.slide-fade-leave-active[data-v-b2d15cde] {
  transition: opacity 0.15s ease-in, transform 0.2s ease-in;
}
.slide-fade-enter-from[data-v-b2d15cde],
.slide-fade-leave-to[data-v-b2d15cde] {
  opacity: 0;
  transform: translateY(-10px);
}
.public-icon-img[data-v-b2d15cde] {
  width: 25px;
  height: 25px;
}
.s-p-b[data-v-b2d15cde] {
  justify-content: space-between;
}
.section-box[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  margin: 15px 5px 15px 5px;
  padding: 0px 10px;
}
.section-box .section-left[data-v-b2d15cde] {
  display: flex;
  align-items: center;
}
.section-box .section-left .icon-down[data-v-b2d15cde] {
  width: 25px;
  height: 25px;
}
.section-box .section-title[data-v-b2d15cde] {
  color: var(---, #787d86);
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
/* 分割线 */
.item-divider[data-v-b2d15cde] {
  height: 1px;
  background-color: #f0f0f0;
  margin-left: 44px;
  /* 14px 左外边距 + 24px 缩略图 + 6px 间距 */
  margin-right: 16px;
}
/**
 * 全局搜索相关的通用样式
 * 减少重复代码，统一样式规范
 */
.search-container-base[data-v-b2d15cde], .search-container[data-v-b2d15cde] {
  max-width: 750px;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.search-header-base[data-v-b2d15cde], .search-container .search-header[data-v-b2d15cde] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 10px 18px;
  gap: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e2e4e9;
  max-width: 750px;
  margin: 0 auto;
}
.search-header-base .search-input[data-v-b2d15cde], .search-container .search-header .search-input[data-v-b2d15cde] {
  flex: 1;
}
.search-content-base[data-v-b2d15cde], .search-container .search-content[data-v-b2d15cde] {
  margin-top: 85px;
  flex: 1;
  overflow-y: auto;
}
.search-content-base .loading-state[data-v-b2d15cde], .search-container .search-content .loading-state[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;
}
.empty-state-base[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 70vh;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.empty-state-base .empty-image[data-v-b2d15cde] {
  width: 113px;
  height: auto;
  margin-bottom: 20px;
}
.empty-state-base .empty-title[data-v-b2d15cde] {
  color: #3e4551;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 8px;
}
.empty-state-base .empty-description[data-v-b2d15cde] {
  color: #adb1ba;
  font-size: 12px;
  line-height: 1.5;
}
.result-section-base[data-v-b2d15cde] {
  padding: 10px;
  font-size: 14px;
}
.result-section-base .section-header[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 8px;
  color: #787d86;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}
.result-section-base .section-header .expand-icon[data-v-b2d15cde] {
  width: 25px;
  height: 25px;
  transition: all 0.3s ease;
}
.result-section-base .section-header .section-title[data-v-b2d15cde] {
  margin-left: 5px;
}
.result-section-base .section-content[data-v-b2d15cde] {
  border-radius: 10px;
  border: 1px solid #e2e4e9;
  background: #ffffff;
}
.result-item-base-deprecated[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  margin: 10px 12px 5px 12px;
  cursor: pointer;
  min-height: 44px;
}
.result-item-base-deprecated .item-left[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  margin-right: 6px;
}
.result-item-base-deprecated .item-left .item-icon[data-v-b2d15cde] {
  width: 24px;
  height: 24px;
}
.result-item-base-deprecated .item-content[data-v-b2d15cde] {
  flex: 1;
  padding-bottom: 2px;
  overflow: hidden;
}
.result-item-base-deprecated .item-content .item-title[data-v-b2d15cde] {
  color: #333333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  margin-bottom: 2px;
}
.result-item-base-deprecated .item-content .item-subtitle[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  color: #adb1ba;
  font-size: 12px;
  line-height: 16px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .time-text[data-v-b2d15cde] {
  min-width: 95px;
}
.result-item-base-deprecated .item-content .item-subtitle .time-info .overdue-text[data-v-b2d15cde] {
  color: #ff4d4f;
  margin-left: 5px;
  min-width: 50px;
}
.result-item-base-deprecated .item-content .item-subtitle .related-info[data-v-b2d15cde] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  max-width: 62vw;
  margin-right: 8px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .business-time[data-v-b2d15cde] {
  min-width: 95px;
  margin-right: 4px;
}
.result-item-base-deprecated .item-content .item-subtitle .business-info .todo-text[data-v-b2d15cde] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.result-item-base-deprecated .item-content .item-subtitle .status-text[data-v-b2d15cde] {
  margin-left: auto;
}
.result-item-base-deprecated .item-action[data-v-b2d15cde] {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.item-divider-base[data-v-b2d15cde] {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 18px 5px 18px;
}
.slide-fade-enter-active[data-v-b2d15cde],
.slide-fade-leave-active[data-v-b2d15cde] {
  transition: all 0.3s ease;
}
.slide-fade-enter-from[data-v-b2d15cde],
.slide-fade-leave-to[data-v-b2d15cde] {
  opacity: 0;
  transform: translateY(-10px);
}
@media (max-width: 750px) {
.search-container-base[data-v-b2d15cde], .search-container[data-v-b2d15cde] {
    max-width: 100%;
}
.search-header-base[data-v-b2d15cde], .search-container .search-header[data-v-b2d15cde] {
    max-width: 100%;
}
.result-item-base .item-content .item-subtitle .business-info[data-v-b2d15cde] {
    max-width: 60vw;
}
}
.search-container[data-v-b2d15cde] {
  /* 搜索头部 */
  /* 内容区域 */
}