<template>
  <!-- 客户详情模块 -->
  <view class="customer-details-container">
    <!-- 公司信息 -->
    <view class="section-box">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">公司信息</view>
      </view>
    </view>
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
        <uni-forms
          class="task-form"
          :modelValue="editFormData"
          border
          label-align="center"
        >
          <uni-forms-item>
            <template #label>
              <view class="task-label">公司简称</view>
            </template>
            <view class="item-box">
              <uni-easyinput
                trim="all"
                v-model="editFormData.company_short_name"
                placeholder="添加公司简称"
              ></uni-easyinput>
            </view>
          </uni-forms-item>
          <uni-forms-item>
            <template #label>
              <view class="task-label">公司全称</view>
            </template>
            <view class="item-box">
              <uni-easyinput
                v-model="editFormData.company_name"
                maxlength="15"
                placeholder="添加公司全称"
              ></uni-easyinput>
            </view>
          </uni-forms-item>
          <uni-forms-item>
            <template #label>
              <view class="task-label">客户级别</view>
            </template>
            <view class="item-box">
              <uni-data-select
                v-model="editFormData.customer_level"
                :localdata="priorityList"
                placeholder="请选择客户级别"
              />
            </view>
          </uni-forms-item>
          <uni-forms-item label="客户备注">
            <template #label>
              <view class="task-label">客户备注</view>
            </template>
            <uni-easyinput
              v-model="editFormData.notes"
              placeholder="请输入客户备注"
              type="textarea"
              maxlength="50"
            ></uni-easyinput>
          </uni-forms-item>
        </uni-forms>
      </view>
    </transition>
    <!-- 联系人部分 -->
    <view class="section-box s-p-b">
      <view class="section-left" @click="toggleExpand('finished')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.finished
              ? 'rotate(0deg)'
              : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">联系人</view>
      </view>
      <uni-icons type="plusempty" size="20" color="#4D5BDE" @click="addNewContact"></uni-icons>
    </view>
    <transition name="slide-fade">
      <view v-show="expandStates.finished">
        <!-- 所有联系人表单（包括主联系人和动态添加的联系人） -->
        <view v-for="(contact, index) in allContacts" :key="index" class="todo-card">
          <!-- 联系人标题和删除按钮 暂时注释-->
          <!-- <view class="contact-header">
            <text class="contact-title">{{ index === 0 ? '主要联系人' : '联系人' }}</text>
            <uni-icons 
              v-if="index !== 0" 
              type="more-filled" 
              size="20" 
              @click="handeOpenDrad(index - 1, contactList[index - 1])"
            ></uni-icons>
          </view> -->
          <!-- 联系人表单字段 -->
          <uni-forms
            class="task-form"
            border
            label-align="center"
          >
            <!-- 循环渲染表单字段 -->
            <uni-forms-item v-for="field in contactFields" :key="field.key">
              <template #label>
                <view class="task-label">{{ field.label }}</view>
              </template>
              <view class="item-box" v-if="!field.isTextarea">
                <uni-easyinput
                  v-if="field.key !== 'contact_birthday'"
                  trim="all"
                  v-model="contact[field.key]"
                  :placeholder="field.placeholder"
                  :maxlength="field.maxlength || 50"
                  :type="field.type || 'text'"
                  :suffixIcon="field.key === 'contact_name' && index !== 0 ? 'more-filled' : ''"
                  @iconClick="handeOpenDrad(index - 1, contactList[index - 1])"
                  @blur="index !== 0 && allowSave ? saveContact(contactList[index - 1]) : null"
                ></uni-easyinput>
                <!-- <uni-datetime-picker
                  v-else
                  type="date"
                  :clear-icon="false"
                  v-model="contact[field.key]"
                  :placeholder="field.placeholder"
                  @change="index !== 0 && allowSave ? saveContact(contactList[index - 1]) : null"
                /> -->
                <DateTimePicker  
                  v-else
                  v-model="contact[field.key]" 
                  type="month-day"
                  :placeholder="field.placeholder"
                  @change="index !== 0 && allowSave ? saveContact(contactList[index - 1]) : null"
                />
              </view>
              <uni-easyinput
                v-else
                trim="all"
                v-model="contact[field.key]"
                :placeholder="field.placeholder"
                type="textarea"
                :maxlength="field.maxlength || 50"
                @blur="index !== 0 ? saveContact(contactList[index - 1]) : null"
              ></uni-easyinput>
            </uni-forms-item>
          </uni-forms>
        </view>
      </view>
    </transition>
  </view>
  <ActionSheet
    v-model:show="customerShow"
    :actions="actions"
    title="删除客户"
    @select="onCustomerSelect"
  />
</template>
      
<script setup>
import { reactive, ref, nextTick, watch, toRefs, onBeforeUnmount, computed } from "vue";
import { updateCustomer, saveCustomerContact, deleteCustomerContact } from "@/http/customer.js";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import DateTimePicker from "@/components/date-time-picker/date-time-picker.vue";
const emit = defineEmits(["refresh"]);
const props = defineProps({
// 客户详情数据
userDetails: {
  type: Object,
  default: {},
},
});
const customerShow = ref(false);
const actions = ref([
{ name: "删除", color: "danger" },
]);
// 当前操作的客户
const currentContactObj = reactive({
index: undefined,
contact: {},
});
// 修改状态管理
const expandStates = ref({
current: true,
finished: true,
});
// 客户级别选项
const priorityList = ref([
{ value: 10, text: "潜在客户" },
{ value: 20, text: "普通客户" },
{ value: 30, text: "重点客户" },
]);
// 1. 使用带标记位的响应式对象
const state = reactive({
editFormData: {
  id: "",
  user_id: "",
  customer_id: "",
  company_name: "",
  company_short_name: "",
  customer_level: 0,
  contact_remark: "",
  contact_birthday: "",
  contact_profile: "",
  contact_name: "",
  contact_title: "",
  phone: "",
  notes: ""
},
isExternalUpdate: false, // 标记是否是外部更新
formDirty: false // 表单是否有修改
})
// 添加一个标记，用于控制是否允许保存
const allowSave = ref(true);
// 联系人列表
const contactList = ref([]);
// 联系人表单字段配置
const contactFields = [
{ 
  key: 'contact_name', 
  label: '联系人', 
  placeholder: '添加联系人', 
  maxlength: 20 
},
{ 
  key: 'contact_title', 
  label: '职位', 
  placeholder: '添加职位', 
  maxlength: 15 
},
{ 
  key: 'phone', 
  label: '手机', 
  placeholder: '添加手机', 
  maxlength: 25, 
  // type: 'number',
  suffixIcon: 'phone' 
},
{ 
  key: 'contact_birthday', 
  label: '联系人生日', 
  placeholder: '联系人生日', 
  maxlength: 20,
  type: 'date'
},
{
  key: 'contact_remark',
  label: '联系人备注', 
  placeholder: '请输入备注', 
  maxlength: 1000,
  isTextarea: true 
},
{
  key: 'contact_profile',
  label: '联系人画像', 
  placeholder: '联系人画像', 
  maxlength: 1000,
  isTextarea: true
},
];

// 计算属性：所有联系人（主联系人 + 动态添加的联系人）
const allContacts = computed(() => {
// 主联系人放在第一位
return [editFormData.value, ...contactList.value];
});

// 添加新联系人
const addNewContact = () => {
allowSave.value = false; // 暂时禁用保存
contactList.value.push({
  customer_id: state.editFormData.customer_id,
  contact_name: "",
  contact_title: "",
  phone: "",
  contact_birthday: "",
  contact_profile: "",
  contact_remark: "",
});
// 延迟恢复保存功能，给DOM更新留出时间
setTimeout(() => {
  allowSave.value = true;
}, 300);
};

// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
allowSave.value = false; // 暂时禁用保存
expandStates.value[type] = !expandStates.value[type];
};
// 点击选项时触发，禁用或加载状态下不会触发
const handeOpenDrad = (index, contact) => {
currentContactObj.index = index;
currentContactObj.contact = contact;
customerShow.value = true;
};
// 点击选项时触发，禁用或加载状态下不会触发
const onCustomerSelect = (item) => {
switch (item.name) {
  case "删除":
  deleteContact(currentContactObj.index,currentContactObj.contact);
    break;
}
customerShow.value = false;
};

// 删除联系人
const deleteContact = async (index, contact) => {
try {
  // 如果有ID，则调用删除接口
  if (contact.id) {
    const res = await deleteCustomerContact({
      id: contact.id
    });
    
    if (res.code === 0) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      });
      contactList.value.splice(index, 1);
    } else {
      uni.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  } else {
    // 如果是新添加的，直接从列表中移除
    contactList.value.splice(index, 1);
  }
} catch (err) {
  console.error('删除联系人失败:', err);
  uni.showToast({
    title: '删除联系人失败',
    icon: 'none'
  });
}
};

// 保存联系人信息
const saveContact = async (contact) => {
  // 如果不允许保存，则直接返回
  if (!allowSave.value) return;
  // 验证必填字段
  if (!contact.contact_name) {
    uni.showToast({
      title: '联系人不能为空',
      icon: 'none'
    });
    return;
  }
  // 提取生日的月和日
  let birthdayMonth = 0;
  let birthdayDay = 0;
  if (contact.contact_birthday) {
    // 解析MM-DD格式的生日
    const birthdayParts = contact.contact_birthday.split('-');
    if (birthdayParts.length === 2) {
      birthdayMonth = parseInt(birthdayParts[0], 10);
      birthdayDay = parseInt(birthdayParts[1], 10);
    }
  }
  try {
    const res = await saveCustomerContact({
      id: contact.id || "",
      customer_id: state.editFormData.customer_id,
      contact_name: contact.contact_name,
      contact_title: contact.contact_title || "",
      phone: contact.phone,
      contact_birthday_m: birthdayMonth,
      contact_birthday_d: birthdayDay,
      contact_profile: contact.contact_profile || "",
      contact_remark: contact.contact_remark || ""
    });
    if (res.code === 0) {
      // 更新联系人ID
      if (res.data && res.data.id) {
        contact.id = res.data.id;
      }
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
      // 触发刷新
      emit("refresh");
    } else {
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  } catch (err) {
    console.error('保存联系人失败:', err);
    uni.showToast({
      title: '保存联系人失败',
      icon: 'none'
    });
  }
};

// 防抖函数
function debounce(fn, delay) {
let timer = null
return function(...args) {
  clearTimeout(timer)
  timer = setTimeout(() => {
    fn.apply(this, args)
  }, delay)
}
}

// 4. 防抖处理提交（500ms）
const handleBusinessSubmitDebounced = debounce(async (formData) => {
console.error('formDataformData',formData);
try {
  // 提取生日的月和日
  let birthdayMonth = 0;
  let birthdayDay = 0;
  if (formData.contact_birthday) {
    // 解析MM-DD格式的生日
    const birthdayParts = formData.contact_birthday.split('-');
    if (birthdayParts.length === 2) {
      birthdayMonth = parseInt(birthdayParts[0], 10);
      birthdayDay = parseInt(birthdayParts[1], 10);
    }
  }
  const res = await updateCustomer({
    ...formData,
    contact_birthday:'',
    contact_birthday_m: birthdayMonth,
    contact_birthday_d: birthdayDay,
  })
  if (res.code === 0) {
    console.error("修改成功:", res);
    emit("refresh");
  }
} catch (err) {
  console.error("请求失败:", err);
}
}, 100)

// 2. 监听props变化（外部更新）
watch(
  () => props.userDetails,
  (newVal) => {
    if (newVal) {
      state.isExternalUpdate = true // 标记为外部更新
      // 处理生日日期格式
      let contact_birthday = newVal.contact_birthday || "";
      // 如果没有contact_birthday但有月日字段，则合成生日
      if (!contact_birthday && newVal.contact_birthday_m && newVal.contact_birthday_d) {
        const month = newVal.contact_birthday_m.toString().padStart(2, '0');
        const day = newVal.contact_birthday_d.toString().padStart(2, '0');
        contact_birthday = `${month}-${day}`;
      }
      Object.assign(state.editFormData, {
        id: newVal.customer_id || "",
        user_id: newVal.user_id || "",
        customer_id: newVal.customer_id || "",
        company_name: newVal.company_name || "",
        company_short_name: newVal.company_short_name || "",
        customer_level: newVal.customer_level || 0,
        contact_remark: newVal.contact_remark || "",
        contact_profile: newVal.contact_profile || "",
        contact_birthday: contact_birthday || "",   
        contact_name: newVal.contact_name || "",
        contact_title: newVal.contact_title || "",
        phone: newVal.phone || "",
        notes: newVal.notes || ""
      })
      // 如果有联系人数据，则更新联系人列表
      if (newVal.contact_list && Array.isArray(newVal.contact_list)) {
        contactList.value = newVal.contact_list.map(contact => {
          // 为每个联系人处理生日格式
          let birthday = contact.contact_birthday || "";
          if (!birthday && contact.contact_birthday_m && contact.contact_birthday_d) {
            const month = contact.contact_birthday_m.toString().padStart(2, '0');
            const day = contact.contact_birthday_d.toString().padStart(2, '0');
            birthday = `${month}-${day}`;
          }
          console.error('为每个联系人处理生日格式-',birthday);
          return {
            ...contact,
            contact_birthday: birthday
          };
        });
      } else {
        contactList.value = [];
      }
      // 使用 nextTick 确保标记重置在更新完成后
      nextTick(() => {
        state.isExternalUpdate = false
      })
    }
  },
  { deep: true, immediate: true }
)

const { editFormData } = toRefs(state)

// 3. 监听表单变化（用户修改）
watch(
() => [...Object.values(state.editFormData)], // 转换为可监听的数组
(newValues, oldValues) => {
  if (state.isExternalUpdate) return // 过滤外部更新
  // 使用高效的值比较
  // const hasChanged = newValues.some((val, index) => 
  //   val !== oldValues[index]
  // )
  // if (hasChanged) {
  //   handleBusinessSubmitDebounced(state.editFormData)
  // }
  // 只要表单有变化，标记为修改状态
  state.formDirty = true;
  // 实时保存修改，使用防抖函数避免频繁请求
  handleBusinessSubmitDebounced(state.editFormData);
},
{ deep: true }
)

// 5. 组件销毁时触发提交
onBeforeUnmount(() => {
// 如果表单有修改，提交数据
if (state.formDirty) {
  handleBusinessSubmitDebounced(state.editFormData);
}
});

</script>
      
<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.customer-details-container {
box-sizing: border-box;
font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
.section-box {
  display: flex;
  align-items: center;
  margin: 15px 5px 15px 5px;
  padding: 0px 10px;
  .section-left {
    display: flex;
    align-items: center;
    .icon-down {
      width: 25px;
      height: 25px;
    }
  }
  .section-title {
    color: var(---, #787d86);
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  
  .contact-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }
}
.s-p-b {
  justify-content: space-between;
}
.todo-card {
  margin: 15px;
  padding: 0px 10px;
  border-radius: 10px;
  border: 1px solid #e2e4e9;
  background-color: #fff;
  & + .todo-card {
    margin-top: 20px;
  }
  .task-form {
    .task-label {
      width: 75px;
      height: 35px;
      color: var(---, #787d86);
      text-overflow: ellipsis;
      font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 35px;
    }
    .item-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 35px;
    }
  }
}
}
</style>