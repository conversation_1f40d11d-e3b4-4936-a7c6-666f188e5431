<template>
  <!-- 待办详情 -->
  <view class="task-detail-container" :style="appSafeAreaStyle">
    <uni-nav-bar
      left-icon="left"
      title="待办详情"
      title-text-align="center"
      right-icon="more-filled"
      backgroundColor="#fff"
      color="#000"
      height="60px"
      @clickLeft="handerLeftBack"
      @clickRight="handerRightEdit"
    />
    <view class="todo-section">
      <checkbox-group @change="handleRadioChange">
        <view class="todo-item">
          <checkbox
            value="0"
            :checked="editFormData.checkbox.length > 0"
            color="#4CAF50"
          />
          <!-- 将 input 元素替换为 textarea -->
          <view class="todo-title">
            <input
              v-model="editFormData.title"
              class="editable-title"
              @blur="handleTitleEdit"
              @confirm="handleTitleEdit"
              :focus="titleFocus"
              @click="titleFocus = true"
              auto-height
              maxlength="50"
            />
          </view>
        </view>
      </checkbox-group>
    </view>
    <!-- 表单区域 -->
    <view class="todo-card">
      <uni-forms
        class="task-form"
        :modelValue="editFormData"
        border
        label-align="center"
      >
        <uni-forms-item>
          <template #label>
            <view class="task-label">截止时间</view>
          </template>
          <view class="item-box">
            <DateTimePicker v-model="editFormData.due_date" type="datetime" @change="handleDueDateChange" />
          </view>
        </uni-forms-item>
        <uni-forms-item>
          <template #label>
            <view class="task-label">提醒时间</view>
          </template>
          <view class="item-box">
            <DateTimePicker v-model="editFormData.notify_time" :type="timeOnly" @change="handleReminderTimeChange"/>
          </view>
        </uni-forms-item>
        <uni-forms-item>
          <template #label>
            <view class="task-label">重复提醒</view>
          </template>
          <view class="item-box">
            <!-- 替换为自定义组件 -->
            <custom-data-select
              v-model="editFormData.notify_cycle"
              :localdata="reminderList"
              :notify-time="editFormData.notify_time"
              @change="handleNotifyCycleChange"
            ></custom-data-select>
          </view>
        </uni-forms-item>
        <uni-forms-item>
          <template #label>
            <view class="task-label">备注</view>
          </template>
            <uni-easyinput
            class="task-easyinput"
            trim="all"
            type="textarea"
            maxlength="500"
            v-model="editFormData.description"
            placeholder="添加"
            @change="handleDescriptionChange"
          ></uni-easyinput>
        </uni-forms-item>
        <uni-forms-item label="优先级">
          <template #label>
            <view class="task-label">优先级</view>
          </template>
          <view class="item-box">
            <uni-data-select
              v-model="editFormData.priority"
              :localdata="priorityList"
              @change="bindPickerChange"
              :clear="true"
            ></uni-data-select>
          </view>
        </uni-forms-item>
        <uni-forms-item>
          <template #label>
            <view class="task-label">关联客户</view>
          </template>
          <view class="item-box">
            <uni-data-select
              v-model="editFormData.customer_id"
              :localdata="oldCustomerList"
              @change="handleCustomerChange"
              :clear="true"
            ></uni-data-select>
          </view>
        </uni-forms-item>
        <uni-forms-item>
          <template #label>
            <view class="task-label">关联商机</view>
          </template>
          <view class="item-box">
            <uni-data-select
              v-model="editFormData.bo_id"
              :localdata="oldBusinessList"
              @change="handleBusinessChange"
              :clear="true"
            ></uni-data-select>
          </view>
        </uni-forms-item>
      </uni-forms>
    </view>
    <view class="switch-list-cell">
			<view class="switch-cell-text">AI自动执行</view>
			<switch :checked="editFormData.type === 2"  style="transform:scale(0.8)" @change="handleSwitchChange" />
		</view>
    <view class="task-time">
      <text class="time-text" v-if="editFormData.checkbox.length > 0">完成于 {{ editFormData.updated_at }}</text>
      <text class="time-text">创建于 {{ editFormData.created_at }}</text>
    </view>
    <!-- 操作按钮 -->
    <ActionSheet
      v-if="todoShow"
      v-model:show="todoShow"
      :actions="actions"
      title="操作"
      @select="onTodoSelect"
    />
    <!-- AI 对话组件 -->
    <CustomerService bottom="0px" />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted,computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
// 在script部分引入事件总线
import { eventBus } from "@/utils/eventBus.js";
import { clearLinkTapStatus } from '@/utils/globalState.js';
import { getTodoDetail, updateTodoItem,deleteTodoItem } from "@/http/todo.js";
import { getCustomerList } from "@/http/customer.js";
import { fetchOpportunityList } from "@/http/business.js";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import DateTimePicker from "@/components/date-time-picker/date-time-picker.vue";
import CustomerService from "@/components/CustomerService.vue";
import CustomDataSelect from "@/components/custom-data-select/custom-data-select.vue";

const reminderList = ref([
  { value: 0, text: "无" },
  { value: 1, text: "每天" },
  { value: 2, text: "工作日" },
  { value: 3, text: "周末" },
  { value: 4, text: "每周" },
  { value: 5, text: "每两周" },
  { value: 6, text: "每月" },
  { value: 7, text: "每三个月" },
  { value: 8, text: "每六个月" },
  { value: 9, text: "每年" },
]);
const priorityList = ref([
  // { value: 1, text: "高" },
  // { value: 2, text: "中" },
  // { value: 3, text: "低" },
  { value: 30, text: "高" },
  { value: 20, text: "中" },
  { value: 10, text: "低" },
]);
const actions = ref([
  { name: "删除", color: "danger" },
]);
// 在ref声明部分添加
const titleFocus = ref(false);
const todoShow = ref(false);
const timeOnly = ref('datetime');  // 是否只显示时间
const oldCustomerList = ref([]);
const oldBusinessList = ref([]);
// 新建表单数据
const editFormData = reactive({
  id: "",
  notify_cycle: 0,
  due_date: "",
  notify_time: "",
  description: "",
  checkbox: [],
  created_at: "",
  updated_at: "",
  priority: 0,
  customer_id: 1,
  company_short_name: "",
  bo_id: 1,
  title: "",
  type: 0, // 新增字段，用于表示开关状态
});
// APP环境下的安全区域样式
const appSafeAreaStyle = computed(() => {
  // #ifdef APP-PLUS
  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
  return {
    paddingTop: `${statusBarHeight}px`
  };
  // #endif
  return {};
});
// 【顶部导航栏】左侧按钮点击时触发
const handerLeftBack = () => {
  clearLinkTapStatus();
  uni.switchTab({
    url: '/pages/tabBar/todo/todo',
    success: () => {
      console.log('切换成功');
    },
    fail: (err) => {
      console.error('切换失败', err);
      uni.showToast({
        title: "切换失败",
        icon: "none",
      });
    }
  });
};

// 【顶部导航栏】右侧按钮点击时触发
const handerRightEdit = () => {
  todoShow.value = true;
};
// 点击选项时触发，禁用或加载状态下不会触发
const onTodoSelect = (item) => {
  switch (item.name) {
    case "删除":
      deleteTodo();
      break;
  }
  todoShow.value = false;
};

// 删除待办
const deleteTodo = async () => {
  try {
    const res = await deleteTodoItem({
      id: editFormData.id,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除成功",
        icon: "success",
      });
      // 触发全局事件，通知列表页刷新
      eventBus.emit("todoUpdated", editFormData.id);
      // 返回上一页（待办列表页）
      uni.navigateBack({
        delta: 2
      });
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};

// 处理标题编辑
const handleTitleEdit = async () => {
  // 失去焦点时保存
  titleFocus.value = false;
  // 如果标题没有变化，不需要更新
  if (!editFormData.title.trim()) {
    uni.showToast({
      title: "标题不能为空",
      icon: "none",
    });
    return;
  }
  // 更新到服务器
  const updateRes = await updateTodoItem({
    id: editFormData.id,
    title: editFormData.title,
  });
  if (updateRes.code === 0) {
    // uni.showToast({
    //   title: "标题修改成功",
    //   icon: "none",
    // });
    // 触发全局事件，通知列表页刷新
    eventBus.emit("todoUpdated", editFormData.id);
  } else {
    uni.showToast({
      title: "修改失败",
      icon: "none",
    });
  }
};

// 处理switch开关状态变化
const handleSwitchChange = async (e) => {
  const isChecked = e.detail.value;
  editFormData.type = isChecked ? 2 : 0;
  try {
    const res = await updateTodoItem({
      id: editFormData.id,
      type: editFormData.type, // 根据需求，传递参数type=2
    });
    if (res.code === 0) {
      uni.showToast({
        title: isChecked ? "已开启" : "已关闭",
        icon: "none",
      });
      // fetchTodoDetail(editFormData.id);
    } else {
      uni.showToast({
        title: "操作失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("开关状态更新失败:", error);
    uni.showToast({
      title: "网络异常，请稍后再试",
      icon: "none",
    });
  }
};

// @change 重复提醒 事件处理
const handleNotifyCycleChange = async (val) => {
  try {
    // 处理空值的情况，重置相关字段
    if (val === '') {
      editFormData.notify_time = '';
      editFormData.notify_cycle = 0; // 重置为无重复提醒
    }
    // 设置时间选择的类型
    timeOnly.value = val !== 0 ? 'time' : 'datetime';
    // 执行更新操作
    const res = await updateTodoItem({
      id: editFormData.id,
      notify_cycle: editFormData.notify_cycle,
      notify_time: editFormData.notify_time,
    });
    // 如果更新成功，显示提示并刷新任务详情
    if (res.code === 0) {
      uni.showToast({
        title: '修改成功',
        icon: 'none',
      });
      fetchTodoDetail(editFormData.id);
    } else {
      // 如果返回错误码，处理错误
      console.error('更新失败:', res);
      uni.showToast({
        title: '修改失败，请稍后重试',
        icon: 'none',
      });
    }
  } catch (error) {
    // 捕获错误并显示提示
    console.error('发生错误:', error);
  }
};

// @change 优先级 重复提醒 事件处理
const bindPickerChange = async (val) => {
  if (val!==0) {
    timeOnly.value = 'time';
  } else {
    timeOnly.value = 'datetime';
  }
  const res = await updateTodoItem({
    id: editFormData.id,
    priority: editFormData.priority,
    notify_cycle: editFormData.notify_cycle,
    notify_time: editFormData.notify_time,
  });
  if (res.code === 0) {
    uni.showToast({
      title: "修改成功",
      icon: "none",
    });
    fetchTodoDetail(editFormData.id);
  }
};

// @change 选中任务 事件处理
const handleRadioChange = async (e) => {
  // 根据选中状态设置checkbox数组
  const isChecked = e.detail.value.length > 0;
  editFormData.checkbox = isChecked ? [0] : [];
  // 更新到服务器
  const res = await updateTodoItem({
    id: editFormData.id,
    is_finished: isChecked ? 1 : 0,
  });
  if (res.code === 0) {
    uni.showToast({
      title: isChecked ? "已完成" : "未完成",
      icon: "none",
    });
    // 不需要重新获取详情，避免页面闪烁
    fetchTodoDetail(editFormData.id);
  }
};

// 截至时间
const handleDueDateChange = async (e) => {
  const res = await updateTodoItem({
    id: editFormData.id,
    due_date: editFormData.due_date,
  });
  if (res.code === 0) {
    fetchTodoDetail(editFormData.id);
    eventBus.emit("todoUpdated", editFormData.id);
  }
  console.log("截至时间:", e);
};
// 提醒时间
const handleReminderTimeChange = async (e) => {
  let notifyTime = editFormData.notify_time;
  // 如果 timeOnly 为 'time'，拼接今天的日期和选择的时间
  if (timeOnly.value === 'time') {
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // 格式化为 "YYYY-MM-DD"
    const time = editFormData.notify_time; // 获取选中的时间，比如 '11:17'
    notifyTime = `${formattedDate} ${time}`; // 拼接日期和时间
  }
  const res = await updateTodoItem({
    id: editFormData.id,
    due_date: editFormData.due_date,
    notify_cycle: editFormData.notify_cycle,
    notify_time: notifyTime,
  });
  if (res.code === 0) {
    fetchTodoDetail(editFormData.id);
    eventBus.emit("todoUpdated", editFormData.id);
  }
  console.log("提醒时间:", e);
};

// @change 修改备注 事件处理
const handleDescriptionChange = async (e) => {
  const res = await updateTodoItem({
    id: editFormData.id,
    description: editFormData.description,
  });
  if (res.code === 0) {
    fetchTodoDetail(editFormData.id);
  }
};
// 关联客户
const handleCustomerChange = async (customerId) => {
  // 从客户列表中找到对应的公司名称
  // const selectedCustomer = oldCustomerList.value.find(
  //   (item) => item.value === editFormData.customer_id
  // );
  // // 如果找到了客户，更新公司名称
  // if (selectedCustomer) {
  //   editFormData.company_short_name = selectedCustomer.text;
  // }
  editFormData.customer_id = customerId;
  if (editFormData.customer_id) {
    // 如果选择了客户，获取该客户下的商机列表
   await queryBusinessList(editFormData.customer_id);
    if (oldBusinessList.value.length===0) {
      editFormData.bo_id = '';
      console.error('商机列表数据',oldBusinessList.value);
    }
  } else {
    if (oldBusinessList.value.length > 0) {
      editFormData.customer_id = '';
      editFormData.bo_id = '';
      queryBusinessList(); // 重新获取商机列表
    }
  }
  const res = await updateTodoItem({
    id: editFormData.id,
    bo_id: editFormData.bo_id || '',
    customer_id: editFormData.customer_id || '',
    // company_short_name: editFormData.company_short_name,
  });
  if (res.code === 0) {
    // uni.showToast({
    //   title: "关联成功",
    //   icon: "none",
    // });
    // fetchTodoDetail(editFormData.id);
    eventBus.emit("todoUpdated", editFormData.id);
  }
};
// 关联商机
const handleBusinessChange = async (businessId) => {
  console.error('businessId',businessId);
  editFormData.bo_id = businessId;
  // 从商机列表中找到对应的客户ID
  if (editFormData.bo_id) {
    // 商机存在去映射客户回显客户
    console.error("当前客户ID:", editFormData.customer_id);
    // 判断是否选择了客户，如果没有选择，通过选择商机来带出客户
    if (editFormData.customer_id==='') {
      let currentBusiness = oldBusinessList.value.find(
        (item) => item.value === editFormData.bo_id
      );
      editFormData.customer_id = currentBusiness.customer_id;
      queryBusinessList(editFormData.customer_id); // 重新获取商机列表
    }
  }
  const res = await updateTodoItem({
    id: editFormData.id,
    bo_id: editFormData.bo_id || '',
    customer_id: editFormData.customer_id || '',
  });
  if (res.code === 0) {
    // uni.showToast({
    //   title: "关联成功",
    //   icon: "none",
    // });
    // fetchTodoDetail(editFormData.id);
    eventBus.emit("todoUpdated", editFormData.id);
  }
};

// 初始化客户列表
const queryCustomerList = async (boId='') => {
  try {
    const res = await getCustomerList({
      page: 1,
      limit: 200,
      customer_id: String(boId), // 确保传递的是字符串
    });
    console.log("初始化客户列表:", oldCustomerList.value);
    if (res.code === 0 && res.data.list.length) {
      oldCustomerList.value = res.data?.list.map((item) => ({
        text: item.company_short_name, // 显示的文本
        value: item.customer_id, // 对应的值
      }));
    } else {
      oldCustomerList.value = [];
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};

// 初始化商机列表
const queryBusinessList = async (customerId='') => {
  try {
    const res = await fetchOpportunityList({
      page: 1,
      limit: 200,
      customer_id: String(customerId) || editFormData.customer_id,
    });
    console.log("初始化商机列表:", res);
    if (res.code === 0 && res.data.list.length) {
      oldBusinessList.value = res.data?.list.map((item) => ({
        text: item.title, // 显示的文本
        value: item.bo_id, // 对应的值
        customer_id: item.customer_id,
      }));
    } else {
      oldBusinessList.value = [];
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};

// 获取代办详情
const fetchTodoDetail = async (todoId) => {
  try {
    const res = await getTodoDetail({
      id: todoId,
    });
    if (res.code === 0) {
      console.log("获取代办详情:", res);
      editFormData.id = res.data.id;
      editFormData.due_date = res.data.due_date;
      editFormData.notify_time = res.data.notify_time;
      editFormData.title = res.data.title;
      editFormData.description = res.data.description;
      editFormData.created_at = res.data.created_at;
      editFormData.updated_at = res.data.updated_at;
      editFormData.priority = res.data.priority;
      editFormData.notify_cycle = res.data.notify_cycle;
      editFormData.checkbox = res.data.is_finished
        ? (editFormData.checkbox = [0])
        : [];
      // 客户
      editFormData.customer_id = res.data?.customer?.customer_id || "";
      editFormData.company_short_name = res.data?.customer?.company_short_name || "";
      // 商机
      editFormData.bo_id = res.data?.business?.bo_id;
      // 更新switch状态
      editFormData.type = res.data.type || 0;
      if (editFormData.notify_cycle !== 0) {
        timeOnly.value = 'time';
      } else {
        timeOnly.value = 'datetime';
      }
    }
  } catch (err) {
    console.error("请求失败:", err);
    uni.showToast({
      title: "网络异常，请稍后再试",
      icon: "none",
    });
  }
};

// 使用 Uni-app 官方生命周期
onLoad(async (options) => {
  // 这里 options 包含所有平台的参数
  if (options.id) {
    // 获取待办详情
    await fetchTodoDetail(options.id); // 根据路由传递的 id 获取详情
    // 查询客户列表
    await queryCustomerList();
    // 初始化商机列表
    await queryBusinessList();
  }
});

onMounted(() => {});
</script>
<style>
.uni-page-head {
  display: none; /* 根据变量控制显示与隐藏 */
}
</style>
<style scoped lang="scss">
// 媒体查询相关样式
@import '/styles/public-layout.scss';
.task-detail-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .todo-section {
    margin: 15px;
    padding: 0px 5px;
    // max-height: 50px;
    // overflow-y: auto;
    .editable-title {
      width: 100%;
      border: none;
      background: transparent;
      font-size: 16px;
      font-weight: bold;
      color: #333;
      padding: 0;
      min-height: 24px;
      line-height: 1.5;
      word-break: break-word; /* 允许在单词内换行 */
      white-space: pre-wrap; /* 保留空格和换行符 */
      overflow: visible; /* 防止内容被截断 */
      resize: none; /* 禁止用户调整大小 */
    }
    .editable-title:focus {
      outline: none;
      border-bottom: 1px solid #4d5bde;
    }
    // 添加自定义样式
    .todo-item {
      display: flex;
      align-items: flex-start; /* 改为顶部对齐，适应多行文本 */
    }
    .todo-title {
      margin-left: 10px;
      font-size: 16px;
      color: #333;
      flex: 1;
    }
  }
  .todo-card {
    margin: 15px;
    padding: 0px 10px;
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;
    .task-form {
      .task-label {
        width: 75px;
        height: 35px;
        color: var(---, #787d86);
        text-overflow: ellipsis;
        font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 35px;
      }
      .item-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 35px;
        .picker-box {
          width: 100%;
          .picker-content {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }
        // :deep(.uni-date-x--border) {
        //   border: unset;
        // }
      }
      // :deep(.is-input-border) {
      //   border: unset;
      // }
      // :deep(.uni-easyinput__content-input) {
      //   padding-left: 0 !important;
      // }
    }
  }
  .switch-list-cell{
    margin: 20px 15px 0 15px;
    padding: 15px 14px;
    border-radius: 10px;
    border: 1px solid #E2E4E9;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #787D86;
    font-family: PingFang SC;
  }
  .task-time {
    text-align: center;
    overflow: hidden;
    color: var(---, #adb1ba);
    text-align: center;
    text-overflow: ellipsis;
    font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    .time-text {
      display: block;
      margin: 10px 0;
    }
  }
}
</style>
