<svg width="129" height="130" viewBox="0 0 129 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_3698_17726)">
<path d="M108.877 107.315L72.5246 87.7874L66.2339 70.9975L48.0271 61.8424L27.431 67.392L16.9093 80.039L26.2149 91.4903L46.2029 97.9898L67.3467 92.4678L108.607 112.67L108.877 107.315Z" fill="url(#paint0_linear_3698_17726)" fill-opacity="0.6"/>
</g>
<ellipse cx="60.7314" cy="39.7359" rx="16.8811" ry="27.5718" transform="rotate(-71.1098 60.7314 39.7359)" fill="#DBFFFD" fill-opacity="0.45"/>
<g filter="url(#filter1_f_3698_17726)">
<ellipse cx="52.3986" cy="43.6041" rx="9.46121" ry="15.4529" transform="rotate(-71.1098 52.3986 43.6041)" fill="white"/>
</g>
<g filter="url(#filter2_ii_3698_17726)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M89.184 46.1871C92.7575 35.7439 83.301 23.0508 68.0622 17.8363C52.8235 12.6219 37.5732 16.8606 33.9996 27.3039C32.4673 31.7819 33.3308 36.6737 36.0187 41.2121C34.0999 37.7157 33.5198 34.0993 34.6464 30.8069C37.6648 21.9858 51.7912 18.8315 66.1986 23.7615C80.606 28.6915 89.8385 39.8389 86.8201 48.6599C86.1033 50.7545 84.7603 52.5296 82.9359 53.9528C85.8925 52.0107 88.0877 49.3909 89.184 46.1871ZM55.0473 55.6292C53.2978 55.027 51.6249 54.3263 50.0396 53.5417C51.6227 54.3191 53.2962 55.0201 55.0473 55.6292Z" fill="url(#paint1_linear_3698_17726)"/>
</g>
<foreignObject x="25.4051" y="10.6507" width="70.2674" height="59.8851"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_3698_17726_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter3_i_3698_17726)" data-figma-bg-blur-radius="3.99774">
<path fill-rule="evenodd" clip-rule="evenodd" d="M90.4392 50.8655C95.3556 36.4977 85.0982 22.7376 68.5718 17.0826C52.0454 11.4275 35.6756 15.53 30.5919 30.3867C26.1425 43.3894 34.3453 57.9713 52.4593 64.1697C70.5732 70.368 85.9898 63.8683 90.4392 50.8655ZM89.1816 46.1922C92.7551 35.7489 83.2986 23.0559 68.0598 17.8414C52.8211 12.6269 37.5707 16.8657 33.9972 27.3089C30.4237 37.7521 39.8802 50.4452 55.119 55.6597C70.3577 60.8742 85.608 56.6354 89.1816 46.1922Z" fill="url(#paint2_linear_3698_17726)" fill-opacity="0.8"/>
</g>
<foreignObject x="72.8983" y="51.9417" width="46.7079" height="46.7074"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_3698_17726_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter4_i_3698_17726)" data-figma-bg-blur-radius="3.99774">
<path fill-rule="evenodd" clip-rule="evenodd" d="M77.719 63.2139C77.7135 63.2086 77.7081 63.2032 77.7027 63.1978C76.3665 61.8616 76.7275 59.334 78.5092 57.5523C80.2908 55.7707 82.8182 55.4096 84.1545 56.7455L84.1546 56.7454L115.608 88.1993L109.156 94.6514L77.719 63.2139Z" fill="url(#paint3_linear_3698_17726)"/>
</g>
<foreignObject x="103.489" y="82.4566" width="18.2323" height="18.2323"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.74px);clip-path:url(#bgblur_2_3698_17726_clip_path);height:100%;width:100%"></div></foreignObject><ellipse data-figma-bg-blur-radius="5.48384" cx="112.605" cy="91.5728" rx="4.5623" ry="2.35891" transform="rotate(-45 112.605 91.5728)" fill="url(#paint4_linear_3698_17726)" fill-opacity="0.6"/>
<defs>
<filter id="filter0_f_3698_17726" x="0.457653" y="45.3912" width="124.871" height="83.7302" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.22576" result="effect1_foregroundBlur_3698_17726"/>
</filter>
<filter id="filter1_f_3698_17726" x="24.9112" y="20.8013" width="54.975" height="45.6054" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.27317" result="effect1_foregroundBlur_3698_17726"/>
</filter>
<filter id="filter2_ii_3698_17726" x="33.2446" y="15.6055" width="56.6943" height="41.164" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.14058"/>
<feGaussianBlur stdDeviation="0.570288"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3698_17726"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.570288" dy="0.570288"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_3698_17726" result="effect2_innerShadow_3698_17726"/>
</filter>
<filter id="filter3_i_3698_17726" x="25.4051" y="10.6507" width="70.2674" height="59.8851" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.570288" dy="0.570288"/>
<feGaussianBlur stdDeviation="0.570288"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3698_17726"/>
</filter>
<clipPath id="bgblur_0_3698_17726_clip_path" transform="translate(-25.4051 -10.6507)"><path fill-rule="evenodd" clip-rule="evenodd" d="M90.4392 50.8655C95.3556 36.4977 85.0982 22.7376 68.5718 17.0826C52.0454 11.4275 35.6756 15.53 30.5919 30.3867C26.1425 43.3894 34.3453 57.9713 52.4593 64.1697C70.5732 70.368 85.9898 63.8683 90.4392 50.8655ZM89.1816 46.1922C92.7551 35.7489 83.2986 23.0559 68.0598 17.8414C52.8211 12.6269 37.5707 16.8657 33.9972 27.3089C30.4237 37.7521 39.8802 50.4452 55.119 55.6597C70.3577 60.8742 85.608 56.6354 89.1816 46.1922Z"/>
</clipPath><filter id="filter4_i_3698_17726" x="72.8983" y="51.9417" width="46.7079" height="46.7074" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.570288" dy="0.570288"/>
<feGaussianBlur stdDeviation="0.570288"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.49 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3698_17726"/>
</filter>
<clipPath id="bgblur_1_3698_17726_clip_path" transform="translate(-72.8983 -51.9417)"><path fill-rule="evenodd" clip-rule="evenodd" d="M77.719 63.2139C77.7135 63.2086 77.7081 63.2032 77.7027 63.1978C76.3665 61.8616 76.7275 59.334 78.5092 57.5523C80.2908 55.7707 82.8182 55.4096 84.1545 56.7455L84.1546 56.7454L115.608 88.1993L109.156 94.6514L77.719 63.2139Z"/>
</clipPath><clipPath id="bgblur_2_3698_17726_clip_path" transform="translate(-103.489 -82.4566)"><ellipse cx="112.605" cy="91.5728" rx="4.5623" ry="2.35891" transform="rotate(-45 112.605 91.5728)"/>
</clipPath><linearGradient id="paint0_linear_3698_17726" x1="113.112" y1="112.967" x2="54.435" y2="80.6125" gradientUnits="userSpaceOnUse">
<stop stop-color="#789DC8"/>
<stop offset="1" stop-color="#789DC8" stop-opacity="0.12"/>
</linearGradient>
<linearGradient id="paint1_linear_3698_17726" x1="94.0254" y1="43.9046" x2="56.5785" y2="7.04688" gradientUnits="userSpaceOnUse">
<stop stop-color="#698AFF"/>
<stop offset="0.515126" stop-color="#DFEBFF"/>
<stop offset="1" stop-color="#85C4FF"/>
</linearGradient>
<linearGradient id="paint2_linear_3698_17726" x1="25.5901" y1="29.0831" x2="79.5061" y2="67.14" gradientUnits="userSpaceOnUse">
<stop stop-color="#DEF2FD"/>
<stop offset="0.485861" stop-color="#E2EAFF"/>
<stop offset="1" stop-color="#7896E1"/>
</linearGradient>
<linearGradient id="paint3_linear_3698_17726" x1="91.7434" y1="78.6932" x2="100.198" y2="71.1445" gradientUnits="userSpaceOnUse">
<stop stop-color="#EEF3FF"/>
<stop offset="0.751556" stop-color="#C3CEEC"/>
<stop offset="1" stop-color="#E0E4EF"/>
</linearGradient>
<linearGradient id="paint4_linear_3698_17726" x1="109.379" y1="88.6846" x2="113.815" y2="94.7335" gradientUnits="userSpaceOnUse">
<stop stop-color="#8BC7FF"/>
<stop offset="1" stop-color="#7693E2"/>
</linearGradient>
</defs>
</svg>
