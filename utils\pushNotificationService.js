/**
 * 推送通知服务模块
 * 处理推送消息的接收、解析和页面跳转
 */
class PushNotificationService {
  constructor() {
    this.isInitialized = false;
    this.pendingPushData = null;
  }

  /**
   * 初始化推送服务
   */
  // #ifdef APP-PLUS
  init() {
    if (this.isInitialized) {
      console.log('推送服务已初始化');
      return;
    }
    // 获取推送客户端ID
    this.getPushClientId();
    // 监听推送消息
    this.setupPushMessageListener();
    this.isInitialized = true;
  }
  // #endif

  /**
   * 获取推送客户端ID
   */
  getPushClientId() {
    // #ifdef APP-PLUS
    uni.getPushClientId({
      success: (res) => {
        console.log('📱 推送客户端ID:', res.cid);
        uni.setStorageSync('pushClientId', res.cid);
      },
      fail: (err) => {
        console.error('❌ 获取推送客户端ID失败:', err);
      }
    });
    // #endif
  }

  /**
   * 确保获取到推送客户端ID
   * @returns {Promise<String>} 推送客户端ID
   */
  ensureClientId() {
    // #ifdef APP-PLUS
    return new Promise((resolve) => {
      const existingCid = uni.getStorageSync('pushClientId') || '';
      if (existingCid) {
        console.log('📱 使用已存储的推送客户端ID:', existingCid);
        resolve(existingCid);
        return;
      }
      // 如果没有存储的CID，重新获取
      uni.getPushClientId({
        success: (res) => {
          console.log('📱 重新获取推送客户端ID:', res.cid);
          uni.setStorageSync('pushClientId', res.cid);
          resolve(res.cid);
        },
        fail: (err) => {
          console.error('❌ 获取推送客户端ID失败:', err);
          resolve(''); // 失败时返回空字符串
        }
      });
    });
    // #endif
  }

  /**
   * 设置推送消息监听器
   */
  setupPushMessageListener() {
    // #ifdef APP-PLUS
    uni.onPushMessage((res) => {
      console.error('📨 收到推送消息:', res);
      // 处理点击通知事件 "click"-从系统推送服务点击消息启动应用事件；
      if (res.type === 'click') {
        console.log('👆 点击了推送通知:', res);
        this.handlePushClick(res);
        return;
      }
      // "receive"-应用从推送服务器接收到推送消息事件。
      if (res.type === 'receive') {
        // 处理接收到的推送消息
        if (res.data) {
          this.handlePushReceive(res);
        }
      }
    });
    // #endif
  }

  /**
   * 处理接收到的推送消息
   * @param {Object} res - 推送消息响应
   */
  handlePushReceive(res) {
    // #ifdef APP-PLUS
    console.log('📥 处理推送消息:', res.data);
    // 解析推送数据
    let pushData = this.parsePushData(res.data);
    // 创建系统通知
    this.createSystemNotification(pushData);
    // #endif
  }

  /**
   * 解析推送数据
   * @param {String|Object} data - 推送数据
   * @returns {Object} 解析后的推送数据
   */
  parsePushData(data) {
    let pushData = data;
    if (typeof data === 'string') {
      try {
        pushData = JSON.parse(data);
      } catch (e) {
        console.error('❌ 推送数据解析失败:', e);
      }
    }
    let targetUrl = null; // 默认跳转到AI聊天页面

    if (pushData.payload && pushData.payload.url) {
      targetUrl = pushData.payload.url;
    } else if (pushData.url) {
      targetUrl = pushData.url;
    }

    // 确保必要字段存在
    pushData = {
      title: '新消息',
      content: '您有一条新消息',
      url: targetUrl,
      ...pushData
    };
    console.log('✅ 推送数据解析完成:', pushData);
    return pushData;
  }

  /**
   * 创建系统通知
   * @param {Object} pushData - 推送数据
   */
  createSystemNotification(pushData) {
    // #ifdef APP-PLUS
    console.error('系统通知-------',pushData);
    // 创建推送通知，使用Ivy作为应用标题
    uni.createPushMessage({
      cover: false, // 是否覆盖上一次提示的消息
      delay: 0, // 提示消息延迟显示的时间，单位为s
      sound: 'system', // 提示音
      title: pushData.title || 'Ivy', // 通知标题，默认为Ivy
      content: pushData.content || '您有一条新消息', // 通知内容
      payload: pushData, // 自定义数据，点击通知时可以获取
      // icon: 'static/push/large/96.png', // 通知图标
      // 注意：推送图标已在manifest.json中配置为Ivy.png
    });
    // #endif
  }

  /**
   * 处理推送通知点击事件
   * @param {Object} pushData - 推送数据
   */
  handlePushClick(pushData) {
    console.log('🎯 处理推送点击:', pushData);
    // 清除应用角标（支持iOS和Android）
    // this.clearAppBadge();
    // 直接根据URL进行页面跳转
    this.navigateToUrl(pushData);
  }

  /**
   * 根据URL进行页面跳转
   * @param {Object} pushData - 推送数据
   */
  navigateToUrl(pushData) {
    // console.error('🧭 根据URL跳转:-------------', pushData);
    // 获取目标URL
    let targetUrl = '';
    // 从payload中提取URL（新数据结构）
    if (pushData.data.payload && pushData.data.payload.url) {
      targetUrl = pushData.data.payload.url;
    }
    // 这里用于测试
    // plus.nativeUI.confirm(targetUrl, function(e){
    //   console.log("Close confirm: " + e);
    // });
    console.log('🎯 目标URL:', targetUrl);
    // 延迟执行跳转，确保应用完全启动
    setTimeout(() => {
      this.performNavigation(targetUrl);
    }, 500); // 延迟500ms确保应用启动完成
  }

  /**
   * 执行页面跳转
   * @param {String} url - 目标页面URL
   */
  performNavigation(url) {
    console.log('🚀 执行页面跳转:', url);
    uni.navigateTo({
      url: url,
      success: () => {
        console.log('✅ 成功跳转到页面:', url);
      },
      fail: (err) => {
        console.error('❌ 跳转到页面失败:', err);
      }
    });
  }
  /**
   * 清除应用角标（支持iOS和Android）
   * 使用H5+方法清理推送角标
   */
  clearAppBadge() {
    // #ifdef APP-PLUS
    try {
      plus.runtime.setBadgeNumber(0);
      // plus.push.clear();
    } catch (error) {
      console.error('❌ 清除应用角标失败:', error);
    }
    // #endif
  }
}

// 创建单例实例
const pushNotificationService = new PushNotificationService();

export default pushNotificationService;
