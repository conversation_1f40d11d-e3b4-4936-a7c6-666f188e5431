import { ref, watch } from 'vue';
import { createAliyunTts } from '../utils/aliyunTts.js';

/**
 * 语音播放服务
 * 封装了阿里云语音合成服务的调用和管理，支持播放、暂停和停止操作
 */
class VoicePlayService {
  constructor() {
    // 单例模式，确保只有一个实例
    if (VoicePlayService.instance) {
      return VoicePlayService.instance;
    }
    
    // 创建实例并记录
    VoicePlayService.instance = this;
    
    // 初始化状态
    this.isPlaying = ref(0); // 0-未播放，1-正在播放
    this.currentText = ref(''); // 当前正在播放的文本
    this.ttsInstance = ref(null); // TTS实例
    this.ttsServices = null; // TTS服务方法集合
    
    // 解构的TTS方法
    this.connectAndStartSynthesis = null;
    this.sendRunSynthesis = null;
    this.sendStopSynthesis = null;
    this.pauseAudioPlayback = null;
    this.setOnPlaybackEndCallback = null;
    this.stopAllPlayback = null;
    this.isPlayingText = null;
    
    // 初始化TTS服务
    this.initTtsService();
    
    // 监听token变化，自动重新初始化
    if (typeof uni !== 'undefined') {
      watch(() => uni.getStorageSync("aliyunToken"), (newToken) => {
        if (newToken) {
          this.initTtsService();
        }
      });
    }
  }
  
  /**
   * 初始化TTS服务
   * @returns {Object|null} 返回TTS服务实例或null
   */
  initTtsService() {
    try {
      // 获取token
      const token = typeof uni !== 'undefined' ? uni.getStorageSync("aliyunToken") : null;
      if (!token) {
        console.warn('语音合成token不存在，无法初始化TTS服务');
        return null;
      }
      
      // 创建TTS实例
      this.ttsInstance.value = createAliyunTts({ token });
      this.ttsServices = this.ttsInstance.value;
      
      // 解构方法
      this.connectAndStartSynthesis = this.ttsServices.connectAndStartSynthesis;
      this.sendRunSynthesis = this.ttsServices.sendRunSynthesis;
      this.sendStopSynthesis = this.ttsServices.sendStopSynthesis;
      this.pauseAudioPlayback = this.ttsServices.pauseAudioPlayback;
      this.setOnPlaybackEndCallback = this.ttsServices.setOnPlaybackEndCallback;
      this.stopAllPlayback = this.ttsServices.stopAllPlayback;
      this.isPlayingText = this.ttsServices.isPlayingText;
      
      // 设置默认的播放结束回调
      this.setDefaultPlaybackEndCallback();
      
      return this.ttsServices;
    } catch (error) {
      console.error('初始化TTS服务失败:', error);
      return null;
    }
  }
  
  /**
   * 设置默认的播放结束回调
   */
  setDefaultPlaybackEndCallback() {
    if (this.setOnPlaybackEndCallback) {
      this.setOnPlaybackEndCallback(() => {
        console.log('音频播放结束，重置播放状态');
        this.isPlaying.value = 0;
        this.currentText.value = '';
      });
    }
  }
  
  /**
   * 播放或暂停语音
   * @param {string} content - 要播放的文本内容
   * @param {number} currentState - 当前播放状态(0:未播放, 1:正在播放)
   * @returns {boolean} 播放操作是否成功
   */
  playVoice(content, currentState = 0) {
    // 输入验证
    if (!content) {
      console.error('无效的内容，无法播放语音');
      return false;
    }
    
    // 确保TTS服务已初始化
    if (!this.ttsInstance.value) {
      const token = typeof uni !== 'undefined' ? uni.getStorageSync("aliyunToken") : null;
      if (!token) {
        console.error('没有找到语音合成token');
        return false;
      }
      
      // 尝试重新初始化
      if (!this.initTtsService()) {
        console.error('TTS服务初始化失败');
        return false;
      }
    }
    
    // 检查当前播放状态
    if (currentState === 0) {
      // 点击播放按钮开始播放
      
      // 如果是同一文本正在播放中，则暂停播放
      if (this.isPlayingText(content)) {
        this.isPlaying.value = 0;
        this.pauseAudioPlayback();
        return true;
      }
      
      // 无论当前状态如何，总是停止当前播放
      this.stopAllPlayback();
      
      // 切换到播放状态
      console.log('开始播放语音:', content);
      this.isPlaying.value = 1;
      this.currentText.value = content;
      
      // 开始新的播放流程
      this.connectAndStartSynthesis();
      this.sendRunSynthesis(content);
      this.sendStopSynthesis();
      return true;
    } else {
      // 当前正在播放，需要停止
      console.log('停止当前语音播放');
      this.stopAllPlayback();
      this.isPlaying.value = 0;
      this.currentText.value = '';
      return true;
    }
  }
  
  /**
   * 暂停当前播放
   * @returns {boolean} 暂停操作是否成功
   */
  pauseVoice() {
    if (this.isPlaying.value === 1 && this.pauseAudioPlayback) {
      this.pauseAudioPlayback();
      this.isPlaying.value = 0;
      return true;
    }
    return false;
  }
  
  /**
   * 停止所有播放
   * @returns {boolean} 停止操作是否成功
   */
  stopVoice() {
    if (this.stopAllPlayback) {
      this.stopAllPlayback();
      this.isPlaying.value = 0;
      this.currentText.value = '';
      return true;
    }
    return false;
  }
  
  /**
   * 设置自定义播放结束回调
   * @param {Function} callback - 播放结束时调用的回调函数
   */
  setPlaybackEndCallback(callback) {
    if (this.setOnPlaybackEndCallback && typeof callback === 'function') {
      this.setOnPlaybackEndCallback(() => {
        // 重置状态
        this.isPlaying.value = 0;
        this.currentText.value = '';
        // 调用自定义回调
        callback();
      });
    }
  }
  
  /**
   * 获取当前播放状态
   * @returns {number} 播放状态：0-未播放，1-正在播放
   */
  getPlayingState() {
    return this.isPlaying.value;
  }
  
  /**
   * 获取当前正在播放的文本
   * @returns {string} 当前播放的文本
   */
  getCurrentPlayingText() {
    return this.currentText.value;
  }
}

// 创建并导出单例实例
export const voicePlayService = new VoicePlayService();

// 导出便捷方法
export const playVoice = (content, state = 0) => voicePlayService.playVoice(content, state);
export const pauseVoice = () => voicePlayService.pauseVoice();
export const stopVoice = () => voicePlayService.stopVoice();
export const getPlayingState = () => voicePlayService.getPlayingState();
export const getCurrentPlayingText = () => voicePlayService.getCurrentPlayingText();
export const setPlaybackEndCallback = (callback) => voicePlayService.setPlaybackEndCallback(callback); 