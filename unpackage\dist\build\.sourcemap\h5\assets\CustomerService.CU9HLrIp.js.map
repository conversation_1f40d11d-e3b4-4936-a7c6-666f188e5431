{"version": 3, "file": "CustomerService.CU9HLrIp.js", "sources": ["../../../../../utils/eventBus.js", "../../../../../components/CustomerService.vue"], "sourcesContent": null, "names": ["eventBus", "reactive", "events", "on", "eventName", "callback", "this", "push", "emit", "args", "for<PERSON>ach", "off", "filter", "cb", "props", "__props", "isPc", "computed", "systemInfo", "uni.getSystemInfoSync", "platform", "windowWidth", "includes", "e", "console", "error", "test", "navigator", "userAgent", "startX", "ref", "startY", "currentX", "currentY", "isDragging", "dragDistance", "style", "position", "bottom", "right", "width", "height", "cursor", "isPcStyle", "goToChat", "navigateTo", "url", "fail", "err", "switchTab", "switchErr", "goPCToChat", "value", "onTouchStart", "event", "touches", "clientX", "clientY", "onTouchMove", "moveX", "moveY", "newRight", "parseInt", "replace", "newBottom", "window", "innerWidth", "document", "documentElement", "clientWidth", "body", "windowHeight", "innerHeight", "clientHeight", "componentWidth", "componentHeight", "onTouchEnd", "savePosition", "uni.setStorageSync", "JSON", "stringify", "updatePosition", "startDrag", "button", "preventDefault", "stopPropagation", "addEventListener", "onDrag", "passive", "stopDrag", "Math", "abs", "isClick", "removeEventListener", "onMounted", "positionStr", "uni.getStorageSync", "parse", "onUnmounted"], "mappings": "yQAEY,MAACA,EAAWC,EAAS,CAE/BC,OAAQ,CAAE,EAGVC,GAAGC,EAAWC,GACPC,KAAKJ,OAAOE,KACVE,KAAAJ,OAAOE,GAAa,IAE3BE,KAAKJ,OAAOE,GAAWG,KAAKF,EAC7B,EAGDG,KAAKJ,KAAcK,GACbH,KAAKJ,OAAOE,IACTE,KAAAJ,OAAOE,GAAWM,YAAoBL,KAAYI,IAE1D,EAGDE,IAAIP,EAAWC,GACTC,KAAKJ,OAAOE,KACVC,EACGC,KAAAJ,OAAOE,GAAaE,KAAKJ,OAAOE,GAAWQ,QAAaC,GAAAA,IAAOR,WAE7DC,KAAKJ,OAAOE,GAGxB,wECFH,MAAMU,EAAQC,EAORC,EAAOC,GAAS,KAEhB,IACF,MAAMC,EAAaC,IAKZ,MAH8B,YAAxBD,EAAWE,UACQ,QAAxBF,EAAWE,UACVF,EAAWG,YAAc,MAAQH,EAAWE,SAASE,SAAS,SAAWJ,EAAWE,SAASE,SAAS,UAMhH,OAJQC,GAGP,OAFQC,QAAAC,MAAM,YAAaF,IAEnB,iEAAiEG,KAAKC,UAAUC,UACzF,KA+BGC,EAASC,EAAI,GACbC,EAASD,EAAI,GACbE,EAAWF,EAAI,GACfG,EAAWH,EAAI,GAEfI,EAAaJ,GAAI,GAEjBK,EAAeL,EAAI,GAEnBM,EAAQN,EAAI,CAChBO,SAAU,QACVC,OAAQxB,EAAMwB,QAAU,QACxBC,MAAO,OACPC,MAAO,OACPC,OAAQ,OACRC,OAAQ,SAEJC,EAAYb,EAAI,CACpBO,SAAU,QACVC,OAAQ,OACRC,MAAO,OACPC,MAAO,OACPC,OAAQ,OACRC,OAAQ,SAaJE,EAAW,KACAC,EAAA,CACbC,IAAK,iBACLC,KAAOC,IACGxB,QAAAC,MAAM,aAAcuB,GAEdC,EAAA,CACZH,IAAK,iBACLC,KAAOG,IACG1B,QAAAC,MAAM,kBAAmByB,EAAS,GAE7C,GAEJ,EAGGC,EAAa,KACK,GAAlBjB,EAAWkB,OAAgBjB,EAAaiB,MAAQ,GACjCP,EAAA,CACfC,IAAK,kBAER,EAIGO,EAAgBC,IACpBzB,EAAOuB,MAAQE,EAAMC,QAAQ,GAAGC,QAChCzB,EAAOqB,MAAQE,EAAMC,QAAQ,GAAGE,OAAA,EAI5BC,EAAeJ,IACnB,MAAMK,EAAQL,EAAMC,QAAQ,GAAGC,QAAU3B,EAAOuB,MAC1CQ,EAAQN,EAAMC,QAAQ,GAAGE,QAAU1B,EAAOqB,MAG5C,IAAAS,EAAWC,SAAS1B,EAAMgB,MAAMb,MAAMwB,QAAQ,KAAM,KAAOJ,EAC3DK,EAAYF,SAAS1B,EAAMgB,MAAMd,OAAOyB,QAAQ,KAAM,KAAOH,EAGjE,MAAMvC,EAAc4C,OAAOC,YAAcC,SAASC,gBAAgBC,aAAeF,SAASG,KAAKD,YACzFE,EAAeN,OAAOO,aAAeL,SAASC,gBAAgBK,cAAgBN,SAASG,KAAKG,aAG5FC,EAAiBZ,SAAS1B,EAAMgB,MAAMZ,MAAMuB,QAAQ,KAAM,KAC1DY,EAAkBb,SAAS1B,EAAMgB,MAAMX,OAAOsB,QAAQ,KAAM,KAI9DF,EAAW,IACFA,EAAA,GAGTA,EAAWxC,EAAcqD,IAC3Bb,EAAWxC,EAAcqD,GAGvBV,EAAY,IACFA,EAAA,GAGVA,EAAYO,EAAeI,IAC7BX,EAAYO,EAAeI,GAI7B3C,EAASoB,MAAQS,EACjB5B,EAASmB,MAAQY,EAEjB5B,EAAMgB,MAAQ,IACThB,EAAMgB,MACTb,MAAO,GAAGsB,MACVvB,OAAQ,GAAG0B,OAGbnC,EAAOuB,MAAQE,EAAMC,QAAQ,GAAGC,QAChCzB,EAAOqB,MAAQE,EAAMC,QAAQ,GAAGE,OAAA,EAI5BmB,EAAa,SAKjB,MAAMvC,EAAW,CACfE,MAAOP,EAASoB,OAASU,SAAS1B,EAAMgB,MAAMb,MAAMwB,QAAQ,KAAM,KAClEzB,OAAQL,EAASmB,OAASU,SAAS1B,EAAMgB,MAAMd,OAAOyB,QAAQ,KAAM,MAE7D/D,EAAAQ,KAhGmB,mCAgGS6B,EAAQ,EAIzCwC,EAAe,KACf,IACF,MAAMxC,EAAW,CACfE,MAAOP,EAASoB,OAASU,SAAS1B,EAAMgB,MAAMb,MAAMwB,QAAQ,KAAM,KAClEzB,OAAQL,EAASmB,OAASU,SAAS1B,EAAMgB,MAAMd,OAAOyB,QAAQ,KAAM,MAEtEe,EA5GyB,4BA4GgBC,KAAKC,UAAU3C,GAGzD,OAFQd,GACCC,QAAAC,MAAM,cAAeF,EAC9B,GAuBG0D,EAAkB5C,IAClBA,IACFD,EAAMgB,MAAQ,IACThB,EAAMgB,MACTb,MAAO,GAAGF,EAASE,UACnBD,OAAQ,GAAGD,EAASC,YAEtBN,EAASoB,MAAQf,EAASE,MAC1BN,EAASmB,MAAQf,EAASC,OAC3B,EAOG4C,EAAa5B,IAEI,IAAjBA,EAAM6B,SAEV7B,EAAM8B,iBACN9B,EAAM+B,kBAENnD,EAAWkB,OAAQ,EACnBjB,EAAaiB,MAAQ,EACrBvB,EAAOuB,MAAQE,EAAME,QACrBzB,EAAOqB,MAAQE,EAAMG,QAErBU,SAASmB,iBAAiB,YAAaC,EAAQ,CAAEC,SAAS,IACjDrB,SAAAmB,iBAAiB,UAAWG,GAAQ,EAOzCF,EAAUjC,IACd,IAAKpB,EAAWkB,MAAO,OAEvBE,EAAM8B,iBAEA,MAAAzB,EAAQL,EAAME,QAAU3B,EAAOuB,MAC/BQ,EAAQN,EAAMG,QAAU1B,EAAOqB,MAErCjB,EAAaiB,OAASsC,KAAKC,IAAIhC,GAAS+B,KAAKC,IAAI/B,GAE7C,IAAAC,EAAWC,SAASnB,EAAUS,MAAMb,MAAMwB,QAAQ,KAAM,KAAOJ,EAC/DK,EAAYF,SAASnB,EAAUS,MAAMd,OAAOyB,QAAQ,KAAM,KAAOH,EAErE,MAAMvC,EAAc4C,OAAOC,YAAcC,SAASC,gBAAgBC,aAAeF,SAASG,KAAKD,YACzFE,EAAeN,OAAOO,aAAeL,SAASC,gBAAgBK,cAAgBN,SAASG,KAAKG,aAE5FC,EAAiBZ,SAASnB,EAAUS,MAAMZ,MAAMuB,QAAQ,KAAM,KAC9DY,EAAkBb,SAASnB,EAAUS,MAAMX,OAAOsB,QAAQ,KAAM,KAGlEF,EAAW,IACFA,EAAA,GAGTA,EAAWxC,EAAcqD,IAC3Bb,EAAWxC,EAAcqD,GAGvBV,EAAY,IACFA,EAAA,GAGVA,EAAYO,EAAeI,IAC7BX,EAAYO,EAAeI,GAG7B3C,EAASoB,MAAQS,EACjB5B,EAASmB,MAAQY,EACjBrB,EAAUS,MAAQ,IACbT,EAAUS,MACbb,MAAO,GAAGsB,MACVvB,OAAQ,GAAG0B,OAEbnC,EAAOuB,MAAQE,EAAME,QACrBzB,EAAOqB,MAAQE,EAAMG,OAAA,EAOjBgC,EAAYnC,IAEV,MAAAsC,EAAUzD,EAAaiB,MAAQ,EAErClB,EAAWkB,OAAQ,EAEVe,SAAA0B,oBAAoB,YAAaN,GACjCpB,SAAA0B,oBAAoB,UAAWJ,GAEnCG,IAEHtC,EAAM8B,iBACN9B,EAAM+B,kBACP,SAIHS,GAAU,KA3HW,MACf,IACI,MAAAC,EAAcC,EArHK,6BAsHzB,GAAID,EAAa,CACT,MAAA1D,EAAW0C,KAAKkB,MAAMF,GAC5B3D,EAAMgB,MAAQ,IACThB,EAAMgB,MACTb,MAAO,GAAGF,EAASE,UACnBD,OAAQ,GAAGD,EAASC,YAEtBN,EAASoB,MAAQf,EAASE,MAC1BN,EAASmB,MAAQf,EAASC,MAC3B,CAGF,OAFQf,GACCC,QAAAC,MAAM,cAAeF,EAC9B,MAgHQvB,EAAAG,GAhPmB,mCAgPO8E,EAAc,IAInDiB,GAAY,KACDlG,EAAAW,IArPmB,mCAqPQsE,GAE3Bd,SAAA0B,oBAAoB,YAAaN,GACjCpB,SAAA0B,oBAAoB,UAAWJ,EAAQ"}