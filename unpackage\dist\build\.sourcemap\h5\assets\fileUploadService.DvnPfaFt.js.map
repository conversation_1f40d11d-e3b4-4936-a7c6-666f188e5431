{"version": 3, "mappings": ";;;;;;0UAyBO,SAASA,EAAoBC,EAAMC,EAAgB,IACtD,OAAOC,EAAQ,CACXC,IAAK,iBACLC,OAAQ,MACRJ,OACAK,QAASJ,GAEjB,CAaO,SAASK,EAAaN,EAAO,GAAIC,EAAgB,IACpD,OAAOC,EAAQ,CACXC,IAAK,yBACLC,OAAQ,OACRJ,OACAK,QAASJ,GAEjB,CChDA,MAAMM,MAAwBC,IAEjBC,EAAgB,CAI3BC,GAAI,sBACJC,GAAI,sBACJC,GAAI,oBACJC,IAAK,2CACLC,KAAM,oCACNC,GAAI,kBACJC,GAAI,yBACJC,GAAI,yBACJC,GAAI,yBACJC,GAAI,yBACJC,GAAI,yBACJC,GAAI,0BAyCCC,eAAeC,EAA0BC,GACxC,MAAAC,QAvCDH,eAA8BE,GAC/B,IAEF,GAAIA,EAAKE,QAAUnB,EAAkBoB,IAAIH,EAAKE,QAErC,OADCE,QAAAC,IAAI,eAAgBL,EAAKE,QAC1BnB,EAAkBuB,IAAIN,EAAKE,QAE9B,MAAAK,QAAYC,EAAqB,CACrCC,KAAMT,EAAKS,KACX9B,IAAKqB,EAAKE,OACVQ,KAAMV,EAAKU,KACXC,KAAMX,EAAKW,OAET,GAAa,IAAbJ,EAAIjB,KAMN,OALQc,QAAAC,IAAI,UAAWE,EAAI/B,MAEvBwB,EAAKE,QACPnB,EAAkB6B,IAAIZ,EAAKE,OAAQK,EAAI/B,KAAKqC,IAEvCN,EAAI/B,KAAKqC,GAERT,QAAAU,MAAM,UAAWP,EAAIQ,SACfC,EAAA,CACZC,MAAOV,EAAIQ,SAAW,SACtBG,KAAM,QASX,OANQJ,GACCV,QAAAU,MAAM,UAAWA,GACXE,EAAA,CACZC,MAAO,eACPC,KAAM,QAET,CACM,WACT,CAI6BC,CAAenB,GAC1C,OAAIC,EACK,IAAKD,EAAMoB,cAAenB,GAE5BD,CACT,CAGOF,eAAeuB,EAAYC,EAAKC,EAAQ,IACzC,IAEE,IAAAC,EAGAD,GAASA,EAAME,OAAS,GAC1BD,EAAQ,GAERD,EAAMG,SAAgB1B,IAEpB,IAAIC,EAAeD,EAAKoB,eACnBnB,GAAgBD,EAAKE,QAAUnB,EAAkBoB,IAAIH,EAAKE,UAC9CD,EAAAlB,EAAkBuB,IAAIN,EAAKE,SAE5CsB,EAAMG,KAAK,CACTC,QAAS5B,EAAKE,OACd2B,aAAc7B,EAAKU,KAAKoB,MAAM,KAAK,IAAM,OACzCV,cAAenB,EACf8B,gBAAiB/B,EAAKS,MACvB,IAGCa,GAAsB,KAAfA,EAAIU,QACbR,EAAMG,KAAK,CACTC,QAASN,EACTO,aAAc,SAKlBL,EAAQF,GAAO,KAGX,MAAAW,EAAS,CAAET,SACXU,QDtGH,SAAoB1D,EAAMC,EAAgB,IAC7C,OAAOC,EAAQ,CACXC,IAAK,mBACLC,OAAQ,OACRJ,OACAK,QAASJ,GAEjB,CC+F2B0D,CAAWF,GAC9B,GAAkB,IAAlBC,EAAS5C,KAOX,OANQc,QAAAC,IAAI,gBAAiB6B,GACVE,EAAA,UAAWF,EAAS1D,KAAK6D,SAG5CtD,EAAkBuD,QAEXJ,EAAS1D,KAAK6D,QAEPrB,EAAA,CACZC,MAAOiB,EAASnB,SAAW,SAC3BG,KAAM,QASX,OANQJ,GACCV,QAAAU,MAAM,UAAWA,GACXE,EAAA,CACZC,MAAO,eACPC,KAAM,QAET,CACM,WACT,CAQOpB,eAAeyC,IAChB,IACI,MAAAL,QDxGH,SAAqC1D,EAAMC,EAAgB,IAC9D,OAAOC,EAAQ,CACXC,IAAK,iBACLC,OAAQ,MACRJ,OACAK,QAASJ,GAEjB,CCiG2B+D,GACnB,GAAkB,IAAlBN,EAAS5C,KAAY,CACjB,MAAAmD,MAAEA,GAAUP,EAAS1D,KAIpB,OAFPkE,EAAmB,cAAeD,GAClCrC,QAAQC,IAAI,+CACLoC,CACR,CAOF,OANQ3B,GACCV,QAAAU,MAAM,UAAWA,GACXE,EAAA,CACZC,MAAO,oBACPC,KAAM,QAET,CACH,CCnJA,MAAMyB,EAAgBC,EAAI,IAEpBC,EAAwB,CAC5B,OAAO,QAAQ,OAAO,OACtB,OAAO,OAAO,OAAO,OACrB,OAAO,QAAQ,OAAO,QACtB,OAAO,QAAQ,OAAO,QAQxB,SAASC,EAAoBC,GAC3B,IAAKA,EAAiB,SAChB,MAAAC,EAAgB,IAAMD,EAASjB,MAAM,KAAKmB,MAAMC,cAC/C,OAAAL,EAAsBM,SAASH,EACxC,CAMO,SAASI,EAAeC,GAEdC,EAAA,CACbC,MAAO,EACP7C,KAAM,MACN8C,QAAS1D,MAAOS,IACd,MAAMgB,EAAQhB,EAAIkD,UAClB,GAAqB,IAAjBlC,EAAME,OAAc,OAEHF,EAAMmC,QAAO1D,IAAS8C,EAAoB9C,EAAKS,QACnDgB,OAAS,EACVT,EAAA,CACZC,MAAO,UACPC,KAAM,OACNyC,SAAU,OAMEC,EAAA,CACd3C,MAAO,OAAOM,EAAME,uBAoBrB3B,eAAmCyB,GACxC,IAAIsC,EAAe,EACfC,EAAY,EAGhB,MAAMC,EAAiB,KACrB,MAAMC,EAAQzC,EAAME,OAEJmC,EAAA,CACd3C,MAAO,OAFS4C,EAAeC,KAEJE,MAC5B,EAIGC,EAAkB,EACxB,IAAIC,EAAgB,EAChBC,EAAY,EAET,WAAIC,SAASC,IAElB,MAAMC,EAAexE,UAEnB,GAAIqE,GAAa5C,EAAME,QAA4B,IAAlByC,EAc/B,WAXgBlD,EADE,IAAd8C,EACY,CACZ7C,MAAO,KAAK4C,WACZ3C,KAAM,WAGM,CACZD,MAAO,GAAG4C,OAAkBC,MAC5B5C,KAAM,cAGVmD,EAAQ1B,EAAc4B,OAIxB,KAAOJ,EAAY5C,EAAME,QAAUyC,EAAgBD,GAAiB,CAClE,MAAMO,EAAeL,IACfnE,EAAOuB,EAAMiD,GACnBN,QAMiBO,EAAAzE,GACd0E,MAAK,KACJb,GAAA,IAEDc,OAAM,KACLb,GAAA,IAEDc,SAAQ,KACPV,cAKL,SAMP,CAnFYW,CAAoBtD,GAGF,mBAAb8B,GACTA,EAASV,EAAc4B,OACxB,GAIP,CAiFOzE,eAAe2E,EAAiBzE,GACjC,IAEF,IAAK8C,EAAoB9C,EAAKS,MACtB,UAAIqE,MAAM,aAAa9E,EAAKS,WAAWoC,EAAsBkC,KAAK,WAG1E,MAAMC,EAAchF,EAAKU,MAAQuE,EAAYjF,EAAKS,MAG5ChC,EAAgB,CACpB,eAAgBuG,GAIZjC,EAAW/C,EAAKS,KAGhBF,QAAY2E,EAChB,CAEEC,UAAWC,KAAKC,MAAQ,IAAMC,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,GAAKH,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,GAAKC,EAAiB3C,GACzIlB,aAAcmD,GAEhBvG,GAGE,GAAa,IAAb8B,EAAIjB,KACA,UAAIwF,MAAM,YAId,IAAAa,EAKFA,EAJE3F,EAAKU,KAAKkF,WAAW,UACrB5F,EAAKS,KAAKyC,cAAc2C,SAAS,SACjC7F,EAAKS,KAAKyC,cAAc2C,SAAS,SAExB,IAAIC,KAAK,OAAO9F,EAAK+F,eAAgB,CAACrF,KAAMV,EAAKU,OAGjDV,EAIb,MAAMgG,QAAuBC,MAAM1F,EAAI/B,KAAK0H,SAAU,CACpDtH,OAAQ,MACRuH,KAAMR,EACN9G,QAAS,CACP,eAAgBmG,KAIhB,IAACgB,EAAeI,GAClB,MAAM,IAAItB,MAAM,SAASkB,EAAeK,UAG1CjG,QAAQC,IAAI,UAAWE,EAAI/B,KAAMwH,GAG3B,MAAAM,QAA6BC,EAA8B,CAC/DrG,OAAQK,EAAI/B,KAAK0B,SAGXE,QAAAC,IAAI,YAAaiG,GAGzB,IAAIE,EAAa,GACiB,IAA9BF,EAAqBhH,MAAcgH,EAAqB9H,MAAQ8H,EAAqB9H,KAAK0H,UAC5FM,EAAaF,EAAqB9H,KAAK0H,SAC/B9F,QAAAC,IAAI,mBAAoBmG,KAGnBA,EAAAC,IAAIC,gBAAgB1G,GACzBI,QAAAuG,KAAK,yBAA0BH,IAIzC,MAAMI,EAAW,CACf/F,GAAIuE,KAAKC,MAAQC,KAAKC,SAASC,SAAS,IAAIqB,OAAO,EAAG,GACtDpG,KAAMT,EAAKS,KACXqG,SAAU/D,EACVpE,IAAK4B,EAAI/B,KAAKG,KAAO4B,EAAI/B,KAAKuI,IAC9B7G,OAAQK,EAAI/B,KAAK0B,OACjBkB,cAAeb,EAAI/B,KAAK4C,eAAiB,KACzCV,KAAMV,EAAKU,MAAQ,2BACnBC,KAAMX,EAAKW,KACX0F,OAAQ,UACRW,QAASR,EACTS,gBAA+C,IAA9BX,EAAqBhH,KAAagH,EAAqB9H,KAAK0H,SAAW,IAIpFgB,QAAwBnH,EAA0B6G,GAIjD,OADOjE,EAAA4B,MAAM5C,KAAKuF,GAClBA,CAIR,OAHQC,GAEA,OADP/G,QAAQU,MAAM,MAAMd,EAAKS,aAAc0G,GAChC/C,QAAQgD,OAAOD,EACvB,CACH,CAMO,SAASE,EAAmBC,GACjC,GAAIA,GAAS,GAAKA,EAAQ3E,EAAc4B,MAAM9C,OAAQ,CAC9C,MAAAzB,EAAO2C,EAAc4B,MAAM+C,GAE7BtH,EAAKgH,SAAWhH,EAAKgH,QAAQpB,WAAW,UACtCa,IAAAc,gBAAgBvH,EAAKgH,SAEbrE,EAAA4B,MAAMiD,OAAOF,EAAO,EACnC,CACH,CAMO,SAASG,IACd,OAAO9E,EAAc4B,KACvB,CAKO,SAASmD,IAEA/E,EAAA4B,MAAM7C,SAAgB1B,IAC9BA,EAAKgH,SAAWhH,EAAKgH,QAAQpB,WAAW,UACtCa,IAAAc,gBAAgBvH,EAAKgH,QAC1B,IAEHrE,EAAc4B,MAAQ,GDrJtBxF,EAAkBuD,OCwJpB,CAcOxC,eAAe6H,EAAY3H,EAAM4H,EAAU,IAE5C,IAAC5H,IAAUA,EAAKrB,MAAQqB,EAAKgH,UAAYhH,EAAKiH,gBAKhD,YAJcjG,EAAA,CACZC,MAAO,eACPC,KAAM,SAMV,MAAM8B,EAAgBhD,EAAKS,KAAQ,IAAMT,EAAKS,KAAKqB,MAAM,KAAKmB,MAAMC,cAAiB,GAGrF,IAAIsD,EAAa,GAab,GAZAxG,EAAKiH,iBACPT,EAAaxG,EAAKiH,gBACV7G,QAAAC,IAAI,eAAgBmG,IACnBxG,EAAKgH,SACdR,EAAaxG,EAAKgH,QACV5G,QAAAC,IAAI,eAAgBmG,KAE5BA,EAAaxG,EAAKrB,IACVyB,QAAAC,IAAI,cAAemG,IAIzB,CAAC,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,QAAS,QAAQrD,SAASH,GAetE,OAdQ5C,QAAAC,IAAI,UAAWmG,QACNqB,EAAA,CACfC,KAAM,CAACtB,GACPuB,QAASvB,EACTwB,UAAW,SACXC,MAAM,EACNC,KAAOf,IACG/G,QAAAU,MAAM,UAAWqG,GACXnG,EAAA,CACZC,MAAO,SACPC,KAAM,QACP,IAQH,IACI,MAAAiH,uBAAEA,SAAiCC,GAAA,IAAAC,OAAO,kDAA8B,0BAE1E,OAAAF,EAAuBnI,EAAKS,OACtBL,QAAAC,IAAI,yBAA0BL,EAAKS,MAGvCmH,EAAQU,oBAA4D,mBAA/BV,EAAQU,wBAC/CV,EAAQU,mBAAmBtI,QAKfgB,EAAA,CACZC,MAAO,cACPC,KAAM,UACNyC,SAAU,QAKJvD,QAAAC,IAAI,0BAA2BL,EAAKS,WACrC8H,OAAAC,KAAKhC,EAAY,UAO3B,OAJQ1F,GAGP,OAFQV,QAAAuG,KAAK,2BAA4B7F,QAClCyH,OAAAC,KAAKhC,EAAY,SAEzB,CAwBaiC,EAAA,CACZxH,MAAO,OACPW,QAAS,WAAWoB,sBACpB0F,YAAa,KACbC,WAAY,KACZnF,QAAUjD,IACJA,EAAIqI,SAEQ5H,EAAA,CACZC,MAAO,aACPC,KAAM,QAET,GAGP", "names": ["getChatHistoryList", "data", "customHeaders", "request", "url", "method", "headers", "clearContext", "fileAttachmentMap", "Map", "tableTagStyle", "ol", "ul", "li", "pre", "code", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "async", "saveAttachmentAfterUpload", "file", "attachmentId", "object", "has", "console", "log", "get", "res", "saveOrEditAttachment", "name", "type", "size", "set", "id", "error", "message", "showToast", "title", "icon", "saveAttachment", "attachment_id", "fetchTaskId", "msg", "files", "query", "length", "for<PERSON>ach", "push", "content", "content_type", "split", "attachment_name", "trim", "params", "response", "createTask", "setStorageSync", "task_id", "clear", "storeSpeechRecognitionToken", "fetchSpeechRecognitionToken", "token", "uni.setStorageSync", "uploadedFiles", "ref", "allowedFileExtensions", "isFileFormatAllowed", "fileName", "fileExtension", "pop", "toLowerCase", "includes", "openFilePicker", "callback", "chooseFile", "count", "success", "tempFiles", "filter", "duration", "showLoading", "successCount", "failCount", "updateProgress", "total", "concurrentLimit", "activeUploads", "fileIndex", "Promise", "resolve", "processQueue", "value", "currentIndex", "uploadSingleFile", "then", "catch", "finally", "uploadMultipleFiles", "Error", "join", "contentType", "getMimeType", "generateFileUploadSignature", "file_name", "Date", "now", "Math", "random", "toString", "substring", "getFileExtension", "fileData", "startsWith", "endsWith", "Blob", "arrayBuffer", "uploadResponse", "fetch", "sign_url", "body", "ok", "status", "downloadSignatureRes", "generateFileDownloadSignature", "previewUrl", "URL", "createObjectURL", "warn", "fileInfo", "substr", "filename", "cdn", "tempUrl", "downloadSignUrl", "updatedFileInfo", "err", "reject", "removeUploadedFile", "index", "revokeObjectURL", "splice", "getUploadedFiles", "clearUploadedFiles", "previewFile", "options", "previewImage", "urls", "current", "indicator", "loop", "fail", "isSupportedByWebOffice", "__vitePreload", "import", "onWebOfficePreview", "window", "open", "showModal", "confirmText", "cancelText", "confirm"], "ignoreList": [], "sources": ["../../../../../http/ai-chat-task.js", "../../../../../utils/aiChatUtils.js", "../../../../../services/fileUploadService.js"], "sourcesContent": [null, null, null], "file": "assets/fileUploadService.DvnPfaFt.js"}