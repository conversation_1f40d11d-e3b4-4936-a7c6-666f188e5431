<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 插件完整测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-panel {
            background: #f8f9fa;
            padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .status-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .status-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 1.1em;
            font-weight: bold;
        }
        
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .status-loading { color: #007bff; }
        
        .test-section {
            padding: 40px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-header {
            margin-bottom: 30px;
        }
        
        .test-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-desc {
            color: #666;
            font-size: 1.1em;
        }
        
        .test-content {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            border: 1px solid #e9ecef;
        }
        
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        
        .debug-section {
            background: #f8f9fa;
            padding: 30px;
        }
        
        .debug-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .debug-title {
            font-size: 1.5em;
            color: #333;
        }
        
        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .clear-btn:hover {
            background: #c82333;
        }
        
        .debug-logs {
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        
        .log-item {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .log-info {
            background: #e6f7ff;
            border-left-color: #1890ff;
        }
        
        .log-success {
            background: #f6ffed;
            border-left-color: #52c41a;
        }
        
        .log-error {
            background: #fff2f0;
            border-left-color: #ff4d4f;
        }
        
        .log-warn {
            background: #fffbe6;
            border-left-color: #faad14;
        }
        
        .log-time {
            color: #999;
            margin-right: 10px;
        }
        
        .actions {
            padding: 30px;
            display: flex;
            gap: 20px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(240, 147, 251, 0.3);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🧪 Mermaid 插件完整测试</h1>
            <p>测试 Mermaid 图表在不同环境下的渲染效果</p>
        </div>
        
        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Mermaid 库状态:</div>
                    <div class="status-value" id="mermaid-status">
                        <span class="loading"></span> 检查中...
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-label">当前环境:</div>
                    <div class="status-value" id="environment">H5 浏览器</div>
                </div>
                <div class="status-item">
                    <div class="status-label">测试时间:</div>
                    <div class="status-value" id="test-time">-</div>
                </div>
                <div class="status-item">
                    <div class="status-label">渲染图表数:</div>
                    <div class="status-value" id="chart-count">0</div>
                </div>
            </div>
        </div>
        
        <!-- 测试用例 1: 基本流程图 -->
        <div class="test-section">
            <div class="test-header">
                <h2 class="test-title">📊 测试 1 - 基本流程图</h2>
                <p class="test-desc">简单的用户登录流程图</p>
            </div>
            <div class="test-content">
                <div class="mermaid">
flowchart TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[显示登录页]
    C --> E[用户操作]
    D --> F[用户登录]
    F --> C
    E --> G[结束]
                </div>
            </div>
        </div>
        
        <!-- 测试用例 2: 带点击事件的流程图 -->
        <div class="test-section">
            <div class="test-header">
                <h2 class="test-title">🔗 测试 2 - 带点击事件的流程图</h2>
                <p class="test-desc">销售流程图，包含可点击的节点</p>
            </div>
            <div class="test-content">
                <div class="mermaid">
flowchart TD
    A[客户咨询] --> B[需求分析]
    B --> C{是否匹配产品?}
    C -->|是| D[产品介绍]
    C -->|否| E[定制方案]
    D --> F[报价]
    E --> F
    F --> G{客户满意?}
    G -->|是| H[签订合同]
    G -->|否| I[调整方案]
    I --> F
    H --> J[项目实施]
    J --> K[项目验收]
    K --> L[售后服务]
    
    click A "https://example.com/consult" "客户咨询入口"
    click H "https://example.com/contract" "合同管理"
    click L "https://example.com/service" "售后服务"
                </div>
            </div>
        </div>
        
        <!-- 测试用例 3: 时序图 -->
        <div class="test-section">
            <div class="test-header">
                <h2 class="test-title">⏱️ 测试 3 - 时序图</h2>
                <p class="test-desc">系统交互时序图</p>
            </div>
            <div class="test-content">
                <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 发起请求
    F->>B: 转发请求
    B->>D: 查询数据
    D-->>B: 返回数据
    B-->>F: 响应结果
    F-->>U: 显示结果
    
    Note over U,D: 完整的请求响应流程
                </div>
            </div>
        </div>
        
        <!-- 测试用例 4: 甘特图 -->
        <div class="test-section">
            <div class="test-header">
                <h2 class="test-title">📅 测试 4 - 甘特图</h2>
                <p class="test-desc">项目进度甘特图</p>
            </div>
            <div class="test-content">
                <div class="mermaid">
gantt
    title 项目开发进度
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求收集           :done,    des1, 2024-01-01,2024-01-05
    需求分析           :done,    des2, 2024-01-06,2024-01-10
    section 设计阶段
    UI设计            :done,    des3, 2024-01-11,2024-01-20
    架构设计           :active,  des4, 2024-01-15,2024-01-25
    section 开发阶段
    前端开发           :         des5, 2024-01-26,2024-02-15
    后端开发           :         des6, 2024-01-26,2024-02-20
    section 测试阶段
    单元测试           :         des7, 2024-02-16,2024-02-25
    集成测试           :         des8, 2024-02-21,2024-02-28
                </div>
            </div>
        </div>
        
        <!-- 测试用例 5: 类图 -->
        <div class="test-section">
            <div class="test-header">
                <h2 class="test-title">🏗️ 测试 5 - 类图</h2>
                <p class="test-desc">面向对象类关系图</p>
            </div>
            <div class="test-content">
                <div class="mermaid">
classDiagram
    class User {
        +String name
        +String email
        +String password
        +login()
        +logout()
        +updateProfile()
    }
    
    class Order {
        +String id
        +Date createTime
        +Double totalAmount
        +String status
        +createOrder()
        +cancelOrder()
        +payOrder()
    }
    
    class Product {
        +String id
        +String name
        +Double price
        +Integer stock
        +updateStock()
        +getDetails()
    }
    
    User ||--o{ Order : places
    Order ||--o{ Product : contains
    
    class Payment {
        +String id
        +Double amount
        +String method
        +Date payTime
        +processPayment()
    }
    
    Order ||--|| Payment : has
                </div>
            </div>
        </div>
        
        <!-- 调试日志 -->
        <div class="debug-section">
            <div class="debug-header">
                <h2 class="debug-title">📋 调试日志</h2>
                <button class="clear-btn" onclick="clearLogs()">清空日志</button>
            </div>
            <div class="debug-logs" id="debug-logs">
                <!-- 日志将在这里显示 -->
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
            <button class="action-btn primary" onclick="refreshTests()">🔄 刷新测试</button>
            <button class="action-btn secondary" onclick="exportLogs()">📤 导出日志</button>
        </div>
    </div>
    
    <!-- 引入 Mermaid 库 -->
    <script src="./static/js/mermaid.min.js"></script>
    <script>
        // 全局变量
        let logs = [];
        let chartCount = 0;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成', 'info');
            initializeTest();
        });
        
        // 初始化测试
        function initializeTest() {
            addLog('初始化测试环境', 'info');
            
            // 更新时间
            updateTime();
            
            // 检查 Mermaid 状态
            checkMermaidStatus();
            
            // 初始化 Mermaid
            initializeMermaid();
        }
        
        // 更新时间
        function updateTime() {
            const now = new Date();
            document.getElementById('test-time').textContent = now.toLocaleString();
        }
        
        // 检查 Mermaid 状态
        function checkMermaidStatus() {
            const statusElement = document.getElementById('mermaid-status');
            
            if (typeof mermaid !== 'undefined') {
                statusElement.innerHTML = '<span class="status-success">✅ 已加载</span>';
                addLog('Mermaid 库检测成功', 'success');
            } else {
                statusElement.innerHTML = '<span class="status-error">❌ 未检测到</span>';
                addLog('Mermaid 库未检测到', 'error');
            }
        }
        
        // 初始化 Mermaid
        function initializeMermaid() {
            if (typeof mermaid === 'undefined') {
                addLog('Mermaid 库未加载，尝试从 CDN 加载', 'warn');
                loadMermaidFromCDN();
                return;
            }
            
            try {
                // 配置 Mermaid
                mermaid.initialize({
                    startOnLoad: true,
                    theme: 'default',
                    securityLevel: 'loose',
                    flowchart: {
                        useMaxWidth: true,
                        htmlLabels: true
                    },
                    sequence: {
                        diagramMarginX: 50,
                        diagramMarginY: 10,
                        actorMargin: 50,
                        width: 150,
                        height: 65,
                        boxMargin: 10,
                        boxTextMargin: 5,
                        noteMargin: 10,
                        messageMargin: 35,
                        mirrorActors: true,
                        bottomMarginAdj: 1,
                        useMaxWidth: true,
                        rightAngles: false,
                        showSequenceNumbers: false
                    },
                    gantt: {
                        titleTopMargin: 25,
                        barHeight: 20,
                        fontFamily: '"Open-Sans", "sans-serif"',
                        fontSize: 11,
                        fontWeight: 'normal',
                        gridLineStartPadding: 35,
                        leftPadding: 75,
                        topPadding: 50,
                        bottomPadding: 5
                    }
                });
                
                addLog('Mermaid 配置完成', 'success');
                
                // 渲染所有图表
                renderAllCharts();
                
            } catch (error) {
                addLog(`Mermaid 初始化失败: ${error.message}`, 'error');
            }
        }
        
        // 从 CDN 加载 Mermaid
        function loadMermaidFromCDN() {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
            script.onload = function() {
                addLog('从 CDN 加载 Mermaid 成功', 'success');
                initializeMermaid();
            };
            script.onerror = function() {
                addLog('从 CDN 加载 Mermaid 失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        // 渲染所有图表
        function renderAllCharts() {
            const mermaidElements = document.querySelectorAll('.mermaid');
            chartCount = mermaidElements.length;
            
            addLog(`发现 ${chartCount} 个 Mermaid 图表`, 'info');
            document.getElementById('chart-count').textContent = chartCount;
            
            mermaidElements.forEach((element, index) => {
                try {
                    addLog(`开始渲染图表 ${index + 1}`, 'info');
                    
                    // 为每个图表生成唯一ID
                    const chartId = `mermaid-chart-${index}`;
                    element.id = chartId;
                    
                    // 渲染图表
                    mermaid.render(chartId + '-svg', element.textContent, function(svgCode) {
                        element.innerHTML = svgCode;
                        addLog(`图表 ${index + 1} 渲染成功`, 'success');
                    });
                    
                } catch (error) {
                    addLog(`图表 ${index + 1} 渲染失败: ${error.message}`, 'error');
                }
            });
        }
        
        // 添加日志
        function addLog(message, type = 'info') {
            const now = new Date();
            const time = now.toLocaleTimeString();
            
            const log = {
                time: time,
                message: message,
                type: type
            };
            
            logs.unshift(log);
            
            // 限制日志数量
            if (logs.length > 100) {
                logs = logs.slice(0, 100);
            }
            
            // 更新日志显示
            updateLogDisplay();
            
            // 控制台输出
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 更新日志显示
        function updateLogDisplay() {
            const logsContainer = document.getElementById('debug-logs');
            logsContainer.innerHTML = '';
            
            logs.forEach(log => {
                const logElement = document.createElement('div');
                logElement.className = `log-item log-${log.type}`;
                logElement.innerHTML = `
                    <span class="log-time">${log.time}</span>
                    <span class="log-message">${log.message}</span>
                `;
                logsContainer.appendChild(logElement);
            });
        }
        
        // 清空日志
        function clearLogs() {
            logs = [];
            updateLogDisplay();
            addLog('日志已清空', 'info');
        }
        
        // 刷新测试
        function refreshTests() {
            addLog('刷新测试', 'info');
            clearLogs();
            initializeTest();
        }
        
        // 导出日志
        function exportLogs() {
            try {
                const logText = logs.map(log => `[${log.time}] ${log.message}`).join('\n');
                const blob = new Blob([logText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `mermaid-test-logs-${Date.now()}.txt`;
                a.click();
                URL.revokeObjectURL(url);
                addLog('日志已导出', 'success');
            } catch (error) {
                addLog(`导出失败: ${error.message}`, 'error');
            }
        }
        
        // 处理 Mermaid 点击事件
        document.addEventListener('click', function(event) {
            if (event.target.closest('.mermaid')) {
                const href = event.target.getAttribute('href');
                if (href && href.startsWith('http')) {
                    event.preventDefault();
                    addLog(`点击了链接: ${href}`, 'info');
                    if (confirm(`是否要打开链接: ${href}?`)) {
                        window.open(href, '_blank');
                    }
                }
            }
        });
    </script>
</body>
</html>