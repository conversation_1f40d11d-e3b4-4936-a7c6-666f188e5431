# 文件上传功能使用说明

基于 xe-upload 组件和自定义 hooks 的文件上传解决方案，支持 H5 和 APP 端。

## 功能特性

- ✅ 支持多平台（H5、APP、小程序）
- ✅ 支持多种文件类型（图片、视频、文档）
- ✅ 支持批量上传（最多9个文件）
- ✅ 智能平台适配（H5 用 PUT，APP 用 POST）
- ✅ 完整的错误处理和进度显示
- ✅ 基于 OSS 签名的安全上传

## 快速使用

### 1. 在页面中导入 hooks

```javascript
import { useFileUpload } from '@/utils/useFileUpload.js';

export default {
  setup() {
    const { 
      chooseAndUpload, 
      uploading, 
      uploadProgress, 
      setXeUploadRef, 
      handleXeUploadCallback 
    } = useFileUpload();
    
    // ... 其他代码
  }
}
```

### 2. 在模板中添加 xe-upload 组件

```vue
<template>
  <view>
    <!-- 你的上传按钮 -->
    <button @click="handleUpload">上传文件</button>
    
    <!-- 必须包含的 xe-upload 组件（隐藏） -->
    <xe-upload 
      ref="xeUpload" 
      @callback="handleXeUploadCallback"
    />
  </view>
</template>
```

### 3. 设置组件引用并调用上传

```javascript
import { ref, onMounted } from 'vue';

export default {
  setup() {
    const xeUpload = ref(null);
    
    // 组件挂载后设置引用
    onMounted(() => {
      if (xeUpload.value) {
        setXeUploadRef(xeUpload.value);
      }
    });
    
    // 上传处理函数
    const handleUpload = async () => {
      try {
        const results = await chooseAndUpload('file', {
          count: 9, // 最多选择9个文件
          extension: ['.pdf', '.doc', '.jpg'] // APP端支持的扩展名
        });
        
        if (results && results.length > 0) {
          console.log('上传成功:', results);
          // 处理上传结果...
        }
      } catch (error) {
        console.error('上传失败:', error);
      }
    };
    
    return {
      xeUpload,
      handleUpload,
      handleXeUploadCallback
    };
  }
}
```

## API 参考

### chooseAndUpload(type, options)

主要的上传方法，用于选择和上传文件。

#### 参数

- **type** (string): 文件类型
  - `'image'`: 图片
  - `'video'`: 视频  
  - `'file'`: 文件（通用）

- **options** (object): 配置选项
  - `count` (number): 最多选择文件数量，默认 1
  - `extension` (array): APP端支持的文件扩展名
  - `sizeType` (array): 图片尺寸类型 `['original', 'compressed']`
  - `sourceType` (array): 图片来源 `['album', 'camera']`

#### 返回值

Promise<Array>，包含上传成功的文件信息：

```javascript
[
  {
    url: "test/3/2025/06/1750317551992-f1qdbiycsx.jpg", // OSS对象路径
    fileName: "image.jpg", // 文件名
    cdn: "https://sale-agent.oss-cn-shenzhen.aliyuncs.com", // CDN地址
    fullUrl: "https://sale-agent.oss-cn-shenzhen.aliyuncs.com/test/3/2025/06/1750317551992-f1qdbiycsx.jpg" // 完整URL
  }
]
```

### 响应式状态

- **uploading** (ref<boolean>): 是否正在上传
- **uploadProgress** (ref<number>): 上传进度（0-100）

### 工具方法

- **setXeUploadRef(ref)**: 设置 xe-upload 组件引用
- **handleXeUploadCallback(e)**: 处理 xe-upload 组件回调

## 使用示例

### 示例1：简单文件上传

```javascript
const handleUpload = async () => {
  const results = await chooseAndUpload('file');
  console.log('上传结果:', results);
};
```

### 示例2：批量图片上传

```javascript
const handleUploadImages = async () => {
  const results = await chooseAndUpload('image', {
    count: 9,
    sizeType: ['compressed'],
    sourceType: ['album']
  });
  console.log('批量上传结果:', results);
};
```

### 示例3：指定文件类型（APP端）

```javascript
const handleUploadDocs = async () => {
  const results = await chooseAndUpload('file', {
    count: 5,
    extension: ['.pdf', '.doc', '.docx', '.xls', '.xlsx']
  });
  console.log('文档上传结果:', results);
};
```

## 平台差异

### H5端
- 使用 `fetch` + `PUT` 请求直接上传到 OSS 签名 URL
- 支持实时上传进度（受浏览器限制）

### APP端
- 使用 `uni.uploadFile` + `POST` 请求上传到 OSS
- 使用 FormData 携带签名参数
- 支持完整的上传进度追踪

### 小程序端
- 使用类似 APP 端的上传逻辑
- 通过 `uni.uploadFile` 实现

## 错误处理

所有错误都会被包装为带有描述信息的 Error 对象：

```javascript
try {
  const results = await chooseAndUpload('file');
} catch (error) {
  console.error('上传失败:', error.message);
  // 常见错误：
  // - "请先设置 xe-upload 组件引用"
  // - "选择文件失败"
  // - "获取上传签名失败"
  // - "上传失败: 500"
}
```

## 注意事项

1. **必须包含 xe-upload 组件**：即使组件是隐藏的，也必须在模板中包含
2. **正确设置引用**：在 `onMounted` 中调用 `setXeUploadRef`
3. **处理回调**：必须在模板中绑定 `@callback="handleXeUploadCallback"`
4. **文件扩展名限制**：`extension` 参数仅在 APP 端生效
5. **上传并发**：为了稳定性，文件会依次上传而不是并发上传

## 完整示例页面

参考 `pages/userInfo/related-documents/simple-upload-example.vue` 获取完整的使用示例。 