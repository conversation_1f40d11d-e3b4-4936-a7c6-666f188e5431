{"version": 3, "file": "pages-preview-weboffice-preview.DYlf-m7s.js", "sources": ["../../../../../pages/preview/weboffice-preview.vue"], "sourcesContent": null, "names": ["webofficeContainer", "ref", "isLoading", "isReady", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "loadingText", "fileInfo", "webOfficeInstance", "startPreview", "async", "value", "nextTick", "WebOfficePreview", "originalShowLoading", "showLoading", "message", "call", "init", "console", "log", "error", "retryPreview", "goBack", "window", "history", "length", "back", "navigateBack", "delta", "onMounted", "pages", "getCurrentPages", "options", "JSON", "parse", "decodeURIComponent", "id", "Error", "name", "onBeforeUnmount", "destroy", "warn"], "mappings": "iZAwDM,MAAAA,EAAqBC,EAAI,MACzBC,EAAYD,GAAI,GAChBE,EAAUF,GAAI,GACdG,EAAWH,GAAI,GACfI,EAAeJ,EAAI,IACnBK,EAAcL,EAAI,WAClBM,EAAWN,EAAI,CAAA,GAGrB,IAAIO,EAAoB,KA2CxB,MAAMC,EAAeC,UACnB,GAAKH,EAASI,OAAUX,EAAmBW,MAKvC,IACFT,EAAUS,OAAQ,EAClBP,EAASO,OAAQ,EACjBR,EAAQQ,OAAQ,EAChBL,EAAYK,MAAQ,mBAGdC,UAaAA,IAGcJ,EAAA,IAAIK,EAAiBb,EAAmBW,OAG5D,MAAMG,EAAsBN,EAAkBO,YAC5BP,EAAAO,YAAeC,IAC/BV,EAAYK,MAAQK,EACAF,EAAAG,KAAKT,EAAmBQ,EAAO,QAI/CR,EAAkBU,KAAKX,EAASI,OAGtCT,EAAUS,OAAQ,EAClBR,EAAQQ,OAAQ,EAEhBQ,QAAQC,IAAI,uBAQb,OANQC,GACCF,QAAAE,MAAM,mBAAoBA,GAElCnB,EAAUS,OAAQ,EAClBP,EAASO,OAAQ,EACJN,EAAAM,MAAQU,EAAML,SAAW,YACvC,MAnDCG,QAAQE,MAAM,YAmDf,EAMGC,EAAe,KACnBH,QAAQC,IAAI,gBAORG,EAAS,KAETC,OAAOC,QAAQC,OAAS,EAC1BF,OAAOC,QAAQE,OAEEC,EAAA,CACfC,MAAO,GAEV,SAwBHC,GAAU,KAER,MAAMC,EAAQC,IAERC,EADcF,EAAMA,EAAML,OAAS,GACbO,SAAW,GAKvC,GAHQd,QAAAC,IAAI,yBAA0Ba,IAGlCA,EAAQ1B,SA0BV,OAHAY,QAAQE,MAAM,cACdjB,EAASO,OAAQ,OACjBN,EAAaM,MAAQ,UAxBjB,IAKE,GAJJJ,EAASI,MAAQuB,KAAKC,MAAMC,mBAAmBH,EAAQ1B,WAC/CY,QAAAC,IAAI,aAAcb,EAASI,QAG9BJ,EAASI,MAAM0B,GACZ,MAAA,IAAIC,MAAM,UAGd,IAAC/B,EAASI,MAAM4B,KACZ,MAAA,IAAID,MAAM,YAUnB,OALQjB,GAIP,OAHQF,QAAAE,MAAM,cAAeA,GAC7BjB,EAASO,OAAQ,OACJN,EAAAM,MAAQU,EAAML,SAAW,WAEvC,CAMF,IAGHwB,GAAgB,KACdrB,QAAQC,IAAI,uBAzDS,MACrB,GAAIZ,EACE,IACFA,EAAkBiC,UACEjC,EAAA,KACpBW,QAAQC,IAAI,qBAGb,OAFQC,GACCF,QAAAuB,KAAK,eAAgBrB,EAC9B,CAGHnB,EAAUS,OAAQ,EAClBR,EAAQQ,OAAQ,EAChBP,EAASO,OAAQ,CAAA"}