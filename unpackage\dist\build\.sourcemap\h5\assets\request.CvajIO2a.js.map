{"version": 3, "file": "request.CvajIO2a.js", "sources": ["../../../../../utils/env.js", "../../../../../utils/request.js"], "sourcesContent": null, "names": ["apiUrl", "apiPrefix", "currentEnv", "window", "location", "href", "includes", "hostname", "console", "error", "envConfig", "baseURL", "_showError", "msg", "showToast", "icon", "duration", "title", "service", "options", "url", "env", "token", "uni.getStorageSync", "header", "accept", "Authorization", "Promise", "resolved", "rejected", "success", "res", "statusCode", "data", "code", "_a", "_b", "reLaunch", "_c", "fail", "err", "timer", "setTimeout", "Error", "complete", "clearTimeout", "uni.request"], "mappings": "uEAAA,IAAIA,EAAQC,EAGZ,MAYMC,EATAC,OAAOC,SAASC,KAAKC,SAAS,cAC9BH,OAAOC,SAASG,SAASD,SAAS,SAClCH,OAAOC,SAASG,SAASD,SAAS,WAC7B,OAGF,aAITE,QAAQC,MAAM,QAASP,GAGJ,gBAAfA,GAA+C,SAAfA,GACzBF,EAAA,gCACGC,EAAA,OACY,eAAfC,IACAF,EAAA,uBAGX,MAAeU,EAAA,CACbC,QAASX,EACTC,YACAC,cC1BF,SAASU,EAAWC,GACFC,EAAA,CACZC,KAAM,OACNC,SAAU,IACVC,MAAOJ,GAEX,CAGF,SAASK,EAAQC,EAAU,IAGjBA,EAAAC,IAA8B,GAAGC,EAAIV,UAAUQ,EAAQC,MAGzD,MAAAE,EAAQC,EAAmB,SAUjC,OARAJ,EAAQK,OAAS,CACf,eAAgB,mBAEhBC,OAAU,sBACNH,EAAQ,CAAEI,cAAe,GAAGJ,KAAY,CAAC,KAC1CH,EAAQK,QAGN,IAAIG,SAAQ,CAACC,EAAUC,KACpBV,EAAAW,QAAWC,cACb,GAAmB,MAAnBA,EAAIC,WAAoB,CAMtB,GAJkB,IAAlBD,EAAIE,KAAKC,MACAtB,GAAA,OAAAuB,EAAIJ,EAAAE,WAAM,EAAAE,EAAAtB,MAAO,QAGR,MAAlBkB,EAAIE,KAAKC,KAUX,OANWtB,GAAA,OAAAwB,EAAIL,EAAAE,WAAM,EAAAG,EAAAvB,MAAO,iBAEfwB,EAAA,CACXjB,IAAK,mBAKTQ,EAASG,EAAIE,KAAI,MACW,MAAnBF,EAAIC,YACLxB,QAAAC,MAAM,SAAUsB,GACxBnB,EAAW,iBACXiB,EAASE,KAEDvB,QAAAC,MAAM,QAASsB,GACvBnB,GAAW,OAAA0B,IAAIL,WAAJ,EAAAK,EAAUzB,MAAO,SAASkB,EAAIC,cACzCH,EAASE,GACX,EAGMZ,EAAAoB,KAAQC,IACNhC,QAAAC,MAAM,UAAW+B,GAgBzB5B,EAAW,gBAEXiB,EAASW,EAAG,EAIR,MAAAC,EAAQC,YAAW,KACvB9B,EAAW,QACFiB,EAAA,IAAIc,MAAM,WAAU,GAC5B,MACKxB,EAAAyB,SAAW,IAAMC,aAAaJ,GACtCK,EAAY3B,EAAO,GAEvB"}