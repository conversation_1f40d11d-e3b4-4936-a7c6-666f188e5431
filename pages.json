{
	"pages": [
		// pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path" : "pages/ai-chat",
			"style": {
				"navigationBarTitleText" : "Ivy - 下一代销售Agent",
				"navigationStyle": "custom",
				"disableScroll": true,
				// 在 iOS 设备中，默认会启用 bounce 效果，即当用户滚动到页面的边缘时，页面会出现回弹的效果。bounce: none 禁用这种回弹效果，使页面在滚动时没有边缘回弹的动作，提供更加平滑的用户体验。
				"app-plus":{
					"bounce":"none"
				}
			}
		},
		{
			"path": "pages/tabBar/todo/todo",
			"style": {
				"navigationBarTitleText": "待办清单",
				"disableScroll":true,
				"navigationStyle": "custom",
				"app-plus":{
				   "bounce":"none"
				}
			}
		},
		{
			"path": "pages/tabBar/statistics/statistics",
			"style": {
				"navigationBarTitleText": "数据统计",
				"navigationStyle": "custom",
				"app-plus":{
					"bounce":"none"
				}
			}
		},
		{
			"path": "pages/tabBar/customer/customer",
			"style": {
				"navigationBarTitleText": "客户列表",
				"navigationStyle": "custom",
				"app-plus":{
					"bounce":"none"
				}
			}
		},
		{
			"path": "pages/tabBar/business/business",
			"style": {
				"navigationBarTitleText": "商机列表",
				"navigationStyle": "custom",
				"disableScroll": true,
				// iOS 端由于存在bounce效果，滑动体验略差，建议禁止bounce效果，禁止方式如下：
				"app-plus":{
					"bounce":"none"
					// 这个配置控制了软键盘弹出时，页面的布局如何调整。在某些设备上，默认情况下，当键盘弹出时，页面可能会被遮挡。adjustResize 设置意味着键盘出现时，页面布局会自动调整以避免被遮挡，通常是将页面内容向上移动，让用户可以看到输入框和其他交互元素。
					// "softinputMode": "adjustResize"
				}
			}
		},
		{
			"path": "pages/tabBar/more/more",
			"style": {
				"navigationBarTitleText": "ProWise",
				"navigationStyle": "custom",
				"disableScroll": true,
				"app-plus":{
					"bounce":"none"
				}
			}
		},
		{
			"path" : "pages/ai-task",
			"style":
			{
				"navigationBarTitleText" : "任务进度",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/ppt-editor/index",
			"style": {
				// "navigationBarTitleText": "PPT编辑器",
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none"
				}
			}
		},
	    {
            "path" : "pages/demo/index",
            "style" : {
                "navigationBarTitleText" : "日志"
            }
        }
	],
	// 分包加载配置，此配置为小程序的分包加载机制。
	"subPackages": [
		{
			"root": "pages/error",
			"pages": [
				{
					"path" : "401",
					"style" : {
						"navigationBarTitleText" : "401"
					}
				},
				{
					"path": "404",
					"style": {
						"navigationBarTitleText": "Not Found"
					}
				}
			]
		},
		{
			"root": "pages/template",
			"pages": [
				{
					"path" : "customer-details/index",
					"style": {
						"navigationBarTitleText": "客户详情",
						"navigationStyle": "custom",
						"disableScroll":true
					}
				},
				{
					"path" : "todo-details/index",
					"style": {
						"navigationBarTitleText": "待办详情",
						"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none",
						    "disableScroll":true
						}
					}
				},
				{
					"path" : "business-detail/index",
					"style": {
						"navigationBarTitleText": "商机详情",
						"navigationStyle": "custom",
						"disableScroll":true
					}
				}
			]
		},
		{
			"root": "pages/userInfo",
			"pages": [
				{
					"path": "user/user-settings",
					"style": {
						"navigationBarTitleText": "个人信息设置",
						"navigationStyle": "custom",
						"app-plus":{
						   "bounce":"none"
						}
					}
				},
				{
					"path": "related-documents/index",
					"style": {
						"navigationBarTitleText": "相关文件",
						"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "global-search/index",
					"style": {
						"navigationBarTitleText": "全局搜索",
						"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
					}
				}
			]
		},
		{
			"root": "pages/preview",
			"pages": [
				{
					"path": "weboffice-preview",
					"style": {
						"navigationBarTitleText": "文档预览",
						"navigationStyle": "custom"
					}
				}
			]
		}
	],
	"globalStyle": {
		// "navigationBarTextStyle": "black",
		// "navigationBarTitleText": "uni-app",
		// "navigationBarBackgroundColor": "#EDEDED",
		// "backgroundColor": "#F8F8F8",
		// "navigationStyle": "custom",
		// "app-plus": {
		//    "titleNView": false
		// }
	},
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "black",
		"borderStyle": "black",
		"backgroundColor": "#F8F9FA",
		"list": [
			{
				"pagePath": "pages/tabBar/statistics/statistics",
				"iconPath": "static/tabBar/chart-pie.png",
				"selectedIconPath": "static/tabBar/chart-pie-active.png",
				"text": "统计"
			},
			{
				"pagePath": "pages/tabBar/todo/todo",
				"iconPath": "static/tabBar/todo.png",
				"selectedIconPath": "static/tabBar/todo-active.png",
				"text": "待办"
			},
			{
				"pagePath": "pages/tabBar/customer/customer",
				"iconPath": "static/tabBar/customer.png",
				"selectedIconPath": "static/tabBar/customer-active.png",
				"text": "客户"
			},
			{
				"pagePath": "pages/tabBar/business/business",
				"iconPath": "static/tabBar/business.png",
				"selectedIconPath": "static/tabBar/business-active.png",
				"text": "商机"
			},
			{
				"pagePath": "pages/tabBar/more/more",
				"iconPath": "static/tabBar/more.png",
				"selectedIconPath": "static/tabBar/more-active.png",
				"text": "更多"
			}
		]
	},
	"uniIdRouter": {},
	"easycom": {
		"autoscan": true, // 关闭自动扫描
		"custom": {
			// 按需配置需要自动导入的组件
			"^uni-(.*)": "@/uni_modules/uni-$1/components/uni-$1/uni-$1.vue"
		}
	}
}
