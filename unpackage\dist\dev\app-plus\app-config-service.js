
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"navigationBar":{"type":"default"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"Ivy","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.75","entryPagePath":"pages/ai-chat","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"black","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/tabBar/statistics/statistics","iconPath":"/static/tabBar/chart-pie.png","selectedIconPath":"/static/tabBar/chart-pie-active.png","text":"统计"},{"pagePath":"pages/tabBar/todo/todo","iconPath":"/static/tabBar/todo.png","selectedIconPath":"/static/tabBar/todo-active.png","text":"待办"},{"pagePath":"pages/tabBar/customer/customer","iconPath":"/static/tabBar/customer.png","selectedIconPath":"/static/tabBar/customer-active.png","text":"客户"},{"pagePath":"pages/tabBar/business/business","iconPath":"/static/tabBar/business.png","selectedIconPath":"/static/tabBar/business-active.png","text":"商机"},{"pagePath":"pages/tabBar/more/more","iconPath":"/static/tabBar/more.png","selectedIconPath":"/static/tabBar/more-active.png","text":"更多"}],"backgroundColor":"#F8F9FA","selectedIndex":0,"shown":true},"fallbackLocale":"zh-Hans","locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/ai-chat","meta":{"isQuit":true,"isEntry":true,"disableScroll":true,"bounce":"none","navigationBar":{"titleText":"Ivy - 下一代销售Agent","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/tabBar/todo/todo","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"disableScroll":true,"bounce":"none","navigationBar":{"titleText":"待办清单","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/tabBar/statistics/statistics","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"bounce":"none","navigationBar":{"titleText":"数据统计","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/tabBar/customer/customer","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"bounce":"none","navigationBar":{"titleText":"客户列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/tabBar/business/business","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":3,"disableScroll":true,"bounce":"none","navigationBar":{"titleText":"商机列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/tabBar/more/more","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":4,"disableScroll":true,"bounce":"none","navigationBar":{"titleText":"ProWise","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/ai-task","meta":{"navigationBar":{"titleText":"任务进度","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/ppt-editor/index","meta":{"bounce":"none","navigationBar":{"style":"custom","type":"default"},"isNVue":false}},{"path":"pages/demo/index","meta":{"navigationBar":{"titleText":"日志","type":"default"},"isNVue":false}},{"path":"pages/error/401","meta":{"navigationBar":{"titleText":"401","type":"default"},"isNVue":false}},{"path":"pages/error/404","meta":{"navigationBar":{"titleText":"Not Found","type":"default"},"isNVue":false}},{"path":"pages/template/customer-details/index","meta":{"disableScroll":true,"navigationBar":{"titleText":"客户详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/template/todo-details/index","meta":{"bounce":"none","disableScroll":true,"navigationBar":{"titleText":"待办详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/template/business-detail/index","meta":{"disableScroll":true,"navigationBar":{"titleText":"商机详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/userInfo/user/user-settings","meta":{"bounce":"none","navigationBar":{"titleText":"个人信息设置","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/userInfo/related-documents/index","meta":{"bounce":"none","navigationBar":{"titleText":"相关文件","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/userInfo/global-search/index","meta":{"bounce":"none","navigationBar":{"titleText":"全局搜索","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/preview/weboffice-preview","meta":{"navigationBar":{"titleText":"文档预览","style":"custom","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  