import { reactive } from 'vue'

export const eventBus = reactive({
  // 事件列表
  events: {},
  
  // 订阅事件
  on(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = []
    }
    this.events[eventName].push(callback)
  },
  
  // 触发事件
  emit(eventName, ...args) {
    if (this.events[eventName]) {
      this.events[eventName].forEach(callback => callback(...args))
    }
  },
  
  // 取消订阅
  off(eventName, callback) {
    if (this.events[eventName]) {
      if (callback) {
        this.events[eventName] = this.events[eventName].filter(cb => cb !== callback)
      } else {
        delete this.events[eventName]
      }
    }
  }
})