<template>
  <!-- 反馈弹框 -->
  <uni-popup 
    ref="feedbackPopup" 
    type="center" 
    background-color="#fff" 
    border-radius="16px"
    :mask-click="false"
  >
    <view class="feedback-modal">
      <!-- 标题栏 -->
      <view class="feedback-header">
        <text class="feedback-title">谢谢你的反馈，我们会继续优化进步</text>
      </view>
      
      <!-- 内容区域 -->
      <view class="feedback-content">
        <textarea
          v-model="feedbackText"
          placeholder="请输入您的反馈建议..."
          class="feedback-textarea"
          maxlength="500"
          :auto-height="true"
          @input="onFeedbackInput"
        ></textarea>
        <!-- <view class="char-count">
          <text class="count-text">{{ feedbackText.length }}/500</text>
        </view> -->
      </view>
      
      <!-- 按钮区域 -->
      <view class="feedback-footer">
        <view class="btn-cancel" @click="handleClose">
          <text class="btn-text">取消</text>
        </view>
        <view 
          class="btn-submit" 
          :class="{ 'btn-disabled': !feedbackText.trim() || isSubmitting }"
          @click="handleSubmit"
        >
          <text class="btn-text">{{ isSubmitting ? '提交中...' : '提交' }}</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import { saveChatFeedback } from '@/http/ai-chat-task.js'
export default {
  name: 'FeedbackModal',
  // Props定义
  props: {
    // 控制弹框显示/隐藏
    visible: {
      type: Boolean,
      default: false,
    },
    // 消息ID
    messageId: {
      type: [String, Number],
      default: null,
    },
  },
  // 响应式数据
  data() {
    return {
      // 反馈文本内容
      feedbackText: '',
      // 是否正在提交
      isSubmitting: false,
    }
  },
  // 监听器
  watch: {
    // 监听visible变化，控制弹框显示/隐藏
    visible: {
      handler(newValue) {
        if (newValue) {
          this.open();
        } else {
          this.close();
        }
      },
      immediate: true
    }
  },
  // 方法
  methods: {
    /**
     * 打开反馈弹框
     */
    open() {
      this.feedbackText = '';
      this.isSubmitting = false;
      if (this.$refs.feedbackPopup) {
        this.$refs.feedbackPopup.open();
      }
    },

    /**
     * 关闭反馈弹框
     */
    close() {
      if (this.$refs.feedbackPopup) {
        this.$refs.feedbackPopup.close();
      }
      this.feedbackText = '';
      this.isSubmitting = false;
    },

    /**
     * 处理关闭操作
     */
    handleClose() {
      this.close();
      this.$emit('close');
    },

    /**
     * 反馈内容输入处理
     */
    onFeedbackInput(e) {
      this.feedbackText = e.detail.value;
    },
    /**
     * 提交消息评价到后端接口
     * @param {Object} params - 评价参数
     * @returns {Promise} 返回提交结果
     */
    async submitMsgRating(params) {
      try {
        console.log('📤 提交评价请求参数:', params);
        // 构建请求参数 - 根据 /api/chat/save 接口规范
        const oldParams = params.message_id
        ? { id: params.message_id }
        : { task_id:  params.task_id };
        const requestData = {
          ...oldParams,
          is_like: params.like_status || 0, // 是否点赞：0-未点赞，1-已点赞
          is_dislike: params.dislike_status || 0, // 是否点踩：0-未踩，1-已踩
          dislike_reason: params.dislike_reason || '' // 点踩原因
        };
        // 调用后端接口
        const response = await saveChatFeedback(requestData);
        console.log('📥 后端响应:', response);
        // 如果请求成功，返回结果
        if (response.code === 0) {
          return {
            code: 0,
            message: response.msg || '提交成功',
            data: response.data || {}
          };
        } else {
          return {
            code: response.code || -1,
            message: response.msg || '提交失败',
            data: {}
          };
        }
      } catch (error) {
        console.error('❌ 提交评价失败:', error);
      }
    },

    /**
     * 处理提交反馈
     */
    async handleSubmit() {
      // 表单验证
      if (!this.feedbackText.trim()) {
        uni.showToast({
          title: '请输入反馈内容',
          icon: 'none'
        });
        return;
      }
      // 防止重复提交
      if (this.isSubmitting) {
        return;
      }
      try {
        this.isSubmitting = true;
        // 直接在组件内部提交反馈和评价
        const response = await this.submitMsgRating({
          message_id: this.messageId,
          task_id: uni.getStorageSync('task_id') || 0,
          like_status: 0, // 踩的时候点赞状态为0
          dislike_status: 1, // 踩的状态为1
          dislike_reason: this.feedbackText.trim() // 反馈内容作为踩的原因
        });
        if (response.code === 0) {
          // 提交成功，触发成功事件并传递相关数据
          this.$emit('submit', {
            messageId: this.messageId,
            // feedback: this.feedbackText.trim(),
            ratingData: {
              like_status: 0,
              dislike_status: 1,
              dislike_reason: this.feedbackText.trim()
            },
            response: response
          });
          // 关闭弹框并清空内容
          this.handleClose();
        } else {
          throw new Error(response.message || '提交失败');
        }
      } catch (error) {
        console.error('❌ 反馈提交失败:', error);
      } finally {
        this.isSubmitting = false;
      }
    },

    /**
     * 供外部调用的打开方法
     */
    show() {
      this.open();
    },

    /**
     * 供外部调用的关闭方法
     */
    hide() {
      this.close();
    }
  }
}
</script>

<style scoped lang="scss">
// 反馈弹框样式
.feedback-modal {
  width: 350px;
  max-width: 90vw;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  
  .feedback-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    .feedback-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  
  }
  
  .feedback-content {
    padding: 20px;
    .feedback-textarea {
      width: 100%;
      min-height: 120px;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      color: #333;
      background-color: #fafafa;
      resize: none;
      box-sizing: border-box;
      transition: border-color 0.2s, background-color 0.2s;
      
      &:focus {
        border-color: #007aff;
        background-color: #fff;
        outline: none;
      }
      
      &::placeholder {
        color: #999;
      }
    }
    
    .char-count {
      margin-top: 4px;
      text-align: right;
      
      .count-text {
        font-size: 12px;
        color: #999;
      }
    }
  }
  
  .feedback-footer {
    display: flex;
    padding: 0px 20px 20px;
    gap: 12px;
    
    .btn-cancel,
    .btn-submit {
      flex: 1;
      height: 44px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      
      .btn-text {
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .btn-cancel {
      background-color: #f5f5f5;
      
      .btn-text {
        color: #666;
      }
      
      &:hover {
        background-color: #ebebeb;
      }
      
      &:active {
        background-color: #e0e0e0;
      }
    }
    
    .btn-submit {
      background-color: #007aff;
      
      .btn-text {
        color: #fff;
      }
      
      &:hover {
        background-color: #0056d6;
      }
      
      &:active {
        background-color: #004bb8;
      }
      
      &.btn-disabled {
        background-color: #ccc;
        cursor: not-allowed;
        
        &:hover,
        &:active {
          background-color: #ccc;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .feedback-modal {
    width: 320px;
    max-width: 85vw;
    
    .feedback-header {
      padding: 16px 20px 12px;
      
      .feedback-title {
        font-size: 16px;
      }
    }
    
    .feedback-content {
      padding: 15px 20px;
      
      .feedback-textarea {
        min-height: 100px;
        padding: 10px;
        font-size: 14px;
      }
    }
    
    .feedback-footer {
      padding: 0px 20px 20px;
      gap: 10px;
      
      .btn-cancel,
      .btn-submit {
        height: 40px;
        
        .btn-text {
          font-size: 14px;
        }
      }
    }
  }
}
</style> 