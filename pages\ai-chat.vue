<template>
  <view class="chat-container">
    <!-- 顶部导航栏 -->
    <ChatHeader 
      ref="chatHeaderRef"
      :sessionTitle="currentSession.title"
      :hasUnreadMessages="hasUnreadMessages"
      @loginSuccess="handleLoginSuccess"
      @toggleDrawer="handleToggleDrawer"
    />
    <!-- 聊天内容区域 -->
    <ChatScrollContainer
      ref="chatScrollRef"
      :messagePaddingBottom="inputContainerHeight"
      :showLoadMore="hasMoreHistory && messages.length > 0 && isLogged"
      :historyLoading="historyLoading"
      @loadMore="fetchChatHistory"
    >
      <template #messages>
        <block v-for="(message, index) in messages" :key="index">
        <!-- 用户消息 :id="'id-' + index + '-view'"-->
        <view v-if="message.role === 'user'" class="message-item user-message">
          <view class="message-content" @click="toggleMessageActions(index)">
            <view class="message-bubble" v-if="!message.created_time && message.content_type!='operation'">
              <!-- 图片类型消息 -->
              <view v-if="message.content_type && isImageFile(message.content_type)" class="message-image-container">
                <image 
                  :src="message.content"
                  mode="widthFix"
                  class="message-image"
                  @click.stop="previewImage(message.content)"
                ></image>
              </view>
              <!-- 文件类型消息 -->
              <view v-else-if="message.content_type && message.attachment" class="message-file-container" @click="handleFilePreview(message.attachment)">
                <view class="file-info">
                  <image :src="getFileIconPath(message.attachment.name)" class="file-type-icon"></image>
                  <view class="file-details">
                    <view class="file-name">{{ message.attachment.name }}</view>
                    <view v-if="message.attachment.size" class="file-size">{{ formatFileSize(message.attachment.size) }}</view>
                  </view>
                </view>
              </view>
              <!-- mix类型消息（包含多种内容类型） -->
              <MessageMixContent 
                v-else-if="message.content_type === 'mix'" 
                :content="message.content"
                @onPreviewImage="previewImage"
                @previewFile="handleFilePreview"
              />
              <!-- 原文件被删除 -->
              <view v-else-if="message.attachment===null&&message.content_type!=='str'&&message.content_type!=='operation'&&message.content_type!=='mix'" class="file-deleted-message">
                  <view class="file-info">
                    <image src="/static/file/unknown.svg" class="file-type-icon"></image>
                    <view>原文件被删除</view>
                  </view>
              </view>
              <!-- 普通文本消息 -->
              <view v-else class="message-text">{{ message.content }}</view>
            </view>
            <!-- 添加时间戳显示 -->
            <block v-else>
              <view v-if="message.created_time && message.content_type!=='operation'" class="conversation-time">{{ message.created_time }}</view>
              <view v-if="message.content_type==='operation'" class="clear-context-msg">已清理上下文</view>
            </block>
            <!-- 用户消息操作按钮 -->
            <UserMessageActions
              v-if="message.content_type==='str'"
              :content="message.content"
              :isVisible="shouldShowMessageActions(message, index)"
              :showActions="true"
              :messageIndex="index"
              @delete="deleteMessage"
            />
          </view>
        </view>
        <!-- AI助手消息 -->
        <view v-else class="message-item assistant-message" :id="'id-' + index + '-view'">
          <view class="message-content" @click.stop="toggleMessageActions(index)">
              <!-- 工具调用容器 -->
              <view v-if="message.toolCalls && message.toolCalls.length > 0" :class="['tool-container', message.areToolsExpanded ? 'expanded' : '']">
                <!-- 处理状态标题 -->
                <view 
                  :class="['processing-status', message.areToolsExpanded ? 'Title-status' : '']" 
                  @click.stop="toggleAllToolsExpand(message)"
                >
                  <view class="status-text">{{ !message.isTyping && !loading ? '处理完成' : '处理中' }}</view>
                  <uni-icons :type="message.areToolsExpanded ? 'top' : 'bottom'" size="14" color="#999"></uni-icons>
                </view>
                <!-- 工具调用列表 -->
                <template v-if="message.areToolsExpanded">
                  <view v-for="(tool, toolIndex) in message.toolCalls" :key="toolIndex" class="tool-call-container">
                    <!-- 工具调用头部 -->
                    <view class="tool-call-header">
                      <!-- 状态指示器 -->
                      <view v-if="getToolLoadingStatus(tool, message.toolCalls, toolIndex)" class="status-icon loading">
                        <uni-load-more status="loading" :iconSize="16" :showText="false" class="tool-loading" style="height: 0; line-height: 1;"></uni-load-more>
                      </view>
                      <image v-else src="/static/global/is-completed.svg" class="status-icon completed"></image>
                      <!-- 工具名称 -->
                      <view class="tool-info">
                        <template v-if="tool.data && tool.data.name !== '思考内容' && tool.data.name !== '分析' && tool.data.name !== '思考'">
                          <text class="tool-label">调用工具：</text>
                          <text class="tool-name">{{ tool.data.name }}</text>
                        </template>
                        <template v-else>
                          <text class="tool-name">{{ tool.data ? tool.data.name : '未知工具' }}</text>
                        </template>
                      </view>
                    </view>
                    <!-- 工具调用内容 -->
                    <view v-if="hasToolContent(tool)" class="tool-call-content">
                      <view v-if="tool.data && tool.data.name === '思考'" class="tool-args args-title-1">
                        <view class="tool-content-title" v-if="tool.data.args && tool.data.args.title">
                          {{ tool.data.args.title }}
                        </view>
                        <text selectable="true" class="tool-content-text">{{ tool.data.args && tool.data.args.thought || '' }}</text>
                      </view>
                      <view v-else-if="tool.data && tool.data.name === '分析'" class="tool-args args-title-2">
                        <view class="tool-content-title" v-if="tool.data.args && tool.data.args.title">
                          {{ tool.data.args.title }}
                        </view>
                        <text selectable="true" class="tool-content-text">{{ tool.data.args && tool.data.args.analysis || '' }}</text>
                        <view class="tool-content-result" v-if="tool.data.args && tool.data.args.result">
                          结果: {{ tool.data.args.result }}
                        </view>
                      </view>
                      <view v-else-if="tool.data && tool.data.name === '网络搜索'" class="tool-args args-title-4">
                        <view v-for="(item, index) in getSearchResults(tool)" :key="index" class="search-result-item">
                          <view class="search-result-title">
                            <text v-if="item.url" class="search-link" @click="handleLinkClick(item.url, item.title)">{{ item.title }}</text>
                            <text v-else class="search-title-text">{{ item.title }}</text>
                          </view>
                          <view v-if="item.date" class="search-result-date">
                            {{ item.date }}
                          </view>
                        </view>
                      </view>
                      <view v-else-if="tool.data && tool.data.name === '网络深度搜索'" class="tool-args args-title-1">
                        <view class="tool-content-text">
                          <mp-html
                            markdown
                            :content="tool.result"
                            :scroll-table="true"
                            :selectable="true"
                            :copy-link="false"
                            @linktap="handeLinkTapChat"
                          />
                        </view>
                      </view>
                      <view v-else-if="tool.data && tool.data.args" class="tool-args args-title-3">
                        <!-- 图片类型消息 -->
                        <view v-if="tool.data.args.imgUrl || (tool.data.args.content_type && isImageFile(tool.data.args.content_type))" class="message-image-container">
                          <image 
                            :src="tool.data.args.imgUrl || tool.data.args.content"
                            mode="widthFix"
                            class="message-image"
                            @click.stop="previewImage(tool.data.args.imgUrl || tool.data.args.content)"
                          ></image>
                          <view v-if="tool.data.args.attachment" class="image-filename">{{ tool.data.args.attachment.name }}</view>
                        </view>
                        <!-- 文件类型消息 -->
                        <view v-else-if="(tool.data.args.content_type && tool.data.args.attachment) || tool.data.args.fileUrl" class="message-file-container">
                          <view class="file-info">
                            <image 
                              :src="getFileIconPath(tool.data.args.attachment ? tool.data.args.attachment.name : tool.data.args.fileUrl)" 
                              mode="aspectFit" 
                              class="file-type-icon"
                            ></image>
                            <view class="file-details">
                              <view class="file-name">{{ tool.data.args.fileName ? tool.data.args.fileName : getFileNameFromUrl(tool.data.args.fileUrl) }}</view>
                              <view v-if="tool.data.args.attachment && tool.data.args.attachment.size" class="file-size">{{ formatFileSize(tool.data.args.attachment.size) }}</view>
                            </view>
                          </view>
                        </view>
                      </view>
                      <view v-if="tool.result && shouldShowToolResult(tool)" class="tool-result">
                        <text selectable="true">{{ tool.result }}</text>
                      </view>
                    </view>
                  </view>
                </template>
              </view>      
              <!-- 处理中状态 - 只在没有工具调用或内容时显示 -->
              <view v-if="message.isTyping && !message.content && (!message.toolCalls || message.toolCalls.length === 0)" class="processing-text-container">
                <text class="processing-text">处理中...</text>
              </view>
              <!-- 消息内容 - 只在有内容时显示 -->
              <view v-if="message.content && message.content.trim() !== ''" class="message-bubble">
                <!-- 有内容时，显示内容 -->
                <mp-html
                  markdown
                  :content="message.content"
                  :scroll-table="true"
                  :selectable="true"
                  :copy-link="false"
                  @linktap="handeLinkTapChat"
                />
              </view>
              <!-- 消息内容为空且不在生成状态，显示占位内容 -->
              <view v-if="!message.isTyping && !loading && (!message.content || message.content.trim() === '') && (!message.toolCalls || message.toolCalls.length === 0)" class="empty-content">
                <!-- 占位空间，确保消息气泡总是显示 -->
                <uni-tag :inverted="true" text="服务器处理失败，请刷新后重试" type="warning" />
              </view>
            </view>
            <!-- 消息操作按钮 -->
            <AiMessageActions
              v-if="isHasToken"
              :ref="el => setAiMessageActionsRef(el, index)"
              :content="message.content"
              :messageId="message.id"
              :isVisible="!message.isTyping && shouldShowMessageActions(message, index)"
              :showActions="true"
              :messageIndex="index"
              :playStatus="isPlayStatus"
              :initialLikeStatus="message.is_like || 0"
              :initialDislikeStatus="message.is_dislike || 0"
              :initialDislikeReason="message.dislike_reason || ''"
              @delete="deleteMessage"
              @playAppVoice="handerPlayAppVoice"
              @openFeedback="openFeedbackModal"
            />
            <!-- 添加时间戳显示 -->
            <view v-if="message.created_time && message.content_type!=='operation'" class="conversation-time">{{ message.created_time }}</view>
          </view>
        </block>
      </template>
    </ChatScrollContainer>
    <!-- 输入区域 -->
    <view class="chat-input-container" ref="chatInputContainerRef">
      <!-- 已上传文件列表 -->
      <UploadedFilesList 
        :files="uploadedFiles"
        @remove="removeUploadedFile"
      />
      <view class="input-wrapper">
        <!-- 统一使用textarea，PC端通过原生document监听键盘事件 -->
        <textarea
          class="chat-input"
          :class="{ 'pc-input': isPCDevice }"
          v-model="inputMessage"
          :placeholder="inputPlaceholder"
          :auto-blur="!isPCDevice"
          :auto-height="true"
          :maxlength="-1"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @input="onInputChange"
          @confirm="sendMessage"
        ></textarea>
        <view class="input-tools">
          <view class="tool-left">
            <view class="clear-input-button" @click="clearInputContext">
              <image class="clear-icon" src="/static/chatImg/clear.svg" alt="删除" />
            </view>
            <view class="tool-item" :class="{'tool-item-active': skillText}"
                @click="toggleSkillSearchStatus">
                <image src="/static/chatImg/tool-tag-active.svg" class="tag-btn-icon" alt="上传文件" v-if="skillText"/>
                <image src="/static/chatImg/tool-tag.svg" class="tag-btn-icon" alt="上传文件" v-else/>
                <text class="tag-text">{{ skillText || '技能' }}</text>
            </view>
          </view>
          <view class="tool-right">
            <!-- 文件选择器 -->
            <view class="file-btn-item" @click="openFilePicker">
              <image src="/static/chatImg/add-flie.svg" class="input-btn-icon" alt="上传文件" />
            </view>
            <!-- H5端语音输入 -->
            <!-- #ifdef H5 -->
            <view class="speech-item voice-tool" :class="{'voice-active': isVoiceInputActive}" @click="isVoiceInputActive ? stopVoiceRecognition() : startVoiceInput()">
              <!-- 非激活状态显示麦克风图标 -->
              <image v-if="!isVoiceInputActive" src="/static/chatImg/btn-yuyin.svg" alt="语音输入" />
              <!-- 激活状态显示声波动画 -->
              <view v-else class="voice-wave-container">
                <view class="voice-wave-bar" v-for="(item, index) in 4" :key="index"></view>
              </view>
              <!-- 提示文本 -->
              <view v-if="isVoiceInputActive" class="voice-tooltip">停止语音输入</view>
            </view>
            <!-- #endif -->
            <!-- APP端语音输入 -->
            <!-- #ifdef APP-PLUS -->
            <view class="speech-item voice-tool" :class="{'voice-active': isVoiceInputActive}" @click="isVoiceInputActive ? stopAppVoiceRecognition() : startAppVoiceInput()">
              <!-- 非激活状态显示麦克风图标 -->
              <image v-if="!isVoiceInputActive" src="/static/chatImg/btn-yuyin.svg" alt="语音输入" />
              <!-- 激活状态显示声波动画 -->
              <view v-else class="voice-wave-container">
                <view class="voice-wave-bar" v-for="(item, index) in 4" :key="index"></view>
              </view>
              <!-- 提示文本 -->
              <view v-if="isVoiceInputActive" class="voice-tooltip">停止语音输入</view>
            </view>
            <!-- #endif -->
            <!-- 停止按钮 -->
            <view v-if="loading" class="tool-button" @click="stopMessage">
              <image src="/static/chatImg/chat-stop.svg" alt="停止生成"  />
            </view>
            <!-- 发送按钮 -->
            <view v-else class="tool-button send-button" 
              :class="{'send-button-disabled': !canSendMessage}"
              @click="canSendMessage ? sendMessage() : null">
              <uni-icons type="paperplane-filled" size="24" color="#FFFFFF"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- SSE客户端组件 -->
    <gao-ChatSSEClient
      ref="chatSSEClientRef"
      @onOpen="openCore"
      @onError="errorCore"
      @onMessage="messageCore"
      @onFinish="finishCore"
    />
    <!-- 技能选择弹出层 -->
    <SkillSelector 
      ref="skillSelectorRef"
      v-if="isLogged"
      v-model="skillText"
      @select="handleSkillSelect"
    />
    <!-- xe-upload 组件（隐藏，用于文件选择） -->
    <xe-upload 
      ref="xeUpload" 
      @callback="handleXeUploadCallback"
    />
    <!-- App端必需的语音播放组件 -->
    <VoicePlayer ref="voicePlayerRef" />
    <!-- 反馈弹框组件 -->
    <FeedbackModal
      :visible="showFeedbackModal"
      :messageId="feedbackMessageId"
      @close="handleFeedbackClose"
      @submit="handleFeedbackSubmit"
    />
    <!-- 左侧抽屉组件 -->
    <DrawerSidebar
      v-if="isLogged"
      ref="drawerSidebarRef"
      @itemClick="handleDrawerItemClick"
    />
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref, nextTick, onMounted, watch, onUnmounted, computed } from 'vue'
import { isImageFile, getFileIconPath, formatFileSize, getFileNameFromUrl,openWebOfficePreview } from '@/utils/fileUtils.js'
import { detectBrowser, isPC, removeContentProperty, convertPythonJsonToStandard, openLink } from '@/utils/utils.js'
import { handleLink } from '@/utils/linkHandler.js' // 导入链接处理工具
// #ifdef APP-PLUS
import { setLinkTapStatus } from '@/utils/globalState.js' // 导入全局状态管理
// #endif

// #ifdef H5
import voiceRecognitionService from '@/services/voiceRecognitionService.js'
import { voicePlayService } from '@/services/voicePlayService.js'
// #endif
import {fetchTaskId, saveAttachmentAfterUpload, getCurrentTaskId, calculateUnreadMessageIndex } from '@/utils/aiChatUtils.js'
import { getChatHistoryList, clearContext, stopTask, deleteChatMessage } from "@/http/ai-chat-task.js"
import { deleteAttachment, generateFileDownloadSignature } from "@/http/attachment.js"
import envConfig from '@/utils/env.js' // 导入环境配置
import ChatScrollContainer from '@/components/chat/ChatScrollContainer.vue' // 导入聊天滚动组件
import ChatHeader from '@/components/chat/ChatHeader.vue' // 导入聊天头部组件
import DrawerSidebar from '@/components/chat/DrawerSidebar.vue' // 导入左侧抽屉组件
import VoicePlayer from '@/components/voice-player/voice-player.vue'; // 语音播放组件
import MessageMixContent from '@/components/chat/MessageMixContent.vue' // 导入混合内容组件
import UploadedFilesList from '@/components/chat/UploadedFilesList.vue' // 导入已上传文件列表组件
import SkillSelector from '@/components/chat/SkillSelector.vue' // 导入技能选择组件
import AiMessageActions from '@/components/chat/AiMessageActions.vue' // 导入AI消息操作按钮组件
import UserMessageActions from '@/components/chat/UserMessageActions.vue' // 导入用户消息操作按钮组件
import FeedbackModal from '@/components/chat/FeedbackModal.vue' // 导入反馈弹框组件
import XeUpload from "@/uni_modules/xe-upload/components/xe-upload/xe-upload.vue";
import { useFileUpload } from '@/utils/useFileUpload.js'; // 导入文件上传hooks
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具
// 使用文件上传hooks
const {
    chooseAndUpload,
    setXeUploadRef, 
    handleXeUploadCallback 
  } = useFileUpload();

const isPCDevice = ref(false); // 是否为PC端
// 计算输入框高度并更新消息列表的底部间距
const inputContainerHeight = ref('80px'); // 默认底部间距
// 添加打字机效果相关状态
const typingSpeed = ref(80); // 打字速度(毫秒/字符)
const displayedContent = ref(''); // 当前显示的内容
const fullContent = ref(''); // 完整的内容
const isTyping = ref(false); // 是否正在打字
const typingTimer = ref(null); // 打字定时器
// 组件引用
const voicePlayerRef = ref(null);
const chatSSEClientRef = ref(null);
const chatScrollRef = ref(null);
const chatHeaderRef = ref(null);
const chatInputContainerRef = ref(null);
const skillSelectorRef = ref(null);
const drawerSidebarRef = ref(null); // 左侧抽屉组件引用
const xeUpload = ref(null); // xe-upload 组件引用
const aiMessageActionsRefs = ref(new Map()); // AiMessageActions 组件引用集合
// 状态变量
const skillText = ref(''); // 当前技能名称
const selectedSkillValue = ref(''); // 当前选中的技能值
const skillExample = ref(''); // 当前技能示例文本
const inputPlaceholder = ref('点击输入...'); // 输入框placeholder
const inputMessage = ref('');
const messages = ref([]);
const loading = ref(false);
const uploadedFiles = ref([]);
// 滚动防抖动计时器
const scrollDebounceTimer = ref(null);
// 添加聊天历史状态
const historyLoading = ref(false);
const hasMoreHistory = ref(true); // 是否有更多历史消息
const currentPage = ref(1); // 当前页码
const pageSize = ref(40); // 每页记录数
// 添加状态变量，用于跟踪当前被点击的消息
const clickedMessageIndex = ref(null);
// 语音输入状态
const isVoiceInputActive = ref(false);
const isPlayStatus = ref(0); // 播放状态
// 反馈弹框相关状态
const showFeedbackModal = ref(false); // 反馈弹框显示状态
const feedbackMessageId = ref(null); // 当前反馈的消息ID
const pendingFeedbackData = ref(null); // 待处理的反馈数据

// APP端语音识别状态变量（简化版，与H5端保持一致）
// #ifdef APP-PLUS
const appAccumulatedText = ref(""); // 累积的识别文本
const isAppVoiceRecognitionStopped = ref(false); // 标记语音识别是否已被主动停止（防止回显）
// #endif
// 登录状态
const isLogged = ref(false); // 是否已登录
const isHasToken = ref(false); // 是否y有token
const isSkillSearchActive = ref(false);
// 商机会话相关状态
const currentSession = ref({
  session_id: '',
  title: ''
}); // 当前会话对象，包含ID和标题

// 计算属性：检查是否有内容可发送
const canSendMessage = computed(() => {
  // 只有当输入框有内容时才允许发送，不再考虑是否上传了文件
  return inputMessage.value.trim() && inputMessage.value.trim().length > 0;
});

// 计算属性：检查是否有未读消息
const hasUnreadMessages = computed(() => {
  // 通过 DrawerSidebar 组件引用获取未读消息状态
  if (drawerSidebarRef.value && drawerSidebarRef.value.hasUnreadMessages) {
    return drawerSidebarRef.value.hasUnreadMessages;
  }
  // 如果无法访问组件数据，返回 false
  return false;
});

// 响应式变量
const token = uni.getStorageSync('token');
const userInfo = uni.getStorageSync('userInfo') || {}; 
// 监听 token 和 userInfo
watch(
  [() => token, () => userInfo], // 监听 token 和 userInfo 的变化
  () => {
    if (token !== undefined && token !== null && token !== '') {
      isHasToken.value = true;
    } else {
      isHasToken.value = false;
    }
    if (userInfo && userInfo.is_activated === 1) {
      isLogged.value = true;
    } else {
      isLogged.value = false;
    }
  },
  { immediate: true } // 确保在初始时也会运行
);

/**
 * 检查登录状态
 * 统一的登录检查方法，供其他功能调用
 * @returns {Boolean} 是否已登录
 */
const checkLoginStatus = () => {
  if (chatHeaderRef.value) {
    return chatHeaderRef.value.checkLoginStatus()
  }
  return false
}

/**
 * 设置AiMessageActions组件引用
 * @param {Object} el - 组件实例
 * @param {Number} index - 消息索引
 */
const setAiMessageActionsRef = (el, index) => {
  if (el) {
    aiMessageActionsRefs.value.set(index, el);
  } else {
    aiMessageActionsRefs.value.delete(index);
  }
};

/**
 * 关闭所有AiMessageActions组件的popover
 */
const closeAllAiMessageActionsPopover = () => {
  aiMessageActionsRefs.value.forEach((ref) => {
    if (ref && typeof ref.closePopover === 'function') {
      ref.closePopover();
    }
  });
};

/**
 * 全局点击事件处理器
 * 点击空白区域关闭所有popover
 * @param {Event} event - 点击事件
 */
const handleGlobalClick = (event) => {
  // 检查点击的元素是否在AiMessageActions组件内部
  const isClickInsideActions = event.target.closest('.message-actions');
  // 如果点击的不是AiMessageActions组件内部，则关闭所有popover
  if (!isClickInsideActions) {
    closeAllAiMessageActionsPopover();
  }
};

// 处理登录成功
const handleLoginSuccess = async (data) => {
  // console.log('🔐 主页面处理登录成功:', data)
  try {
    // 更新本地状态
    isLogged.value = data.isLogged
    isHasToken.value = data.isHasToken
    // 只保存token
    uni.setStorageSync("token", data.token);

    // 已登录，加载聊天历史
    await fetchChatHistory()
    // console.log('✅ 聊天历史加载完成')
    
    // 滚动到底部
    await scrollToBottom()
  } catch (error) {
    console.error('❌ 登录后操作失败:', error)
  }
}

/**
 * 图片预览功能 - 支持多种URL类型
 * @param {string} imageUrl - 图片URL
 */
const previewImage = (imageUrl) => {
  console.log('预览图片URL:', imageUrl);
  if (!imageUrl) return;
  // 去除可能的引号并清理URL
  const cleanUrl = imageUrl.replace(/[`'"]/g, '').trim();
  // 预览图片
  uni.previewImage({
    urls: [cleanUrl],
    current: cleanUrl,
    indicator: 'number',
    loop: false,
    fail: (err) => {
      console.error('图片预览失败:', err);
      uni.showToast({
        title: '图片预览失败',
        icon: 'none'
      });
    }
  });
};

/**
 * 处理文件预览 - 集成原previewFile逻辑
 * @param {Object} attachment - 文件对象
 */
const handleFilePreview = async (attachment) => {
  try {
    console.log('🔍 开始预览文件:', attachment);
    // 调用公共预览工具，传入WebOffice预览回调
    const result = await previewFile(attachment, {
      onWebOfficePreview: openWebOfficePreview, // H5端WebOffice预览回调
    });
    console.log('📄 文件预览结果:', result);
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '文件预览失败',
      icon: 'none'
    });
  }
};

// SSE事件处理函数
const openCore = (response) => {
  console.log("open sse：", response);
}

const errorCore = (err) => {
  console.log("error sse：", err);
  loading.value = false;
  // 查找最后一条助手消息并确保所有工具调用都标记为已完成
  const lastAssistantMessage = findLastAssistantMessage();
  if (lastAssistantMessage) {
    // 标记消息不再处于打字状态
    lastAssistantMessage.isTyping = false;
    // 确保所有工具调用都标记为已完成
    if (lastAssistantMessage.toolCalls && lastAssistantMessage.toolCalls.length > 0) {
      lastAssistantMessage.toolCalls.forEach(toolCall => {
        if (!toolCall.isCompleted) {
          completeToolLoading(toolCall); // 使用新的完成方法
          console.log(`错误处理：标记工具调用 ${toolCall.data?.name || '未知工具'} 为已完成`);
        }
      });
    }
  }
  // 显示错误消息
  uni.showToast({
    title: '连接失败，请稍后重试',
    icon: 'none',
    duration: 5000
  });
  // 移除正在输入的消息
  if (messages.value.length > 0 && messages.value[messages.value.length - 1].isTyping) {
    messages.value.pop();
  }
}

const getSearchResults = (tool) => {
  try {
    // 网络搜索工具不显示原始结果，只显示解析后的结构化数据
    if (!tool || !tool.data || tool.data.name !== '网络搜索') {
      return null;
    }
    let searchData = null;
    // 如果没有从result中获取到数据，尝试从其他位置获取
    if (tool.data && tool.data.args && tool.data.args.tool_result) {
      // 来自list.json的格式
      if (typeof tool.data.args.tool_result === 'string') {
        try {
          if (tool.data.args?.tool_result) {
            try {
              // 删除content属性
              const cleanedData = removeContentProperty(tool.data.args.tool_result);
              // 转换为标准JSON格式
              const standardJson = convertPythonJsonToStandard(cleanedData);
              // 解析为数组
              searchData = JSON.parse(standardJson);
              console.log('解析结果:', searchData);
            } catch (error) {
              console.error('JSON解析失败:', error);
            }
          }
        } catch (e) {
          console.error('解析搜索结果失败:', e);
        }
      } else if (Array.isArray(tool.data.args.tool_result)) {
        searchData = tool.data.args.tool_result;
      }
    }
    // 验证数据格式并返回
    if (Array.isArray(searchData) && searchData.length > 0) {
      return searchData.filter(item => item && (item.title || item.content));
    }
    return null;
  } catch (error) {
    console.error('获取搜索结果时出错:', error);
    return null;
  }
};

// 检查是否应显示工具结果
const shouldShowToolResult = (tool) => {
  // 网络搜索工具不显示原始结果，只显示解析后的结构化数据
  if (tool.data && tool.data.name === "网络搜索" || tool.data.name === "网络深度搜索") {
    return false;
  }
  // 特殊工具（思考、分析）的特殊处理
  if (tool.data && (tool.data.name === "思考" || tool.data.name === "分析")) {
    console.log(`检查工具结果显示 - 名称:${tool.data.name}, 结果:`, tool.result);
    
    // 如果没有结果，不显示结果部分
    if (!tool.result) {
      console.log("结果为空，不显示结果");
      return false;
    }
    
    // 如果结果已经在参数中显示，就不再显示结果
    if (tool.data.args) {
      const argsContent = tool.data.name === "分析" 
        ? (tool.data.args.analysis || "") 
        : (tool.data.args.thought || "");
        
      // 如果结果与参数内容完全相同，不显示结果
      if (tool.result === argsContent) {
        console.log("结果与参数相同，不显示结果");
        return false;
      }
    }
    
    // 如果结果不为空且与参数不同，则显示结果
    return true;
  }
  return true;
};

// 检查工具是否有内容可以展示
const hasToolContent = (tool) => {
  if (!tool || !tool.data) {
    return false;
  }
  if (tool.data.name === '智能内容写手') {
    return false;
  }
  
  // 检查思考工具
  if (tool.data.name === '思考') {
    // 检查是否有思考内容
    return tool.data.args && 
           tool.data.args.thought && 
           tool.data.args.thought.trim() !== '';
  }
  
  // 检查分析工具
  else if (tool.data.name === '分析') {
    // 检查是否有分析内容
    return tool.data.args && 
           tool.data.args.analysis && 
           tool.data.args.analysis.trim() !== '';
  }
  
  // 检查网络搜索工具
  else if (tool.data.name === '网络搜索') {
    return getSearchResults(tool) !== null;
  }
  
  // 检查图片类型内容
  else if (tool.data.args && (tool.data.args.imgUrl || (tool.data.args.content_type && isImageFile(tool.data.args.content_type)))) {
    return true;
  }
  
  // 检查文件类型内容
  else if (tool.data.args && tool.data.args.content_type && tool.data.args.attachment) {
    return true;
  }
  
  // 检查其他具有参数的工具
  else if (tool.data.args) {
    // 对于其他工具，检查是否有非空参数
    if (typeof tool.data.args === 'object') {
      // 检查参数对象中是否有任何非空值
      return Object.values(tool.data.args).some(value => {
        if (typeof value === 'string') {
          return value.trim() !== '';
        }
        return value !== null && value !== undefined;
      });
    }
    return true;
  }
  
  // 检查是否有工具执行结果
  if (tool.result) {
    return tool.result.trim() !== '';
  }
  
  return false;
};

// ==================== 工具调用状态管理 ====================

/**
 * 获取工具的loading状态 - 顺序执行逻辑
 * @param {Object} tool - 工具调用对象
 * @param {Array} toolCalls - 所有工具调用数组
 * @param {number} currentIndex - 当前工具在数组中的索引
 * @returns {boolean} 是否正在loading
 */
const getToolLoadingStatus = (tool, toolCalls = [], currentIndex = 0) => {
  if (!tool) return false;
  // 如果工具已完成，不显示loading
  if (tool.isCompleted) return false;
  // 检查是否是当前应该执行的工具（顺序执行逻辑）
  // 只有前面的工具都完成了，当前工具才能显示loading
  for (let i = 0; i < currentIndex; i++) {
    if (toolCalls[i] && !toolCalls[i].isCompleted) {
      // 前面还有未完成的工具，当前工具不应该显示loading
      return false;
    }
  }
  // 如果前面的工具都完成了，且当前工具未完成，则显示loading
  return !tool.isCompleted;
};

/**
 * 设置工具的loading状态
 * @param {Object} tool - 工具调用对象
 * @param {boolean} isLoading - loading状态
 */
const setToolLoadingStatus = (tool, isLoading) => {
  if (!tool) return;
  tool.isLoading = isLoading;
  console.log(`🔧 设置工具 ${tool.data?.name || '未知工具'} loading状态:`, isLoading);
};

/**
 * 开始工具loading
 * @param {Object} tool - 工具调用对象
 */
const startToolLoading = (tool) => {
  setToolLoadingStatus(tool, true);
  tool.isCompleted = false;
};

/**
 * 完成工具loading
 * @param {Object} tool - 工具调用对象
 */
const completeToolLoading = (tool) => {
  setToolLoadingStatus(tool, false);
  tool.isCompleted = true;
};

/**
 * 更新工具执行状态 - 确保顺序执行
 * @param {Array} toolCalls - 工具调用数组
 * @param {number} completedIndex - 刚完成的工具索引
 */
const updateToolExecutionStatus = (toolCalls, completedIndex) => {
  if (!toolCalls || toolCalls.length === 0) return;
  console.log(`🔧 工具 ${completedIndex} 完成，更新执行状态`);
  // 确保当前工具已完成
  if (completedIndex >= 0 && completedIndex < toolCalls.length) {
    completeToolLoading(toolCalls[completedIndex]);
  }
  // 检查下一个工具是否应该开始
  const nextIndex = completedIndex + 1;
  if (nextIndex < toolCalls.length && !toolCalls[nextIndex].isCompleted) {
    console.log(`🚀 开始执行下一个工具: ${toolCalls[nextIndex].data?.name || '未知工具'}`);
    // 下一个工具自动开始（不需要手动设置，因为getToolLoadingStatus会自动判断）
  }
};

// 切换所有工具调用的展开/收起状态
const toggleAllToolsExpand = (message) => {
  if (!message.toolCalls || message.toolCalls.length === 0) return;
  // 如果未设置默认值，设为false（默认不展开）
  if (message.areToolsExpanded === undefined) {
    message.areToolsExpanded = false;
  }
  // 处理中状态下的特殊处理：允许手动收起，但处理中时会自动保持展开
  if (message.isTyping && !message.areToolsExpanded) {
    // 如果是处理中状态且当前是收起状态，则展开
    message.areToolsExpanded = true;
    console.log("处理中状态，展开工具调用列表");
    return;
  }
  // 切换展开/收起状态
  message.areToolsExpanded = !message.areToolsExpanded;
  console.log("切换工具调用展开状态:", message.areToolsExpanded);
};

// 实现打字机效果和工具调用处理函数
const messageCore = (msg) => {
  // console.log("message sse：", msg);
  try {
    // 检查是否是结束标志 [DONE]
    if (msg.data === '[DONE]') {
      console.log("收到结束标志 [DONE]");
      drawerSidebarRef.value.markSessionAsRead(currentSession.value.session_id);
      finishCore();
      return;
    }
    // 检查数据是否为数组
    let dataArray = [];
    // 处理不同数据格式：字符串、对象、数组
    if (typeof msg.data === 'string') {
      try {
        // 尝试将字符串解析为JSON
        const parsed = JSON.parse(msg.data);
        // 判断解析结果是数组还是对象
        if (Array.isArray(parsed)) {
          dataArray = parsed; // 直接使用数组
        } else {
          dataArray = [parsed]; // 单个对象包装成数组
        }
      } catch (e) {
        // 如果无法解析为JSON，则视为普通字符串
        dataArray = [msg.data];
      }
    } else if (Array.isArray(msg.data)) {
      dataArray = msg.data; // 已经是数组
    } else {
      dataArray = [msg.data]; // 其他情况，包装为数组
    }
    // 处理每个数据项
    dataArray.forEach(dataItem => {
      if (dataItem === '[DONE]') {
        console.log("收到结束标志 [DONE]");
        finishCore();
        return;
      }
      // 解析每个数据项，支持字符串和对象两种情况
      let parsedData;
      try {
        parsedData = typeof dataItem === 'string' ? JSON.parse(dataItem) : dataItem;
      } catch (e) {
        console.warn("无法解析数据项:", dataItem);
        return; // 跳过无法解析的数据项
      }
      console.log("解析后的数据:", parsedData);
      // 处理数组类型的parsedData（多数组情况）
      if (Array.isArray(parsedData)) {
        parsedData.forEach(item => {
          processDataItem(item);
        });
      } else {
        // 处理单个对象（单数组情况）
        processDataItem(parsedData);
      }
    });
    // 使用防抖动方式滚动到底部，避免频繁滚动导致的页面抖动
    if (!scrollDebounceTimer.value) {
      scrollDebounceTimer.value = setTimeout(() => {
        scrollToBottom();
        scrollDebounceTimer.value = null;
      }, 100);
    }
  } catch (error) {
    console.error('解析SSE消息失败:', error, msg.data);
    // 不要因为解析错误而中断流程，特别是对于[DONE]标记
  }
}

// 处理单个数据项
const processDataItem = (data) => {
  // 处理不同的事件类型，参考 processStream 方法的逻辑
  if (data.event) {
    // 处理 ai-task.vue 中的事件类型
    handleEventTypeData(data);
  } else if (data.eventType) {
    // 处理现有的 eventType 类型事件
    handleEventTypeData(data);
  } else if (data && data.choices && data.choices[0]) {
    // 处理标准 API 的 SSE 消息格式
    handleStandardApiData(data);
  } else if (data && data.content) {
    // 处理直接包含content的简单结构
    handleSimpleContent(data);
  }
}

// 处理简单的内容结构
const handleSimpleContent = (data) => {
  if (data.content && data.content !== "") {
    const lastAssistantMessage = findLastAssistantMessage();
    if (!lastAssistantMessage || !lastAssistantMessage.isTyping) {
      // 创建新消息
      messages.value.push({
        role: 'assistant',
        content: '',
        fullContent: data.content,
        isTyping: true,
        toolCalls: [], // 初始化空的工具调用数组
        areToolsExpanded: false, // 默认不展开工具调用列表
        // timestamp: Date.now()
      });
      // 更新打字机状态
      fullContent.value = data.content;
      displayedContent.value = '';
      isTyping.value = true;
      // 开始打字机效果
      startTypewriterEffect();
    } else {
      // 检查是否有工具调用，并且是首次开始显示实际文本内容
      if (lastAssistantMessage.toolCalls && 
          lastAssistantMessage.toolCalls.length > 0 && 
          (lastAssistantMessage.content === '' || lastAssistantMessage.content === ' ')) {
        // 开始显示文本内容时，自动关闭工具调用列表
        lastAssistantMessage.areToolsExpanded = false;
        console.log("检测到开始显示文本内容，自动关闭工具调用列表");
      }
      
      // 更新最后一条消息的完整内容
      lastAssistantMessage.fullContent += data.content;
      fullContent.value = lastAssistantMessage.fullContent; // 确保全局状态匹配
      // 重启打字机效果以显示新内容
      startTypewriterEffect();
    }
  }
}

// 处理带有 event 或 eventType 属性的数据
const handleEventTypeData = (data) => {
  const eventType = data.event || data.eventType;
  console.log(`处理事件类型: ${eventType}`, data);
  
  switch (eventType) {
    case "RunStarted":
      console.log("Run Started");
      break;
    case "ToolResponse":
      // 处理工具响应 - 添加打字机效果
      const toolContent = data.content || '';
      if (toolContent !== '') {
        let lastIndex = messages.value.length - 1;
        let lastMessage = messages.value[lastIndex];
        
        if (lastMessage && lastMessage.toolCalls && lastMessage.toolCalls.length > 0) {
          let lastToolIndex = lastMessage.toolCalls.length - 1;
          let currentTool = lastMessage.toolCalls[lastToolIndex];
          
          // 如果工具还没有结果，初始化
          if (!currentTool.result) {
            currentTool.result = '';
            currentTool.fullResult = toolContent;
            currentTool.isTyping = true;
            
            // 启动工具响应的打字机效果
            startToolTypewriterEffect(currentTool, toolContent);
          } else {
            // 累加内容并重启打字机效果
            currentTool.fullResult = (currentTool.fullResult || currentTool.result) + toolContent;
            currentTool.isTyping = true;
            
            // 重启工具响应的打字机效果
            startToolTypewriterEffect(currentTool, currentTool.fullResult);
          }
        }
      }
      break;
    case "RunResponse":
      // 处理文本消息
      const content = data.content || (data.data && data.data.content);
      if (content !== undefined && content !== "") {
        console.log("收到文本消息内容:", content);
        
        // 查找最后一个用户消息之后的第一个助手消息
        let lastAssistantMessage = null;
        let lastUserMessageIndex = -1;
        
        // 查找最后一个用户消息的索引
        for (let i = messages.value.length - 1; i >= 0; i--) {
          if (messages.value[i].role === 'user') {
            lastUserMessageIndex = i;
            break;
          }
        }
        
        // 在用户消息之后查找助手消息
        if (lastUserMessageIndex !== -1) {
          for (let i = lastUserMessageIndex + 1; i < messages.value.length; i++) {
            if (messages.value[i].role === 'assistant') {
              lastAssistantMessage = messages.value[i];
              break;
            }
          }
        }
        
        // 如果找不到助手消息或者助手消息已经完成打字，创建新消息
        if (!lastAssistantMessage) {
          // 创建新消息
          lastAssistantMessage = {
            role: 'assistant',
            content: '', // 初始为空，将通过打字机效果逐渐显示
            fullContent: content, // 存储完整内容
            isTyping: true,
            toolCalls: [], // 初始化空的工具调用数组
            areToolsExpanded: false, // 默认不展开工具调用列表
            // timestamp: Date.now()
          };
          
          // 如果找到了最后一个用户消息，在其后插入助手消息
          if (lastUserMessageIndex !== -1) {
            messages.value.splice(lastUserMessageIndex + 1, 0, lastAssistantMessage);
          } else {
            // 如果没有找到用户消息，则添加到消息列表末尾
            messages.value.push(lastAssistantMessage);
          }
          
          // 更新打字机状态
          fullContent.value = content;
          displayedContent.value = '';
          isTyping.value = true;
          
          // 开始打字机效果
          startTypewriterEffect();
        } else {
          // 更新现有消息的内容
          if (!lastAssistantMessage.fullContent) {
            lastAssistantMessage.fullContent = '';
          }
          // 确保areToolsExpanded属性存在
          if (lastAssistantMessage.areToolsExpanded === undefined) {
            lastAssistantMessage.areToolsExpanded = true;
          }
          // 检查是否有工具调用，并且是首次开始显示实际文本内容
          if (lastAssistantMessage.toolCalls && 
              lastAssistantMessage.toolCalls.length > 0 && 
              (lastAssistantMessage.content === '' || lastAssistantMessage.content === ' ')) {
            // 开始显示文本内容时，自动关闭工具调用列表
            lastAssistantMessage.areToolsExpanded = false;
            console.log("检测到开始显示文本内容，自动关闭工具调用列表");
          }
          
          lastAssistantMessage.fullContent += content;
          lastAssistantMessage.isTyping = true;
          
          // 更新打字机状态
          fullContent.value = lastAssistantMessage.fullContent;
          
          // 如果已有内容，继续从当前位置打字
          if (lastAssistantMessage.content) {
            displayedContent.value = lastAssistantMessage.content;
          } else {
            displayedContent.value = '';
          }
          isTyping.value = true;
          // 重启打字机效果
          startTypewriterEffect();
        }
        // console.log("更新后的助手消息:", lastAssistantMessage);
      }
      break;
    case "ToolCallStarted":
    case "toolCallStart":
      // 获取工具名称
      const toolName = data.member_name || (data.data && data.data.name);
      console.log("工具调用名称:", toolName);
      
      // 查找最后一个用户消息之后的第一个助手消息
      let lastUserMessageIndex = -1;
      let lastAssistantMessage = null;
      
      // 查找最后一个用户消息的索引
      for (let i = messages.value.length - 1; i >= 0; i--) {
        if (messages.value[i].role === 'user') {
          lastUserMessageIndex = i;
          break;
        }
      }
      // 在用户消息之后查找助手消息
      if (lastUserMessageIndex !== -1) {
        for (let i = lastUserMessageIndex + 1; i < messages.value.length; i++) {
          if (messages.value[i].role === 'assistant') {
            lastAssistantMessage = messages.value[i];
            break;
          }
        }
      } else {
        // 如果没有找到用户消息，使用最后一条助手消息
        lastAssistantMessage = findLastAssistantMessage();
      }
      // 如果找不到助手消息，创建一个新的
      if (!lastAssistantMessage) {
        lastAssistantMessage = {
          role: 'assistant',
          content: '',  // 初始化为空字符串，稍后会填充实际内容
          fullContent: '', // 同样初始化为空
          toolCalls: [],
          areToolsExpanded: false, // 默认不展开工具调用列表
          // timestamp: Date.now()
        };
        
        // 如果找到了最后一个用户消息，在其后插入助手消息
        if (lastUserMessageIndex !== -1) {
          messages.value.splice(lastUserMessageIndex + 1, 0, lastAssistantMessage);
        } else {
          // 如果没有找到用户消息，则添加到消息列表末尾
          messages.value.push(lastAssistantMessage);
        }
      }
      
      // 确保areToolsExpanded属性存在
      if (lastAssistantMessage.areToolsExpanded === undefined) {
        lastAssistantMessage.areToolsExpanded = false; // 默认不展开
      }
      // 在处理工具调用期间，自动展开工具调用列表
      if (lastAssistantMessage.isTyping) {
        lastAssistantMessage.areToolsExpanded = true; // 处理中状态，自动展开工具调用列表
      }
      
      // 将工具调用添加到助手消息的工具调用数组中
      if (!lastAssistantMessage.toolCalls) {
        lastAssistantMessage.toolCalls = [];
      }
      
      // 处理特殊工具：思考 和 分析
      const newToolCallId = `tool-${Date.now()}`;
      let toolData = {};
      let toolResult = null;
      // 检查是否是特殊工具：思考 或 分析
      if ((toolName === "思考" || toolName === "分析") && 
           ((data.tool && data.tool.tool_args) || (data.data && data.data.args))) {
        
        // 获取工具参数 - 处理不同的数据格式
        let toolArgs;
        if (data.tool && data.tool.tool_args) {
          // 来自SSE的原始格式
          toolArgs = data.tool.tool_args;
          console.log("从data.tool.tool_args获取参数:", toolArgs);
        } else if (data.data && data.data.args) {
          // 已处理的格式
          toolArgs = data.data.args;
          console.log("从data.data.args获取参数:", toolArgs);
        } else {
          toolArgs = {};
          console.log("找不到工具参数");
        }
        
        if (toolName === "思考") {
          // 处理思考工具
          toolResult = toolArgs.thought || "";
          console.log("思考内容:", toolResult);
          toolData = {
            name: "思考",
            toolCallId: data.tool ? data.tool.tool_call_id : (data.tool ? data.tool.id : newToolCallId),
            args: {
              thought: toolResult,
              title: toolArgs.title || "",  // 保留原始title
              confidence: toolArgs.confidence || 0
            }
          };
          // 思考工具有结果时立即标记为完成
          if (toolResult) {
            console.log("🧠 思考工具有结果，立即标记为完成");
          }
        } else if (toolName === "分析") {
          // 处理分析工具
          toolResult = toolArgs.analysis || "";
          console.log("分析内容:", toolResult);
          toolData = {
            name: "分析",
            toolCallId: data.tool ? data.tool.tool_call_id : (data.tool ? data.tool.id : newToolCallId),
            args: {
              analysis: toolResult,
              title: toolArgs.title || "",
              result: toolArgs.result || "",
              next_action: toolArgs.next_action || "",
              confidence: toolArgs.confidence || 0
            }
          };
          // 分析工具有结果时立即标记为完成
          if (toolResult) {
            console.log("📊 分析工具有结果，立即标记为完成");
          }
        }
      } else {
        // 处理普通工具
        toolData = data.data || {
          name: toolName || "未知工具", 
          toolCallId: data.tool && (data.tool.tool_call_id || data.tool.id) || newToolCallId,
          args: data.tool && data.tool.tool_args
        };
      }
      
      // 添加工具调用
      const toolCall = {
        id: newToolCallId,
        eventType: 'toolCallStart',
        data: toolData,
        result: "",
        isCompleted: false,
        isLoading: true, // 新增：初始化为loading状态
        // timestamp: Date.now()
      };

      // 如果有工具结果，直接添加并标记为完成（特别是思考、分析工具）
      if (toolResult) {
        toolCall.result = toolResult;
        // 对于有结果的特殊工具，立即标记为完成
        if (toolName === "思考" || toolName === "分析") {
          toolCall.isCompleted = true;
          toolCall.isLoading = false;
          console.log(`🎯 ${toolName}工具有结果，立即标记为完成`);
        }
      }

      // 检查是否已经存在相同工具调用
      const existingToolIndex = lastAssistantMessage.toolCalls.findIndex(t =>
        t.data && t.data.toolCallId === toolCall.data.toolCallId
      );

      if (existingToolIndex === -1) {
        // 新工具开始时，完成前面所有未完成的工具（特别是思考工具）
        lastAssistantMessage.toolCalls.forEach((prevTool, index) => {
          if (!prevTool.isCompleted) {
            console.log(`🔄 新工具开始，完成前面的工具: ${prevTool.data?.name || '未知工具'}`);
            completeToolLoading(prevTool);
          }
        });

        // 如果不存在，添加新的工具调用
        lastAssistantMessage.toolCalls.push(toolCall);
        // 开始工具loading
        startToolLoading(toolCall);
        console.log("添加工具调用1:", toolCall);
      } else {
        // 如果已存在，更新现有工具调用
        console.log("更新现有工具调用:", toolCall);
        const updatedTool = {
          ...lastAssistantMessage.toolCalls[existingToolIndex],
          ...toolCall
        };
        lastAssistantMessage.toolCalls[existingToolIndex] = updatedTool;
        // 开始工具loading
        startToolLoading(updatedTool);
      }
      break;
    case "ToolCallCompleted":
    case "toolCallEnd":
      // 查找最后一个用户消息之后的第一个助手消息
      let userMsgIndex = -1;
      let assistantMsg = null;
      
      // 查找最后一个用户消息的索引
      for (let i = messages.value.length - 1; i >= 0; i--) {
        if (messages.value[i].role === 'user') {
          userMsgIndex = i;
          break;
        }
      }
      // 在用户消息之后查找助手消息
      if (userMsgIndex !== -1) {
        for (let i = userMsgIndex + 1; i < messages.value.length; i++) {
          if (messages.value[i].role === 'assistant') {
            assistantMsg = messages.value[i];
            break;
          }
        }
      } else {
        // 如果没有找到用户消息，使用最后一条助手消息
        assistantMsg = findLastAssistantMessage();
      }
      // 获取工具调用ID和工具信息
      const toolCallId = data.data && data.data.toolCallId;
      const toolInfo = data.tool || (data.data && data.data);
      if (assistantMsg && assistantMsg.toolCalls) {
        // 查找对应的工具调用
        for (let i = 0; i < assistantMsg.toolCalls.length; i++) {
          const toolCall = assistantMsg.toolCalls[i];
          // 匹配工具调用ID
          if (toolCall.eventType === 'toolCallStart' && 
             (toolCall.data.toolCallId === toolCallId || 
              toolCall.id === toolCallId ||
              (toolInfo && toolInfo.tool_call_id && toolCall.data.toolCallId === toolInfo.tool_call_id))) {
            // 检查是否是网络搜索工具
            if (toolCall.data && toolCall.data.name === "网络搜索" && toolInfo && toolInfo.tool_result) {
              console.log("处理网络搜索工具结果:", toolInfo.tool_result);
              // 将 tool_result 保存到工具调用的 args 中，以便 getSearchResults 方法能够获取
              if (!toolCall.data.args) {
                toolCall.data.args = {};
              }
              toolCall.data.args.tool_result = toolInfo.tool_result;
              // 不设置 result，因为网络搜索工具使用特殊的展示方式
              completeToolLoading(toolCall); // 使用新的完成方法

              // 更新工具执行状态，确保下一个工具可以开始
              updateToolExecutionStatus(assistantMsg.toolCalls, i);

              console.log("网络搜索工具调用已完成，结果已保存到args中");
              break;
            }
            // 检查是否是特殊工具（思考、分析）
            const isSpecialTool = toolCall.data && 
              (toolCall.data.name === "思考" || 
               toolCall.data.name === "分析");
            // 如果不是特殊工具，或者是特殊工具但没有结果，则设置结果
            if (!isSpecialTool || !toolCall.result) {
              // 优先使用 tool.tool_result，其次使用 data.result
              const result = (toolInfo && toolInfo.tool_result) || (data.data && data.data.result);
              if (result) {
                toolCall.result = result;
              }
            }
            // 使用新的完成方法
            completeToolLoading(toolCall);

            // 更新工具执行状态，确保下一个工具可以开始
            updateToolExecutionStatus(assistantMsg.toolCalls, i);

            break;
          }
        }
        
        // 如果没有找到匹配的工具调用ID，尝试通过工具名称匹配最后一个未完成的同名工具
        if (toolInfo && toolInfo.tool_name) {
          const uncompletedTool = assistantMsg.toolCalls.find(tool => 
            !tool.isCompleted && 
            tool.data && 
            tool.data.name === toolInfo.tool_name
          );
          if (uncompletedTool) {
            // 特殊处理网络搜索工具
            if (toolInfo.tool_name === "网络搜索" && toolInfo.tool_result) {
              if (!uncompletedTool.data.args) {
                uncompletedTool.data.args = {};
              }
              uncompletedTool.data.args.tool_result = toolInfo.tool_result;
              completeToolLoading(uncompletedTool); // 使用新的完成方法

              // 找到工具在数组中的索引并更新执行状态
              const toolIndex = assistantMsg.toolCalls.findIndex(t => t === uncompletedTool);
              updateToolExecutionStatus(assistantMsg.toolCalls, toolIndex);

              console.log("通过工具名称匹配，网络搜索结果已保存");
            } else {
              // 其他工具的处理
              const isSpecialTool = uncompletedTool.data &&
                (uncompletedTool.data.name === "思考" ||
                 uncompletedTool.data.name === "分析");

              if ((!isSpecialTool || !uncompletedTool.result) && toolInfo.tool_result) {
                uncompletedTool.result = toolInfo.tool_result;
              }
              completeToolLoading(uncompletedTool); // 使用新的完成方法

              // 找到工具在数组中的索引并更新执行状态
              const toolIndex = assistantMsg.toolCalls.findIndex(t => t === uncompletedTool);
              updateToolExecutionStatus(assistantMsg.toolCalls, toolIndex);
            }
          }
        }
      }
      break;
    case "RunCompleted":
      console.log("Run Completed");
      break;
    case "summary":
      // 总结事件，只有当内容非空时才处理
      if (data.content !== undefined && data.content !== "") {
        // 查找最后一个用户消息的索引
        let lastUserIndex = -1;
        for (let i = messages.value.length - 1; i >= 0; i--) {
          if (messages.value[i].role === 'user') {
            lastUserIndex = i;
            break;
          }
        }
        
        // 创建新的摘要消息
        const summaryMsg = {
          role: 'assistant',
          content: data.content,
          isSummary: true,
          // timestamp: Date.now()
        };
        
        // 如果找到最后一个用户消息，在其后插入摘要
        if (lastUserIndex !== -1) {
          messages.value.splice(lastUserIndex + 1, 0, summaryMsg);
        } else {
          // 否则添加到末尾
          messages.value.push(summaryMsg);
        }
      }
      break;
    case "allCompleted":
      // 所有任务完成事件
      console.log("All tasks completed");
      finishCore();
      break;
    default:
      console.warn("未知的事件类型:", eventType);
      break;
  }
}

// 处理标准API的SSE消息格式
const handleStandardApiData = (data) => {
  // 从不同格式的响应中提取内容
  let content = '';
  if (data.choices && data.choices[0]) {
    if (data.choices[0].delta && data.choices[0].delta.content) {
      content = data.choices[0].delta.content;
    } else if (data.choices[0].content) {
      content = data.choices[0].content;
    }
  }
  if (content) {
    // 查找最后一条助手消息
    const lastMessage = findLastAssistantMessage();
    // 如果是第一条消息，添加一个新的消息项
    if (!lastMessage || !lastMessage.isTyping) {
      // 初始化打字机状态
      fullContent.value = content;
      displayedContent.value = '';
      isTyping.value = true;
      // 创建新消息
      const newAssistantMessage = {
        role: 'assistant',
        content: '',  // 初始为空，将通过打字机效果逐渐显示
        fullContent: content, // 存储完整内容
        isTyping: true,
        toolCalls: [], // 确保toolCalls属性
        areToolsExpanded: false, // 默认不展开工具调用列表
        // timestamp: Date.now()
      };
      messages.value.push(newAssistantMessage);
      // 开始打字机效果
      startTypewriterEffect();
    } else {
      // 检查是否有工具调用，并且是首次开始显示实际文本内容
      if (lastMessage.toolCalls && 
          lastMessage.toolCalls.length > 0 && 
          (lastMessage.content === '' || lastMessage.content === ' ')) {
        // 开始显示文本内容时，自动关闭工具调用列表
        lastMessage.areToolsExpanded = false;
        console.log("检测到开始显示文本内容，自动关闭工具调用列表");
      }
      // 更新最后一条消息的完整内容
      lastMessage.fullContent += content;
      fullContent.value = lastMessage.fullContent; // 确保全局状态匹配
      // 重启打字机效果以显示新内容
      startTypewriterEffect();
    }
  }
}

// 查找最后一条助手消息的辅助函数
const findLastAssistantMessage = () => {
  // 首先检查消息数组是否为空
  if (!messages.value || messages.value.length === 0) {
    return null;
  }
  
  // 从后向前查找最后一条助手消息
  for (let i = messages.value.length - 1; i >= 0; i--) {
    if (messages.value[i].role === 'assistant') {
      return messages.value[i];
    }
  }
  
  // 如果没有找到助手消息，返回null
  return null;
}

const finishCore = () => {
  console.error("finish sse-----------------------结束--");
  loading.value = false;
  
  // 如果还在打字，立即完成打字
  if (typingTimer.value) {
    clearInterval(typingTimer.value);
    typingTimer.value = null;
  }
  
  // 清理滚动防抖动计时器
  if (scrollDebounceTimer.value) {
    clearTimeout(scrollDebounceTimer.value);
    scrollDebounceTimer.value = null;
  }
  
  // 确保最后一条消息显示完整内容
  if (messages.value.length > 0) {
    // 查找最后一条助手消息
    let lastAssistantMessage = null;
    for (let i = messages.value.length - 1; i >= 0; i--) {
      if (messages.value[i].role === 'assistant') {
        lastAssistantMessage = messages.value[i];
        break;
      }
    }
    
    if (lastAssistantMessage) {
      console.log("完成消息处理，更新最终内容:", lastAssistantMessage);
      
      // 确保有完整内容
      if (lastAssistantMessage.fullContent) {
        lastAssistantMessage.content = lastAssistantMessage.fullContent;
        // console.log("设置最终消息内容:", lastAssistantMessage.content);
      } else if (displayedContent.value) {
        // 如果没有fullContent但有displayedContent，使用displayedContent
        lastAssistantMessage.content = displayedContent.value;
      }
      
      // 标记消息不再处于打字状态
      lastAssistantMessage.isTyping = false;
      
      // 确保所有工具调用都标记为已完成，包括工具打字效果
      if (lastAssistantMessage.toolCalls && lastAssistantMessage.toolCalls.length > 0) {
        lastAssistantMessage.toolCalls.forEach(toolCall => {
          if (!toolCall.isCompleted) {
            completeToolLoading(toolCall); // 使用新的完成方法
            console.log(`标记工具调用 ${toolCall.data?.name || '未知工具'} 为已完成`);
          }
          
          // 完成工具打字效果
          if (toolCall.isTyping && toolCall.typingTimer) {
            clearInterval(toolCall.typingTimer);
            toolCall.typingTimer = null;
            toolCall.isTyping = false;
            // 确保显示完整结果
            if (toolCall.fullResult) {
              toolCall.result = toolCall.fullResult;
            }
            console.log(`完成工具 ${toolCall.data?.name || '未知工具'} 的打字效果`);
          }
        });
        
        // 处理完成时自动关闭工具调用列表
        lastAssistantMessage.areToolsExpanded = false;
        console.log("处理完成，自动关闭工具调用列表");
      }
      
      // 确保消息内容显示（即使为空也要占位）
      if (!lastAssistantMessage.content || lastAssistantMessage.content.trim() === '') {
        console.log("消息内容为空，设置占位内容");
        lastAssistantMessage.content = ' '; // 使用空格而非空字符串，确保渲染
      }
      // 最后一次滚动到底部，确保完整内容可见
      nextTick(() => {
        scrollToBottom();
      });
    }
  }
  
  // 重置打字机状态
  isTyping.value = false;
  displayedContent.value = '';
  fullContent.value = '';

}

// 打字机效果函数
const startTypewriterEffect = () => {
  if (typingTimer.value) {
    clearInterval(typingTimer.value);
  }
  // 确保打字机状态正确
  isTyping.value = true;
  // 如果还没有开始打字，确保初始化
  if (displayedContent.value.length === 0) {
    displayedContent.value = '';
  }
  
  console.log("启动打字机效果，当前内容:", displayedContent.value, "目标内容:", fullContent.value);
  
  // 设置打字机定时器
  typingTimer.value = setInterval(() => {
    if (displayedContent.value.length < fullContent.value.length) {
      // 计算当前应该添加的字符数量，根据内容长度动态调整
      // 内容越长，每次添加的字符越多，以减少渲染次数
      const remainingChars = fullContent.value.length - displayedContent.value.length;
      const charsToAdd = Math.min(Math.max(1, Math.floor(remainingChars / 20)), 5);
      
      // 添加多个字符
      const nextChars = fullContent.value.substr(displayedContent.value.length, charsToAdd);
      displayedContent.value += nextChars;
      
      // 更新消息内容 - 查找最后的助手消息
      let lastAssistantMessage = null;
      for (let i = messages.value.length - 1; i >= 0; i--) {
        if (messages.value[i].role === 'assistant') {
          lastAssistantMessage = messages.value[i];
          break;
        }
      }
      
      // 如果找到了助手消息，更新其内容
      if (lastAssistantMessage && lastAssistantMessage.isTyping) {
        lastAssistantMessage.content = displayedContent.value;
        // 确保内容长度不为0，以使气泡显示
        if (lastAssistantMessage.content.length === 0) {
          lastAssistantMessage.content = ' ';
        }
      }
    } else {
      // 打字完成，清除定时器
      clearInterval(typingTimer.value);
      typingTimer.value = null;
      console.log("打字效果完成，最终内容:", displayedContent.value);
    }
  }, typingSpeed.value);
}

// 工具响应打字机效果函数
const startToolTypewriterEffect = (toolCall, fullContent) => {
  // 清除可能存在的工具打字机定时器
  if (toolCall.typingTimer) {
    clearInterval(toolCall.typingTimer);
  }

  // 清除可能存在的滚动定时器
  if (toolCall.scrollTimer) {
    clearTimeout(toolCall.scrollTimer);
  }

  // 初始化打字机状态
  if (!toolCall.displayedResult) {
    toolCall.displayedResult = '';
  }

  console.log("启动工具打字机效果，当前内容:", toolCall.displayedResult, "目标内容:", fullContent);

  // 滚动计数器，用于控制滚动频率
  let scrollCounter = 0;

  // 设置工具打字机定时器
  toolCall.typingTimer = setInterval(() => {
    if (toolCall.displayedResult.length < fullContent.length) {
      // 计算当前应该添加的字符数量
      const remainingChars = fullContent.length - toolCall.displayedResult.length;
      const charsToAdd = Math.min(Math.max(1, Math.floor(remainingChars / 20)), 3);

      // 添加字符
      const nextChars = fullContent.substr(toolCall.displayedResult.length, charsToAdd);
      toolCall.displayedResult += nextChars;

      // 更新显示的结果
      toolCall.result = toolCall.displayedResult;

      // 对于工具内容，使用更频繁的滚动：每次更新都滚动，确保用户能看到最新内容
      scrollCounter++;
      if (scrollCounter >= 1) {
        scrollCounter = 0;
        nextTick(() => {
          // 滚动主消息列表到底部，确保工具消息可见
          scrollToBottomForTool();
          // 同时滚动工具内容区域到底部
          scrollToolContentToBottom();
        });
      }
    } else {
      // 打字完成，清除定时器
      clearInterval(toolCall.typingTimer);
      toolCall.typingTimer = null;
      toolCall.isTyping = false;
      toolCall.result = fullContent;
      console.log("工具打字效果完成，最终内容:", toolCall.result);
      // 工具打字完成后最后一次滚动到底部
      nextTick(() => {
        scrollToBottomForTool();
        scrollToolContentToBottom();
      });
    }
  }, 50); // 工具响应打字速度稍快一些
}

/**
 * PC设置键盘事件监听器
 */
 const setupKeyboardListener = () => {
  if (isPCDevice.value && typeof document !== 'undefined') {
    const handleKeydown = (event) => {
      // 判断按下的键是否是回车键（键码为13）
      if (!event.shiftKey && event.keyCode == 13) {
        event.preventDefault();
        sendMessage();
      }
    };
    // 注册键盘事件监听器
    document.addEventListener("keydown", handleKeydown);
    // 返回销毁函数
    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  }
};

// 发送消息
const sendMessage = async () => {
  // 检查用户是否登录
  if (!checkLoginStatus()) {
    return
  }
  if (loading.value) return;
  if (!inputMessage.value.trim() && uploadedFiles.value.length === 0) return;
  // 获取当前消息内容
  let userMessage = inputMessage.value.trim();
  // 清除所有正在输入中的AI消息，避免干扰新的对话
  messages.value = messages.value.filter(msg => !msg.isTyping);
  // console.error('发送消息:', userMessage, '已上传文件:', uploadedFiles.value);
  // 处理上传的文件和消息
  if (uploadedFiles.value.length > 0) {
    // 创建mix类型消息的内容数组
    const mixContentArray = [];
    // 添加所有文件到mix内容数组
    for (const file of uploadedFiles.value) {
      const fileType = file.filename ? file.filename.split('.').pop().toLowerCase() : 'file';
      const fileObj = {
        content: file.downloadSignUrl || file.tempUrl || file.url,
        content_type: fileType,
        attachment_id: file.attachment_id,
        attachment: {
          id: file.attachment_id,
          name: file.name || file.filename || '未命名文件',
          type: fileType,
          url: file.url || '',
          size: file.size || 0,
          downloadSignUrl: file.downloadSignUrl || ''
        }
      };
      mixContentArray.push(fileObj);
    }
    // 如果有文本消息，也添加到mix内容数组
    if (userMessage) {
      mixContentArray.push({
        content: userMessage,
        content_type: 'str'
      });
    }
    // 创建mix类型的消息对象 - 显示时需要包含技能信息
    let displayMessage = userMessage;
    if (selectedSkillValue.value && skillText.value) {
      displayMessage = `${skillText.value}：${userMessage}`;
    }
    const mixMessage = {
      role: 'user',
      content: JSON.stringify(mixContentArray),
      content_type: 'mix',
      is_robot: 0,
      created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
      attachment: null,
    };
    // 添加mix类型消息到消息数组
    messages.value.push(mixMessage);
  } else if (userMessage) {
    // 如果只有文本消息，添加普通文本消息 - 显示时需要包含技能信息
    let displayMessage = userMessage;
    if (selectedSkillValue.value && skillText.value) {
      displayMessage = `${skillText.value}：${userMessage}`;
    }
    const userMessageObj = {
      role: 'user',
      content: displayMessage,
      content_type: 'str',
      // timestamp: Date.now()
    };
    messages.value.push(userMessageObj);
  }
  // 立即添加一个空的助手消息，显示加载状态
  const assistantLoadingMessage = {
    role: 'assistant',
    content: '',  // 空内容，显示加载动画
    isTyping: true,
    content_type: 'str',
    toolCalls: [], // 空工具调用数组
    // timestamp: Date.now()
  };
  messages.value.push(assistantLoadingMessage);
  // 设置加载状态
  loading.value = true;
  try {
    // 🚀 在发送消息前立即停止语音识别，防止回显
    // #ifdef APP-PLUS
    if (isVoiceInputActive.value) {
      console.log('📤 发送消息前停止APP端语音识别');
      stopAppVoiceRecognition();
    }
    // #endif
    // #ifdef H5
    // if (isVoiceInputActive.value) {
    //   console.log('📤 发送消息前停止H5端语音识别');
    //   stopVoiceRecognition();
    // }
    // #endif
    // 使用新的文件上传方案 - 直接使用本地文件列表
    const files = uploadedFiles.value;
    console.log('📁 发送消息时的文件列表:', files);
    let oldInputMessage = inputMessage.value
    // 清空输入框
    inputMessage.value = '';
    // 构建技能信息对象
    const skillInfo = selectedSkillValue.value && skillText.value ? {
      value: selectedSkillValue.value,
      label: skillText.value,
      example: skillExample.value
    } : null;
    // 获取任务ID，传入消息内容、文件列表、技能信息和会话ID
    await fetchTaskId(oldInputMessage.trim(), files, skillInfo, currentSession.value.session_id);
    // 发送请求
    await sendRequest();
    // 消息发送成功后，重置技能选择器状态
    if (skillSelectorRef.value) {
      skillSelectorRef.value.resetState();
    }
    // APP h5 消息发送成功后，重置语音识别会话
    // #ifdef H5
    voiceRecognitionService.resetSession();
    // #endif
    // 清空文件列表 - 使用新的方案直接清空本地列表
    uploadedFiles.value = [];
    // 滚动到底部，立即显示用户消息和加载状态
    scrollToBottom();
  } catch (error) {
    console.error('发送消息失败:', error);
    // 发送失败时，显示错误提示但不重置语音识别状态
    uni.showToast({
      title: '发送消息失败，请稍后重试',
      icon: 'none'
    });
    loading.value = false;
  }
}

// 发送请求
async function sendRequest() {
  try {
    // 清空输入框
    // inputMessage.value = '';
    // 更新UI状态（确保图标显示正确）
    isVoiceInputActive.value = false;
    // console.log('messages.value', messages.value);
    console.log('uploadedFiles', uploadedFiles.value);
    // loading.value已经在sendMessage中设置，这里只设置openLoading状态
    // 获取 task_id 或使用默认值
    const task_id = uni.getStorageSync("task_id") || ""; 
    // 准备请求体
    const requestBody = {
      id: task_id,
      stream: true,
    };
    // 根据环境配置构造请求的 URL
    const baseUrl = envConfig.baseURL || 'https://apitest.profly.com.cn';
    const apiPrefix = envConfig.apiPrefix || 'api';
    const url = `${baseUrl}/${apiPrefix}/task/results`;
    console.log('请求URL:', url); // 添加日志，便于调试
    // 获取 token
    const token = uni.getStorageSync("token");
    // 连接 SSE 等待 SSE 连接启动完成
    await chatSSEClientRef.value.startChat({
      url: url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token,
      },
      method: 'post',
      body: requestBody
    });
    // 发送后清空文件列表
    uploadedFiles.value = [];
  } catch (error) {
    console.error('请求失败:', error);
    uni.showToast({
      title: '发送消息失败，请稍后重试',
      icon: 'none'
    });
    loading.value = false;
  }
  // 注意：不在finally中清除loading状态，会在finishCore中处理
}

// 获取聊天历史记录
async function fetchChatHistory() {
  try {
    if (historyLoading.value || !hasMoreHistory.value) return;
    historyLoading.value = true;
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      session_id: currentSession.value.session_id || '', // 会话ID参数
    };
    const response = await getChatHistoryList(params);
    // const response = await import("./test.json"); // 本地调试用
    // console.log("聊天历史响应:", response);
    if (response.code === 0 && response.data) {
      const historyData = response.data.list || [];
      if (historyData.length === 0) {
        // 没有更多历史消息
        hasMoreHistory.value = false;
      } else {
        if (historyData.length < pageSize.value) {
          // 返回的数据少于每页数量，说明已经是最后一页
          hasMoreHistory.value = false;
        }
        // 处理每一条历史记录项
        let processedMessages = [];
        
        // 遍历历史数据
        for (let i = 0; i < historyData.length; i++) {
          const item = historyData[i];
          // 判断消息类型：用户消息 vs AI消息
          if (item.is_robot === 0) {
            // 处理文件或图片类型消息
              processedMessages.push({
                role: 'user',
                id: item.id,
                content: item.content || '',
                content_type: item.content_type,
                attachment: item.attachment,
                created_time: item.created_time,
                // 添加点赞点踩状态
                is_like: item.is_like || 0,
                is_dislike: item.is_dislike || 0,
                dislike_reason: item.dislike_reason || ''
              });
          } else {
            // 处理AI消息 (is_robot === 1 或其他值)
            let content = item.content;
            let parsedContent = content;
            
            // 尝试解析内容是否为JSON (数组或对象)
            if (typeof content === 'string') {
              try {
                if (content.startsWith('[') || content.startsWith('{')) {
                  parsedContent = JSON.parse(content);
                  // console.log("成功解析AI消息内容为JSON:", parsedContent);
                }
              } catch (e) {
                console.warn("AI消息内容解析失败，使用原始内容:", e);
                parsedContent = content;
              }
            }
            
            // 处理不同格式的AI消息内容
            if (Array.isArray(parsedContent)) {
              // 处理数组格式 - 可能包含tool和assistant混合内容
              const toolCalls = [];
              let assistantContent = '';
              
              // 遍历数组中的每个项
              parsedContent.forEach(entry => {
                if (entry.role === 'tool' && entry.content) {
                  // 处理工具调用 - 创建toolCall对象
                  try {
                    // 确保content是对象
                    const toolContent = typeof entry.content === 'string' 
                      ? JSON.parse(entry.content) 
                      : entry.content;
                    
                    // 创建工具数据对象
                    const toolData = {
                      name: toolContent.tool_name || '未知工具',
                      toolCallId: toolContent.tool_call_id || `tool-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                      args: toolContent.tool_args || {}
                    };
                    
                    // 创建工具调用对象
                    const toolCall = {
                      id: toolData.toolCallId,
                      eventType: 'toolCallStart',
                      data: toolData,
                      isExpanded: true,
                      isCompleted: true,
                      isLoading: false, // 历史消息中的工具已完成，不显示loading
                    };
                    
                    // 特殊处理"思考"、"分析"和"网络搜索"工具
                    if (toolData.name === '思考' && toolData.args.thought) {
                      toolCall.result = toolData.args.thought;
                    } else if (toolData.name === '分析' && toolData.args.analysis) {
                      toolCall.result = toolData.args.analysis;
                    } else if (toolData.name === '网络搜索' && toolContent.tool_result) {
                      // 网络搜索工具需要保存tool_result到data.args中
                      toolCall.data.args.tool_result = toolContent.tool_result;
                    } else if (toolData.name === '网络深度搜索' && toolContent.tool_result) {
                      toolCall.result = toolContent.tool_result;
                    }
                    
                    // 添加到工具调用列表
                    toolCalls.push(toolCall);
                    // console.log("添加工具调用2:", toolCall);
                  } catch (e) {
                    console.error("处理工具调用失败:", e, entry);
                  }
                } else if (entry.role === 'assistant') {
                  // 处理助手消息 - 保存文本内容
                  assistantContent = entry.content || '';
                }
              });
              
              // 创建助手消息对象，包含工具调用和文本内容
              if (assistantContent || toolCalls.length > 0) {
                processedMessages.push({
                  role: 'assistant',
                  id: item.id,
                  content: assistantContent,
                  content_type: item.content_type,
                  attachment: item.attachment,
                  toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                  created_time: item.created_time,
                  // 添加点赞点踩状态
                  is_like: item.is_like || 0,
                  is_dislike: item.is_dislike || 0,
                  dislike_reason: item.dislike_reason || ''
                  // timestamp: new Date(item.created_time || Date.now()).getTime()
                });
              }
            } else if (typeof parsedContent === 'object' && parsedContent !== null) {
              // 处理单个对象
              if (parsedContent.role === 'assistant') {
                // 单个助手消息对象
                processedMessages.push({
                  role: 'assistant',
                  id: item.id,
                  content: parsedContent.content || '',
                  // 添加点赞点踩状态
                  is_like: item.is_like || 0,
                  is_dislike: item.is_dislike || 0,
                  dislike_reason: item.dislike_reason || ''
                });
              } else {
                // 其他类型的对象，转为字符串处理
                processedMessages.push({
                  role: 'assistant',
                  id: item.id,
                  content: JSON.stringify(parsedContent),
                  // 添加点赞点踩状态
                  is_like: item.is_like || 0,
                  is_dislike: item.is_dislike || 0,
                  dislike_reason: item.dislike_reason || ''
                  // timestamp: new Date(item.created_time || Date.now()).getTime()
                });
              }
            } else {
              // 处理AI发送的文件或图片类型消息
              processedMessages.push({
                role: 'assistant',
                id: item.id,
                content: item.content || '',
                content_type: item.content_type,
                attachment: item.attachment,
                created_time: item.created_time,
                // 添加点赞点踩状态
                is_like: item.is_like || 0,
                is_dislike: item.is_dislike || 0,
                dislike_reason: item.dislike_reason || ''                
              });
            }
          }
        }
        
        // 按时间戳排序，确保消息按时间顺序显示
        processedMessages.sort((a, b) => a.id - b.id);
        
        // 处理对话配对，确保AI回复总是跟在用户问题之后
        const orderedMessages = [];
        const userMessages = [];
        const assistantMessages = [];
        
        // 分离用户消息和助手消息
        processedMessages.forEach(msg => {
          if (msg.role === 'user') {
            // 如果有积累的助手消息，先处理
            if (assistantMessages.length > 0) {
              // 将所有未配对的助手消息添加到有序数组
              orderedMessages.push(...assistantMessages);
              assistantMessages.length = 0; // 清空助手消息数组
            }
            // 添加用户消息
            userMessages.push(msg);
          } else if (msg.role === 'assistant') {
            // 如果有用户消息，将助手消息与最后一个用户消息配对
            if (userMessages.length > 0) {
              // 将用户消息添加到有序数组
              orderedMessages.push(...userMessages);
              userMessages.length = 0; // 清空用户消息数组
              // 添加助手消息
              orderedMessages.push(msg);
            } else {
              // 如果没有用户消息，暂存助手消息
              assistantMessages.push(msg);
            }
          }
        });
        
        // 处理剩余的消息
        if (userMessages.length > 0) {
          orderedMessages.push(...userMessages);
        }
        if (assistantMessages.length > 0) {
          orderedMessages.push(...assistantMessages);
        }
        
        console.log("处理后的历史消息:", orderedMessages);
        
        // 如果是第一页，替换消息，否则追加
        if (currentPage.value === 1) {
          messages.value = orderedMessages;
        } else {
          // 将新的历史消息添加到数组前面
          messages.value.unshift(...orderedMessages);
        }
        // 增加页码，用于加载更多历史消息
        currentPage.value++;
      }
    } else {
      console.warn("获取聊天历史失败:", response.message);
    }
  } catch (error) {
    console.error("获取聊天历史失败:", error);
  } finally {
    historyLoading.value = false;
  }
}

// 停止生成
const stopMessage = async () => {
  // 先停止前端的聊天流
  chatSSEClientRef.value.stopChat();
  console.warn("stop停止生成");
  loading.value = false;
  // 调用后端停止任务接口
  try {
    const taskId = uni.getStorageSync('task_id');
    if (taskId) {
      const response = await stopTask({ task_id: taskId });
      if (response.code === 0) {
        console.log('任务停止成功:', response.msg);
      } else {
        console.warn('任务停止失败:', response.msg);
      }
    }
  } catch (error) {
    console.error('调用停止任务接口失败:', error);
  }
  
  // 停止打字机效果
  if (typingTimer.value) {
    clearInterval(typingTimer.value);
    typingTimer.value = null;
  }
  // 确保最后一条消息显示当前已打出的内容
  if (messages.value.length > 0) {
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage.role === 'assistant') {
      // 使用已经打出的内容作为最终内容
      lastMessage.content = displayedContent.value || lastMessage.content || ' ';
      lastMessage.isTyping = false; // 设置为false，使操作按钮显示
      // 确保所有工具调用都标记为已完成
      if (lastMessage.toolCalls && lastMessage.toolCalls.length > 0) {
        lastMessage.toolCalls.forEach(toolCall => {
          if (!toolCall.isCompleted) {
            completeToolLoading(toolCall); // 使用新的完成方法
            console.log(`手动停止：标记工具调用 ${toolCall.data?.name || '未知工具'} 为已完成`);
          }
        });
        // 停止时自动关闭工具调用列表
        lastMessage.areToolsExpanded = false;
      }
    }
  }
  // 重置打字机状态
  isTyping.value = false;
}

/**
 * 滚动到底部 - 通过组件引用调用，简化版本
 */
const scrollToBottom = async () => {
  try {
    if (chatScrollRef.value) {
      // 更新输入框容器高度
      if (chatInputContainerRef.value && chatInputContainerRef.value.$el) {
        let containerHeight = 0;
        const browserType = detectBrowser();
        const element = chatInputContainerRef.value.$el;
        // 确保元素存在且有offsetHeight属性
        if (element && typeof element.offsetHeight === 'number') {
          // 根据不同环境调整容器高度计算方式
          if (browserType === 'Apple Safari') {
            containerHeight = element.offsetHeight;
          } else if (browserType === 'App') {
            // App端环境的高度计算
            containerHeight = element.offsetHeight;
          } else {
            // 其他浏览器环境
            containerHeight = element.offsetHeight - 121;
          }
        } else {
          // 如果无法获取offsetHeight，使用默认值
          containerHeight = 60; // 默认输入框高度
        }
        inputContainerHeight.value = `${containerHeight}px`;
      }
      // 等待DOM更新
      await nextTick();
      await chatScrollRef.value.scrollToBottom();
    }
  } catch (error) {
    console.error('❌ 主页面滚动失败:', error);
  }
}

/**
 * 工具内容专用滚动函数 - 更及时的滚动响应
 */
const scrollToBottomForTool = async () => {
  try {
    if (chatScrollRef.value) {
      // 直接滚动，不更新输入框高度以提高性能
      await nextTick();
      await chatScrollRef.value.scrollToBottom();
    }
  } catch (error) {
    console.error('❌ 工具内容滚动失败:', error);
  }
}

/**
 * 滚动工具内容区域到底部
 * 通过直接操作DOM元素来实现工具内容区域的滚动
 */
const scrollToolContentToBottom = async () => {
  try {
    await nextTick();

    // #ifdef H5
    // 在H5环境下，可以直接操作DOM
    setTimeout(() => {
      const toolContentElements = document.querySelectorAll('.tool-call-content');
      if (toolContentElements.length > 0) {
        // 滚动最后一个工具内容区域（通常是正在更新的那个）
        const lastToolContent = toolContentElements[toolContentElements.length - 1];
        if (lastToolContent) {
          lastToolContent.scrollTop = lastToolContent.scrollHeight;
          console.log('H5环境：工具内容区域滚动到底部');
        }
      }
    }, 50);
    // #endif

    // #ifdef APP-PLUS
    // 在APP环境下，使用uni.createSelectorQuery
    const query = uni.createSelectorQuery();
    query.selectAll('.tool-call-content').scrollOffset();
    query.exec((res) => {
      if (res && res[0] && res[0].length > 0) {
        console.log('APP环境：检测到工具内容区域，尝试滚动');
        // 在APP环境下，滚动控制有限，主要依靠主滚动区域
        scrollToBottomForTool();
      }
    });
    // #endif

  } catch (error) {
    console.error('❌ 工具内容区域滚动失败:', error);
  }
}

// 输入框焦点事件
const onInputFocus = async () => {
  // 输入框获得焦点时滚动到底部
  await scrollToBottom();
}

// 输入文字的时候触发
const onInputChange = async (e) => {
  // 不再重置语音识别会话，允许手动输入与语音输入混合使用
  // console.log('输入框内容变更:', inputMessage.value);
  // 实时同步输入框内容到语音识别服务
  // 这样当用户手动修改输入框内容后再次进行语音输入时，会基于最新的输入内容
  if (isVoiceInputActive.value) {
    // 如果当前正在进行语音识别，不进行同步避免干扰
    console.log('正在进行语音识别，暂不同步输入框内容');
  } else {
    // 当不在语音识别状态时，同步输入框内容到语音识别服务
    voiceRecognitionService.updateInputText(inputMessage.value);
  }
  // 更新消息列表的底部间距
  await nextTick();
  // 移除输入时的自动滚动，避免抖动
}

// 输入框失去焦点时的处理
const onInputBlur = async () => {
  // 输入框失去焦点时也同步一次内容到语音识别服务
  if (!isVoiceInputActive.value) {
    voiceRecognitionService.updateInputText(inputMessage.value);
  }
  // 更新消息列表的底部间距
  await nextTick();
  // 移除失焦时的自动滚动，避免抖动
}

// 清空上下文，不清空输入框
const clearInputContext = async () => {
  try {
    const params = {
      session_id:currentSession.value.session_id || ''
    };
    const res = await clearContext(params);
    if (res.code === 0) {
      uni.showToast({
        title: '上下文已清空',
        icon: 'success'
      });
      // 重置之前的状态
      historyLoading.value = false; 
      hasMoreHistory.value = true;
      currentPage.value = 1;
      pageSize.value = 40;
      await fetchChatHistory(); // 重新获取聊天历史
      // 输入框获得焦点时滚动到底部
      await scrollToBottom();
    }
  } catch (error) {
    console.error('清空上下文失败:', error);
    uni.showToast({
      title: '清空失败，请重试',
      icon: 'none'
    });
  }
}

// 切换技能
const toggleSkillSearchStatus = () => {
  // 检查用户是否登录
  if (!checkLoginStatus()) {
    return
  }
  // 打开技能选择弹出层
  skillSelectorRef.value?.open()
}

// #ifdef H5
// H5端语音输入方法 开始语音输入
const startVoiceInput = async () => {
  // 使用语音识别服务
  try {
    // 检查用户是否登录
    if (!checkLoginStatus()) {
      return
    }
    let VoiceToken = uni.getStorageSync("aliyunToken")
    if (VoiceToken) {
      // 🚀 立即更新UI状态，让用户感知系统正在响应
      isVoiceInputActive.value = true;
      
      // 在启动语音识别前，确保使用最新的输入框内容
      voiceRecognitionService.updateInputText(inputMessage.value);
      // 初始化语音识别服务，设置回调函数
      voiceRecognitionService.init(
        // 文本识别回调
        (text, isFinal, isError) => {
          if (!isError) {
            // 将识别的文本设置到输入框
            inputMessage.value = text;
          }
        },
        // 状态变化回调
        (active, text, stoppedDueToSilence) => {
          isVoiceInputActive.value = active;
          if (!active) {
            // 语音识别结束且有文本时，更新输入框
            if (text) {
              inputMessage.value = text;
            }          
            // 如果是因为静音超时而停止，确保UI状态立即更新
            if (stoppedDueToSilence) {
              console.log('语音识别因静音超时而停止');
              isVoiceInputActive.value = false;
            }
          }
        },
        // 传递当前输入框中的文本作为初始文本
        inputMessage.value,
        {
          token:VoiceToken
        }
      );
      // 切换语音输入，不需要重置会话，保留之前的文本
      await voiceRecognitionService.toggleVoiceInput();
    }
  } catch (error) {
    console.error('启动语音识别失败:', error);
    uni.showToast({
      title: error.message || '语音识别启动失败',
      icon: 'none'
    });
    // 发生错误时确保UI状态正确
    isVoiceInputActive.value = false;
  }
};

// 停止语音识别
const stopVoiceRecognition = () => {
  voiceRecognitionService.stopRecognition();
};
// #endif

// #ifdef APP-PLUS
/**
 * APP端开始语音输入（点击模式）（与H5端逻辑保持一致）
 */
const startAppVoiceInput = async () => {
  try {
    // 检查用户是否登录
    if (!checkLoginStatus()) {
      return
    }
    console.log("APP端开始语音识别");
    // 🚀 立即更新UI状态，让用户感知系统正在响应
    isVoiceInputActive.value = true;
    // 重置停止标志，允许语音识别更新输入框
    isAppVoiceRecognitionStopped.value = false;
    // 保存开始识别时的输入框内容作为基础文本
    appAccumulatedText.value = inputMessage.value;
    // 使用plus.speech进行语音识别
    plus.speech.startRecognize({
      engine: "baidu",
      lang: "zh-cn",
      userInterface: false,
      continue: true, // 连续识别模式，实现实时识别
    });
  } catch (error) {
    console.error('启动APP端语音识别失败:', error);
    uni.showToast({
      title: error.message || '语音识别启动失败',
      icon: 'none'
    });
    // 发生错误时确保UI状态正确
    isVoiceInputActive.value = false;
    isAppVoiceRecognitionStopped.value = true;
  }
};

/**
 * APP端停止语音识别（点击模式）
 */
const stopAppVoiceRecognition = () => {
  console.log("APP端停止语音识别");
  // 设置停止标志，防止后续的识别回调更新输入框
  isAppVoiceRecognitionStopped.value = true;
  // 停止语音识别
  plus.speech.stopRecognize();
  // 立即更新UI状态
  isVoiceInputActive.value = false;
};

/**
 * APP端语音识别开始回调
 */
const onAppSpeechStart = () => {
  console.log("APP语音识别事件: start");
  // 开始识别时不需要额外操作，UI状态已经在startAppVoiceInput中设置
};

/**
 * APP端音量变化回调
 * @param {Object} e - 音量事件对象
 */
const onAppVolumeChange = (e) => {
  // 音量变化用于UI反馈，暂时保留但不做特殊处理
  // console.log("音量变化用于UI反馈: volumeChange", e.volume);
};

/**
 * APP端识别中回调（实时识别）
 * @param {Object} e - 识别事件对象
 */
const onAppRecognizing = (e) => {
  // 检查语音识别是否已被停止，如果是则不更新输入框
  if (isAppVoiceRecognitionStopped.value) {
    console.log("APP语音识别已停止，忽略实时识别结果:", e.partialResult);
    return;
  }
  if (e.partialResult) {
    // 实时更新输入框内容：基础文本 + 当前识别的部分结果
    const currentText = appAccumulatedText.value ?
    appAccumulatedText.value + e.partialResult : e.partialResult;
    inputMessage.value = currentText;
    console.log("APP语音识别实时结果:", e.partialResult);
  }
};

/**
 * APP端识别完成回调（一句话识别完成）
 * @param {Object} e - 识别事件对象
 */
const onAppRecognition = (e) => {
  // 检查语音识别是否已被停止，如果是则不更新输入框
  if (isAppVoiceRecognitionStopped.value) {
    console.log("APP语音识别已停止，忽略识别完成结果:", e.result);
    return;
  }
  if (e.result) {
    // 将完整的识别结果累积到基础文本中
    appAccumulatedText.value = appAccumulatedText.value ?
    appAccumulatedText.value + e.result : e.result;
    // 更新输入框内容
    inputMessage.value = appAccumulatedText.value;
    console.log("APP累积文本:", appAccumulatedText.value);
  }
};

/**
 * APP端识别结束回调
 */
const onAppSpeechEnd = () => {
  console.log("APP语音识别事件: end");
  // 更新UI状态
  isVoiceInputActive.value = false;
  // 清空累积文本，为下次识别做准备
  appAccumulatedText.value = "";
};
// #endif

// 旧的fileUploadService相关代码已完全移除，现在使用xe-upload方案
const openFilePicker = async () => {
  // 检查用户是否登录
  if (!checkLoginStatus()) {
    return
  }
  // 定义文件数量限制常量
  const MAX_FILE_COUNT = 9;
  const currentFileCount = uploadedFiles.value.length;
  // 检查当前已上传文件数量是否已达到上限
  if (currentFileCount >= MAX_FILE_COUNT) {
    uni.showModal({
      title: '文件数量限制',
      content: `最多只能上传${MAX_FILE_COUNT}个文件，您当前已上传${currentFileCount}个文件。请先删除部分文件后再上传新文件。`,
      showCancel: false,
      confirmText: '我知道了',
      confirmColor: '#007AFF'
    });
    return;
  }
  // 计算还能上传的文件数量
  const remainingCount = MAX_FILE_COUNT - currentFileCount;
  try {
    // 使用新的 hooks 选择并上传文件
    const results = await chooseAndUpload('file', {
      count: remainingCount, // 动态设置可选择的文件数量
      // APP端支持的文件扩展名
      // #ifdef H5
      extension: [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', // 图片
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', // 文档
        '.txt', '.rtf', // 文本
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv','.m4a', // 视频
        '.mp3', '.wav', '.aac', '.flac', // 音频
        '.zip', '.rar', '.7z', '.tar', '.gz' // 压缩包
      ],
      // #endif
      // #ifdef APP
      extension:'*',
      // #endif
    });

    console.log('xe-upload 原始上传结果:', results);

    if (results && results.length > 0) {
      // 检查是否超出限制（双重保险）
      const totalAfterUpload = currentFileCount + results.length;
      if (totalAfterUpload > MAX_FILE_COUNT) {
        // 只处理允许的文件数量
        const allowedResults = results.slice(0, remainingCount);
        const rejectedCount = results.length - allowedResults.length;
        // 显示限制提示
        uni.showModal({
          title: '文件数量超出限制',
          content: `您选择了${results.length}个文件，但最多只能上传${MAX_FILE_COUNT}个文件。已为您上传前${allowedResults.length}个文件，其余${rejectedCount}个文件已忽略。`,
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#007AFF'
        });
        // 转换为兼容的数据结构并获取 attachment_id
        const processedFiles = await processUploadResults(allowedResults);
        console.log('处理后的文件列表:', processedFiles);
        // 更新本地文件列表
        uploadedFiles.value = [...uploadedFiles.value, ...processedFiles];
      } else {
        // 正常处理所有文件
        const processedFiles = await processUploadResults(results);
        console.log('处理后的文件列表:', processedFiles);

        // 更新本地文件列表
        uploadedFiles.value = [...uploadedFiles.value, ...processedFiles];
        // 显示成功提示（如果接近上限）
        const finalCount = uploadedFiles.value.length;
        if (finalCount >= MAX_FILE_COUNT - 1) {
          uni.showToast({
            title: `已上传${results.length}个文件，还可上传${MAX_FILE_COUNT - finalCount}个`,
            icon: 'none',
            duration: 3000
          });
        }
      }
    }
  } catch (error) {
    console.error('💥 文件上传过程出错:', error);
    uni.showToast({
      title: '文件上传失败',
      icon: 'none'
    });
  }
};

/**
 * 处理上传结果，转换为兼容的数据结构
 * @param {Array} uploadResults - xe-upload 的上传结果
 * @returns {Promise<Array>} 处理后的文件列表
 */
const processUploadResults = async (uploadResults) => {
  const processedFiles = [];

  for (const result of uploadResults) {
    try {
      console.log('处理上传结果:', result);

      // 从 xe-upload 结果中提取信息
      const fileName = result.fileName || result.file?.name || 'unknown';
      const fileUrl = result.url || result.fullUrl;
      const fileSize = result.file?.size || 0;
      const fileType = result.file?.type || 'application/octet-stream';

      // 调用生成下载签名接口获取预览地址
      let downloadSignUrl = '';
      let previewUrl = '';

      try {
        const downloadSignatureRes = await generateFileDownloadSignature({
          object: result.url // 使用上传返回的 object 路径
        });

        console.log('生成下载签名结果:', downloadSignatureRes);

        if (downloadSignatureRes.code === 0 && downloadSignatureRes.data?.sign_url) {
          downloadSignUrl = downloadSignatureRes.data.sign_url;
          previewUrl = downloadSignUrl;
          console.log('获取到下载签名URL:', downloadSignUrl);
        }
      } catch (error) {
        console.warn('获取下载签名失败:', error);
      }

      // 构建兼容的文件对象
      const fileInfo = {
        id: Date.now() + Math.random().toString(36).substring(2, 7), // 生成唯一ID
        name: fileName,
        filename: fileName,
        url: fileUrl,
        object: result.url, // OSS 对象路径
        attachment_id: null, // 待获取
        type: fileType,
        size: fileSize,
        status: 'success',
        tempUrl: previewUrl || fileUrl, // 预览地址
        downloadSignUrl: downloadSignUrl, // 下载签名URL
      };

      // 调用 saveAttachmentAfterUpload 获取 attachment_id
      const updatedFileInfo = await saveAttachmentAfterUpload(fileInfo);
      console.log('获取到 attachment_id:', updatedFileInfo.attachment_id);

      processedFiles.push(updatedFileInfo);

    } catch (error) {
      console.error('处理文件失败:', error);
      // 即使处理失败，也添加基础信息
      processedFiles.push({
        id: Date.now() + Math.random().toString(36).substring(2, 7),
        name: result.fileName || 'unknown',
        filename: result.fileName || 'unknown',
        url: result.url || result.fullUrl,
        object: result.url,
        attachment_id: null,
        type: result.file?.type || 'application/octet-stream',
        size: result.file?.size || 0,
        status: 'error',
        tempUrl: result.url || result.fullUrl,
        downloadSignUrl: '',
      });
    }
  }
  return processedFiles;
};

// 删除已上传文件
const removeUploadedFile = async (file, index) => {
  try {
    console.log('删除前文件列表长度:', uploadedFiles.value.length);
    // 1. 先调用后端删除接口
    const res = await deleteAttachment({
      id: file.attachment_id || file.id,
    });
    if (res.code === 0) {
      // 2. 直接从本地文件列表中删除指定索引的文件
      if (index >= 0 && index < uploadedFiles.value.length) {
        uploadedFiles.value.splice(index, 1);
        console.log('✅ 本地文件删除成功，剩余文件数量:', uploadedFiles.value.length);
        uni.showToast({
          title: "文件删除成功",
          icon: "success",
          duration: 1500
        });
      } else {
        console.warn('⚠️ 删除索引无效:', index, '文件列表长度:', uploadedFiles.value.length);
      }
    } else {
      uni.showToast({
        title: res.message || "删除失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("❌ 删除文件异常:", error);
    uni.showToast({
      title: "删除失败",
      icon: "none",
    });
  }
}

/**
 * 检查AI是否正在回复中
 * @returns {boolean} 是否正在回复
 */
const isAIReplying = () => {
  // 检查全局loading状态
  if (loading.value) {
    return true;
  }

  // 检查全局打字状态
  if (isTyping.value) {
    return true;
  }

  // 检查是否有消息正在打字
  const hasTypingMessage = messages.value.some(message =>
    message.role === 'assistant' && message.isTyping
  );

  if (hasTypingMessage) {
    return true;
  }

  // 检查是否有工具调用正在执行
  const hasLoadingTools = messages.value.some(message =>
    message.role === 'assistant' &&
    message.toolCalls &&
    message.toolCalls.some(tool => !tool.isCompleted)
  );

  return hasLoadingTools;
};

/**
 * 删除消息
 * @param {number} index - 消息在数组中的索引
 */
 const deleteMessage = async (index) => {
  const message = messages.value[index];
  console.log('准备删除消息:', message);
  // 检查AI是否正在回复中，如果是则不允许删除
  if (isAIReplying()) {
    uni.showToast({
      title: 'AI正在回复中，请等待回复完成后再删除',
      icon: 'none',
      duration: 2000
    });
    return;
  }
  try {
    uni.showLoading({ title: '删除中...', mask: true });
    // 构建删除参数：历史消息用id，对话消息用task_id
    const deleteParams = message.id
      ? { id: message.id }  // 历史消息
      : { task_id: getCurrentTaskId() };  // 当前对话消息
    if (!message.id && !deleteParams.task_id) {
      throw new Error('无法获取删除标识，请重新开始对话');
    }
    console.log('删除参数:', deleteParams);
    // 调用删除接口
    const response = await deleteChatMessage(deleteParams);
    if (response.code !== 0) {
      throw new Error(response.message || '删除失败');
    }
    // 删除成功，根据后端返回的ids更新本地状态
    if (response.data && response.data.ids && Array.isArray(response.data.ids)) {
      // 根据返回的ids删除对应的消息
      const idsToDelete = response.data.ids;
      messages.value = messages.value.filter(msg => {
        // 如果消息有id且在删除列表中，则过滤掉
        return !(msg.id && idsToDelete.includes(msg.id));
      });
      console.log(`✅ 根据返回的ids删除了 ${idsToDelete.length} 条消息:`, idsToDelete);
    } else {
      // 兜底方案：如果后端没有返回ids，仍使用原来的索引删除方式
      messages.value.splice(index, 1);
      console.log('⚠️ 后端未返回ids，使用索引删除方式');
    }
    uni.hideLoading();
    uni.showToast({ title: '删除成功', icon: 'success' });
    console.log(`✅ ${message.id ? '历史' : '对话'}消息删除成功`);
  } catch (error) {
    console.error('❌ 删除消息失败:', error);
  }
}

/**
 * 打开反馈弹框
 * @param {string|number} messageId - 消息ID
 * @param {Object} pendingData - 待处理的反馈数据
 */
const openFeedbackModal = (messageId, pendingData = null) => {
  feedbackMessageId.value = messageId;
  pendingFeedbackData.value = pendingData;
  showFeedbackModal.value = true;
  console.log('🔔 打开反馈弹框:', { messageId, pendingData });
};

/**
 * 处理反馈弹框关闭
 */
const handleFeedbackClose = () => {
  showFeedbackModal.value = false;
  feedbackMessageId.value = null;
  pendingFeedbackData.value = null;
  console.log('🚫 反馈弹框已关闭');
};

/**
 * 处理反馈提交成功
 * @param {Object} result - 反馈提交结果
 */
const handleFeedbackSubmit = (result) => {
  console.log('✅ 反馈提交成功:', result);
  // 更新消息的点赞/点踩状态
  if (result.messageId && result.ratingData) {
    const messageIndex = messages.value.findIndex(msg => msg.id === result.messageId);
    if (messageIndex !== -1) {
      const message = messages.value[messageIndex];
      // 更新消息的评价状态
      message.is_like = result.ratingData.like_status || 0;
      message.is_dislike = result.ratingData.dislike_status || 0;
      message.dislike_reason = result.ratingData.dislike_reason || '';
      // 同步状态到对应的AiMessageActions组件
      const aiMessageActionsRef = aiMessageActionsRefs.value.get(messageIndex);
      if (aiMessageActionsRef && typeof aiMessageActionsRef.updateRatingStatus === 'function') {
        aiMessageActionsRef.updateRatingStatus(message.is_like, message.is_dislike);
        console.log('🔄 已同步状态到AiMessageActions组件');
      }
    }
  }
  // 关闭弹框
  handleFeedbackClose();
  // 显示成功提示
  uni.showToast({
    title: '💝 感谢您的反馈',
    icon: 'none',
    duration: 2000
  });
};

/**
 * 处理链接点击事件
 * @param {string} url - 链接地址
 * @param {string} title - 链接标题
 */
const handleLinkClick = (url, title) => {
  openLink(url, title);
};
/**
 * mp-html组件的链接点击事件处理
 * @param {Object} item - mp-html传递的链接信息
 */
const handeLinkTapChat = async (item) => {
  console.log('🔗 链接点击事件触发:', item);
  if (!item.href) {
    console.warn('⚠️ 链接地址为空');
    return;
  }
  // #ifdef APP-PLUS
  // 设置链接点击状态 用于app 返回跳转
  setLinkTapStatus(true);
  // #endif

  // 使用统一的链接处理工具
  await handleLink(item.href);
}
/**
 * APP端播放语音处理
 * @param {string} content - 要播放的文本内容
 * @param {number} state - 当前播放状态 (0: 未播放, 1: 播放中)
 */
const handerPlayAppVoice = async (content, state) => {
  try {
    if (!content || typeof content !== 'string') {
      console.error('无效的语音内容');
      return;
    }
    // 检查语音播放器是否已初始化
    if (!voicePlayerRef.value) {
      console.error('语音播放器未初始化');
      uni.showToast({
        title: '语音播放器未准备就绪',
        icon: 'none'
      });
      return;
    }
    // 如果当前正在播放相同内容，则停止播放
    const currentText = voicePlayerRef.value.getCurrentText();
    const isPlaying = voicePlayerRef.value.getPlayingState();
    // 同步播放状态=>显示动画效果
    if (isPlaying===false) {
      isPlayStatus.value = 1
    } else {
      isPlayStatus.value = 0
    }
    if (isPlaying && currentText === content) {
      console.log('停止当前播放');
      voicePlayerRef.value.stopVoice();
      return;
    }
    // 开始播放新内容
    console.log('开始播放语音:', content);
    await voicePlayerRef.value.playVoice(content);
  } catch (error) {
    console.error('语音播放处理失败:', error);
  }
}

// 检查是否应该显示消息操作按钮
const shouldShowMessageActions = (message, index) => {
  // 图片和文件类型消息不显示操作按钮
  if (message.content_type!=='str') {
    return false;
  }
  // 没有内容的消息不显示操作按钮
  if (!message.content || message.content.trim() === '') {
    return false;
  }
  // 如果有点击的消息，只显示被点击的消息的操作按钮
  if (clickedMessageIndex.value !== null) {
    return clickedMessageIndex.value === index;
  }
  // 没有点击消息时，最后一条AI消息显示操作按钮
  if (message.role === 'assistant' && index === messages.value.length - 1) {
    return true;
  }
  // 其他情况不显示操作按钮
  return false;
};

// 切换消息操作按钮的显示状态
const toggleMessageActions = (index) => {
  // 检查是否正在播放语音
  // #ifdef H5
  if (isVoiceInputActive.value) {
    stopVoiceRecognition()
  }
  if (voicePlayService && voicePlayService.isPlaying && voicePlayService.isPlaying.value) {
    console.log('H5端正在播放语音，忽略点击');
    return;
  }
  // #endif
  // #ifdef APP-PLUS
  if (isVoiceInputActive.value) {
    stopAppVoiceRecognition()
  }
  if (voicePlayerRef.value && voicePlayerRef.value.getPlayingState && voicePlayerRef.value.getPlayingState()) {
    console.log('APP端正在播放语音，忽略点击');
    return;
  }
  // #endif
  // 如果点击的是当前已选中的消息，则取消选中
  if (clickedMessageIndex.value === index) {
    clickedMessageIndex.value = null;
    console.log('取消选中消息');
    return;
  }
  // 否则选中当前点击的消息（包括最后一条消息）
  clickedMessageIndex.value = index;
};

// 处理技能选择
const handleSkillSelect = (skill) => {
  if (skill.value === '' && skill.label === '') {
    // 取消选择技能
    skillText.value = ''
    selectedSkillValue.value = ''
    skillExample.value = ''
    inputPlaceholder.value = '点击输入...'
    isSkillSearchActive.value = false
    // console.log('取消选择技能')
  } else {
    // 选择技能
    skillText.value = skill.word
    selectedSkillValue.value = skill.value || skill.id
    skillExample.value = skill.example || ''
    // 设置动态placeholder
    inputPlaceholder.value = skill.example || '点击输入...'
    isSkillSearchActive.value = true
    // console.log('选中技能:', skill)
    // console.log('设置placeholder:', inputPlaceholder.value)
  }
}

// 处理抽屉切换
const handleToggleDrawer = () => {
  if (drawerSidebarRef.value) {
    drawerSidebarRef.value.open();
  }
}

/**
 * 处理抽屉列表项点击
 * @param {Object} data
 */
 const handleDrawerItemClick = async (data) => {
  chatSSEClientRef.value.stopChat();
  currentSession.value.title = data.title || data.name || '销售助理 Ivy'
  currentSession.value.session_id = data.session_id || ''
  // 设置当前会话信息到本地存储，以便下次打开时恢复
  uni.setStorageSync('currentSession', currentSession.value);
  console.error('当前会话信息-----:',data);
  messages.value = [];
  // 重置之前的状态
  historyLoading.value = false; 
  hasMoreHistory.value = true;
  currentPage.value = 1;
  pageSize.value = 40;
  await fetchChatHistory(); // 重新获取聊天历史
  if (data.unread_num) {
    // 计算未读消息的正确位置
    const targetIndex = calculateUnreadMessageIndex(messages.value, data.unread_num);
    if (targetIndex !== -1) {
      // const targetMessage = messages.value[targetIndex];
      await chatScrollRef.value.scrollToTarget(`id-${targetIndex}-view`);
    } else {
      await scrollToBottom();
      console.warn('❌ 未找到目标未读消息，滚动到底部');
    }
  } else {
    await scrollToBottom();
  }
};

/**
 * 初始化检查技能状态
 * 检查本地存储中是否有之前选中的技能
 */
const initSkillStatus = () => {
  const savedSkillLabel = uni.getStorageSync('selectedSkillLabel')
  const savedSkillValue = uni.getStorageSync('selectedSkill')
  const savedSkillExample = uni.getStorageSync('selectedSkillExample')
  if (savedSkillLabel && savedSkillValue) {
    skillText.value = savedSkillLabel
    selectedSkillValue.value = savedSkillValue
    skillExample.value = savedSkillExample || ''
    inputPlaceholder.value = savedSkillExample || '点击输入...'
    isSkillSearchActive.value = true
    // console.log('恢复技能状态:', { 
    //   label: savedSkillLabel, 
    //   value: savedSkillValue, 
    //   example: savedSkillExample 
    // })
  }
}

/**
 * 初始化会话状态
 * 恢复之前选中的会话信息到抽屉组件
 */
 const initSessionStatus = () => {
  const savedSession = uni.getStorageSync('currentSession');
  if (savedSession && savedSession.session_id && drawerSidebarRef.value) {
    // 恢复会话对象
    currentSession.value = savedSession;
    // 使用nextTick确保组件已完全挂载
    nextTick(() => {
      if (drawerSidebarRef.value && drawerSidebarRef.value.setCurrentSession) {
        drawerSidebarRef.value.setCurrentSession(savedSession.session_id);
        // drawerSidebarRef.value.markSessionAsRead(currentSession.value.session_id);
        console.log('恢复会话状态:', savedSession);
      }
    });
  }
}
// APP 运行环境从前台切换到后台事件只有这样写才能监听到
// html5plus不支持箭头函数写法所以写成函数写法
function pauseListener() {
  // #ifdef APP-PLUS
  // plus.nativeUI.confirm('ssssssss', function(e){
  //   console.log("Close confirm: " + isLogged.value);
  // });
  // 重置分页参数
  historyLoading.value = false;
  hasMoreHistory.value = true;
  currentPage.value = 1;
  pageSize.value = 40;
  // 自执行的 async 函数
  (async function() {
    try {
      // 先恢复会话状态，确保 currentSession.session_id 正确
      initSessionStatus();
      // 清空消息列表
      messages.value = [];
      // 重新获取聊天历史
      await fetchChatHistory();
      await scrollToBottom();
      console.log('APP恢复完成，当前会话ID:', currentSession.value.session_id);
    } catch (err) {
      console.error("APP恢复过程中发生错误: ", err);
    }
  })();
  // #endif
}

// 监听消息列表变化
watch(messages, async (newMessages, oldMessages) => {
  // 如果消息数量增加，或者最后一条消息内容变化，滚动到底部
  const newLength = newMessages.length;
  const oldLength = oldMessages.length;
  if (newLength > 0 && oldLength > 0) {
    const newLastMsg = newMessages[newLength - 1];
    const oldLastMsg = oldMessages[oldLength - 1];
    // 如果最后一条消息发生变化，滚动到底部
    if (newLastMsg && oldLastMsg && 
        (newLastMsg.content !== oldLastMsg.content || 
         (newLastMsg.toolCalls?.length || 0) !== (oldLastMsg.toolCalls?.length || 0))) {
      await nextTick();
      await scrollToBottom();
    }
  }
}, { deep: true });

// 定义 cleanupKeyboardListener 变量
let cleanupKeyboardListener = null;

// 初始化
onMounted(async () => {
  try {
    // 初始化设备检测
    isPCDevice.value = isPC();
    // 添加全局点击监听器
    // #ifdef H5
    if (typeof document !== 'undefined') {
      document.addEventListener('click', handleGlobalClick);
    }
    // 设置PC端键盘监听器
    cleanupKeyboardListener = setupKeyboardListener();
    // #endif
    if (xeUpload.value) {
      setXeUploadRef(xeUpload.value);
    }
    // 如果没有历史消息，显示欢迎消息
    if (isLogged.value === false) {
      messages.value = [
        {
          role: "assistant",
          content: `您好，我是您的智能销售助手 Ivy 😄\n\n我能帮您：\n - 管理客户和商机，记录并跟进销售进展\n - 创建和提醒各类待办事项\n - 快速查询公司、联系人、文件和资料\n - 搜索企业、产品或客户的最新动态\n - 提供个性化的销售建议和跟进话术\n\n建议您先上传常用销售资料（比如公司介绍、产品介绍、价格清单、解决方案模板、合同模板、报价单模板等），方便后续随时调用。\n\n请问您怎么称呼？`,
        }
      ];
    } else {
      // 初始化技能状态
      initSkillStatus();
      // 初始化会话状态
      initSessionStatus();
      // #ifdef APP-PLUS
      // 监听APP前后台切换事件
      plus.globalEvent.addEventListener('pause', pauseListener);
      // #endif
    }
  } catch (error) {
    console.error("初始化失败:", error);
  }
});

// 在onUnmounted钩子中清理所有计时器和资源
onUnmounted(() => {
  // 清理打字机定时器
  if (typingTimer.value) {
    clearInterval(typingTimer.value);
    typingTimer.value = null;
  }
  // 清理滚动防抖动计时器
  if (scrollDebounceTimer.value) {
    clearTimeout(scrollDebounceTimer.value);
    scrollDebounceTimer.value = null;
  }
  // 清理全局点击监听器
  // #ifdef H5
  if (typeof document !== 'undefined') {
    document.removeEventListener('click', handleGlobalClick);
  }
  // 移除键盘事件监听器
  if (cleanupKeyboardListener) {
    cleanupKeyboardListener();
  }
  // #endif
  // 清理AiMessageActions组件引用
  aiMessageActionsRefs.value.clear();
  // 停止语音识别服务
  // #ifdef H5
  voiceRecognitionService.stopRecognition();
  // #endif
  
  // #ifdef APP-PLUS
  // 清理APP端语音识别事件监听器
  plus.speech.removeEventListener("start", onAppSpeechStart, false);
  plus.speech.removeEventListener("volumeChange", onAppVolumeChange, false);
  plus.speech.removeEventListener("recognizing", onAppRecognizing, false);
  plus.speech.removeEventListener("recognition", onAppRecognition, false);
  plus.speech.removeEventListener("end", onAppSpeechEnd, false);
  // 清理APP前后台切换事件监听器
  plus.globalEvent.removeEventListener('pause', pauseListener);
  // #endif
});


// 使用 Uni-app 官方生命周期
onLoad((options) => {
  // #ifdef APP-PLUS
  // 监听语音识别事件
  plus.speech.addEventListener("start", onAppSpeechStart, false);
  plus.speech.addEventListener("volumeChange", onAppVolumeChange, false);
  plus.speech.addEventListener("recognizing", onAppRecognizing, false);
  plus.speech.addEventListener("recognition", onAppRecognition, false);
  plus.speech.addEventListener("end", onAppSpeechEnd, false);
  // 监听APP前后台切换事件
  // plus.globalEvent.addEventListener("resume", onAppResume, false);
  console.error('已添加APP前后台切换事件监听器');
  // #endif
});
</script>

<style scoped lang="scss">
// 动画相关样式
@import '/styles/animations.scss';
// 媒体查询相关样式
@import '/styles/chat-layout.scss';
// 主样式文件
@import '/styles/ai-chat.scss';
</style>