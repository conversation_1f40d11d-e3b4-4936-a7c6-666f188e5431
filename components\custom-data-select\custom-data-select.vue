<template>
	<!-- 继承uni-data-select的模板，只修改显示文本的部分 -->
	<view class="uni-stat__select">
		<span v-if="label" class="uni-label-text hide-on-phone">{{label + '：'}}</span>
		<view class="uni-stat-box" :class="{'uni-stat__actived': current}">
			<view class="uni-select" :class="{'uni-select--disabled':disabled}">
				<view class="uni-select__input-box" @click="toggleSelector">
					<!-- 这里使用formatNotifyCycle替代textShow -->
					<view v-if="current" class="uni-select__input-text">{{formatNotifyCycle}}</view>
					<view v-else class="uni-select__input-text uni-select__input-placeholder">{{typePlaceholder}}</view>
					<view v-if="current && clear && !disabled" @click.stop="clearVal">
						<uni-icons type="clear" color="#c0c4cc" size="24" />
					</view>
					<view v-else>
						<uni-icons :type="showSelector? 'top' : 'bottom'" size="14" color="#999" />
					</view>
				</view>
				<view class="uni-select--mask" v-if="showSelector" @click="toggleSelector" />
				<view class="uni-select__selector" :style="getOffsetByPlacement" v-if="showSelector">
					<view :class="placement=='bottom'?'uni-popper__arrow_bottom':'uni-popper__arrow_top'"></view>
					<scroll-view scroll-y="true" class="uni-select__selector-scroll">
						<view class="uni-select__selector-empty" v-if="mixinDatacomResData.length === 0">
							<text>{{emptyTips}}</text>
						</view>
						<view v-else class="uni-select__selector-item" v-for="(item,index) in mixinDatacomResData" :key="index"
							@click="change(item)">
							<text :class="{'uni-select__selector__disabled': item.disable}">{{formatItemName(item)}}</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getNotifyCycle } from '@/utils/formatTime.js'

	/**
	 * DataSelect 数据选择器
	 * @description 支持单列、和多列选择
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=3796
	 * @property {String} label 左侧标题
	 * @property {String} placeholder 输入框占位符
	 * @property {Array} localdata 本地数据 ，格式 [{text:'',value:''}]
	 * @property {Boolean} clear 是否显示清除按钮
	 * @property {Boolean} emptyText 没有数据时显示的文字 ，本地数据无效
	 * @property {String} emptyTips 选项列表为空时显示的文字
	 * @property {String} format 格式化输出 用法 format="{value} 元"
	 * @property {Boolean} disabled 是否禁用
	 * @property {String} cacheKey 本地缓存数据的key
	 * @property {String} placement 弹出层位置
	 * @property {String} notifyTime 提醒时间，用于格式化显示
	 * @value top 上
	 * @value bottom 下
	 * @event {Function} change  选中发生变化触发
	 */
	export default {
		name: 'CustomDataSelect',
		mixins: [{
			props: {
				localdata: {
					type: Array,
					default() {
						return []
					}
				},
				value: {
					type: [String, Number],
					default: ''
				},
				modelValue: {
					type: [String, Number],
					default: ''
				},
				label: {
					type: String,
					default: ''
				},
				placeholder: {
					type: String,
					default: '请选择'
				},
				emptyTips: {
					type: String,
					default: '无选项'
				},
				clear: {
					type: Boolean,
					default: true
				},
				defItem: {
					type: Number,
					default: 0
				},
				disabled: {
					type: Boolean,
					default: false
				},
				format: {
					type: String,
					default: ''
				},
				cacheKey: {
					type: String,
					default: ''
				},
				placement: {
					type: String,
					default: 'bottom'
				},
				notifyTime: {
					type: String,
					default: ''
				}
			},
			data() {
				return {
					showSelector: false,
					current: '',
					mixinDatacomResData: []
				}
			},
			computed: {
				typePlaceholder() {
					const text = this.placeholder
					return text
				},
				valueCom() {
					return this.modelValue !== '' ? this.modelValue : this.value
				},
				textShow() {
					return this.current
				},
				getOffsetByPlacement() {
					switch (this.placement) {
						case 'top':
							return "bottom:calc(100% + 12px);";
						case 'bottom':
							return "top:calc(100% + 12px);";
					}
				},
				// 格式化提醒时间显示
				formatNotifyCycle() {
					// 如果有当前选中值，并且有提醒时间，则使用getNotifyCycle格式化显示
					if (this.current && this.valueCom !== '' && this.notifyTime) {
						return getNotifyCycle(this.valueCom, this.notifyTime)
					}
					// 否则使用原始文本
					return this.textShow
				},
			},
			watch: {
				localdata: {
					immediate: true,
					handler(val) {
						if (Array.isArray(val)) {
							this.mixinDatacomResData = val
							this.initDefVal()
						}
					}
				},
				valueCom(val) {
					this.initDefVal()
				}
			},
			created() {
				this.form = this.getForm('uniForms')
				this.formItem = this.getForm('uniFormsItem')
				if (this.formItem) {
					if (this.formItem.name) {
						this.rename = this.formItem.name
						this.form.inputChildrens.push(this)
					}
				}
			},
			methods: {
				getForm(name = 'uniForms') {
					let parent = this.$parent;
					let parentName = parent.$options.name;
					while (parentName !== name) {
						parent = parent.$parent;
						if (!parent) return false;
						parentName = parent.$options.name;
					}
					return parent;
				},
				initDefVal() {
					let defValue = ''
					if ((this.valueCom || this.valueCom === 0) && !this.isDisabled(this.valueCom)) {
						defValue = this.valueCom
					} else {
						let strogeValue
						if (this.cacheKey) {
							strogeValue = this.getCache()
						}
						if (strogeValue || strogeValue === 0) {
							defValue = strogeValue
						} else {
							let defItem = ''
							if (this.defItem > 0 && this.defItem <= this.mixinDatacomResData.length) {
								defItem = this.mixinDatacomResData[this.defItem - 1].value
							}
							defValue = defItem
						}
						if (defValue || defValue === 0) {
							this.emit(defValue)
						}
					}
					const def = this.mixinDatacomResData.find(item => item.value === defValue)
					this.current = def ? this.formatItemName(def) : ''
				},

				/**
				 * @param {[String, Number]} value
				 * 判断用户给的 value 是否同时为禁用状态
				 */
				isDisabled(value) {
					let isDisabled = false;

					this.mixinDatacomResData.forEach(item => {
						if (item.value === value) {
							isDisabled = item.disable
						}
					})

					return isDisabled;
				},

				clearVal() {
					this.emit('')
					if (this.cacheKey) {
						this.removeCache()
					}
				},
				change(item) {
					if (!item.disable) {
						this.showSelector = false
						this.current = this.formatItemName(item)
						this.emit(item.value)
					}
				},
				emit(val) {
					this.$emit('input', val)
					this.$emit('update:modelValue', val)
					this.$emit('change', val)
					if (this.cacheKey) {
						this.setCache(val);
					}
				},
				toggleSelector() {
					if (this.disabled) {
						return
					}

					this.showSelector = !this.showSelector
				},
				formatItemName(item) {
					let {
						text,
						value,
						channel_code
					} = item
					channel_code = channel_code ? `(${channel_code})` : ''

					if (this.format) {
						// 格式化输出
						let str = "";
						str = this.format;
						for (let key in item) {
							str = str.replace(new RegExp(`{${key}}`, "g"), item[key]);
						}
						return str;
					} else {
						return this.cacheKey && this.cacheKey.indexOf('app-list') > 0 ?
							`${text}(${value})` :
							(
								text ?
								text :
								`未命名${channel_code}`
							)
					}
				},
				// 获取当前加载的数据
				getLoadData() {
					return this.mixinDatacomResData;
				},
				// 获取当前缓存key
				getCurrentCacheKey() {
					return this.cacheKey;
				},
				// 获取缓存
				getCache(name = this.getCurrentCacheKey()) {
					let cacheData = uni.getStorageSync(this.cacheKey) || {};
					return cacheData[name];
				},
				// 设置缓存
				setCache(value, name = this.getCurrentCacheKey()) {
					let cacheData = uni.getStorageSync(this.cacheKey) || {};
					cacheData[name] = value;
					uni.setStorageSync(this.cacheKey, cacheData);
				},
				// 删除缓存
				removeCache(name = this.getCurrentCacheKey()) {
					let cacheData = uni.getStorageSync(this.cacheKey) || {};
					delete cacheData[name];
					uni.setStorageSync(this.cacheKey, cacheData);
				},
			}
		}]
	}
</script>

<style lang="scss">
	$uni-base-color: #6a6a6a !default;
	$uni-main-color: #333 !default;
	$uni-secondary-color: #909399 !default;
	$uni-border-3: #e5e5e5;

	/* #ifndef APP-NVUE */
	@media screen and (max-width: 500px) {
		.hide-on-phone {
			display: none;
		}
	}

	/* #endif */
	.uni-stat__select {
		display: flex;
		align-items: center;
		// padding: 15px;
		/* #ifdef H5 */
		cursor: pointer;
		/* #endif */
		width: 100%;
		flex: 1;
		box-sizing: border-box;
	}

	.uni-stat-box {
		width: 100%;
		flex: 1;
	}

	.uni-stat__actived {
		width: 100%;
		flex: 1;
		// outline: 1px solid #2979ff;
	}

	.uni-label-text {
		font-size: 14px;
		font-weight: bold;
		color: $uni-base-color;
		margin: auto 0;
		margin-right: 5px;
	}

	.uni-select {
		font-size: 14px;
		border: 1px solid $uni-border-3;
		box-sizing: border-box;
		border-radius: 4px;
		padding: 0 5px;
		padding-left: 10px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		user-select: none;
		/* #endif */
		flex-direction: row;
		align-items: center;
		border-bottom: solid 1px $uni-border-3;
		width: 100%;
		flex: 1;
		height: 35px;

		&--disabled {
			background-color: #f5f7fa;
			cursor: not-allowed;
		}
	}

	.uni-select__label {
		font-size: 16px;
		// line-height: 22px;
		height: 35px;
		padding-right: 10px;
		color: $uni-secondary-color;
	}

	.uni-select__input-box {
		height: 35px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		flex-direction: row;
		align-items: center;
	}

	.uni-select__input {
		flex: 1;
		font-size: 14px;
		height: 22px;
		line-height: 22px;
	}

	.uni-select__input-plac {
		font-size: 14px;
		color: $uni-secondary-color;
	}

	.uni-select__selector {
		/* #ifndef APP-NVUE */
		box-sizing: border-box;
		/* #endif */
		position: absolute;
		left: 0;
		width: 100%;
		background-color: #FFFFFF;
		border: 1px solid #EBEEF5;
		border-radius: 6px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		z-index: 3;
		padding: 4px 0;
	}

	.uni-select__selector-scroll {
		/* #ifndef APP-NVUE */
		max-height: 200px;
		box-sizing: border-box;
		/* #endif */
	}

	/* #ifdef H5 */
	@media (min-width: 768px) {
		.uni-select__selector-scroll {
			max-height: 600px;
		}
	}

	/* #endif */

	.uni-select__selector-empty,
	.uni-select__selector-item {
		/* #ifndef APP-NVUE */
		display: flex;
		cursor: pointer;
		/* #endif */
		line-height: 35px;
		font-size: 14px;
		text-align: center;
		/* border-bottom: solid 1px $uni-border-3; */
		padding: 0px 10px;
	}

	.uni-select__selector-item:hover {
		background-color: #f9f9f9;
	}

	.uni-select__selector-empty:last-child,
	.uni-select__selector-item:last-child {
		/* #ifndef APP-NVUE */
		border-bottom: none;
		/* #endif */
	}

	.uni-select__selector__disabled {
		opacity: 0.4;
		cursor: default;
	}

	/* picker 弹出层通用的指示小三角 */
	.uni-popper__arrow_bottom,
	.uni-popper__arrow_bottom::after,
	.uni-popper__arrow_top,
	.uni-popper__arrow_top::after{
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	border-color: transparent;
	border-style: solid;
	border-width: 6px;
	}

	.uni-popper__arrow_bottom {
		filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
		top: -6px;
		left: 10%;
		margin-right: 3px;
		border-top-width: 0;
		border-bottom-color: #EBEEF5;
	}

	.uni-popper__arrow_bottom::after {
		content: " ";
		top: 1px;
		margin-left: -6px;
		border-top-width: 0;
		border-bottom-color: #fff;
	}

	.uni-popper__arrow_top {
		filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
		bottom: -6px;
		left: 10%;
		margin-right: 3px;
		border-bottom-width: 0;
		border-top-color: #EBEEF5;
	}

	.uni-popper__arrow_top::after {
		content: " ";
		bottom: 1px;
		margin-left: -6px;
		border-bottom-width: 0;
		border-top-color: #fff;
	}


	.uni-select__input-text {
		// width: 280px;
		width: 100%;
		color: $uni-main-color;
		white-space: nowrap;
		text-overflow: ellipsis;
		-o-text-overflow: ellipsis;
		overflow: hidden;
	}

	.uni-select__input-placeholder {
		color: $uni-base-color;
		font-size: 12px;
	}

	.uni-select--mask {
		position: fixed;
		top: 0;
		bottom: 0;
		right: 0;
		left: 0;
		z-index: 2;
	}
</style>