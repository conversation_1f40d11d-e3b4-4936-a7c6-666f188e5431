<template>
  <view v-if="files.length > 0" class="uploaded-files-container">
  <!-- 已上传文件列表 -->
    <scroll-view scroll-x="true" class="file-list-scroll" show-scrollbar="false">
      <view class="file-list">
        <view v-for="(file, index) in files" :key="file.id">
          <!-- 图片类型文件 -->
          <view v-if="isImageFile(file.filename)" class="img-item">
            <image :src="file.tempUrl || '/static/file/unknown.svg'" mode="aspectFill" class="file-image" @click="handlePreview(file)"></image>
            <!-- 删除按钮 -->
            <view class="file-delete" @click="handleRemove(file, index)">
              <text class="delete-icon">×</text>
            </view>
          </view>
          <!-- 文件类型文件 -->
          <view v-else class="file-item">
            <view class="file-thumbnail" @click="handlePreview(file)">
              <image :src="getFileIconPath(file.filename)" mode="aspectFit" class="file-type-icon">
              </image>
               <view class="file-name">{{ getShortFileName(file.name) }}</view>
            </view>
            <!-- 删除按钮 -->
            <view class="file-delete" @click="handleRemove(file, index)">
              <text class="delete-icon">×</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { isImageFile, getFileIconPath, getShortFileName,openWebOfficePreview } from '@/utils/fileUtils.js';
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具

// 定义组件属性
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['preview', 'remove']);

/**
 * 预览已上传的文件
 * @param {Object} file 文件对象
 */
 const handlePreview = async (file) => {
  if (!file) return;
  file.id=file.attachment_id
  try {
    // 调用预览服务，传入不同平台的预览回调
    await previewFile(file, {
      onWebOfficePreview: openWebOfficePreview,// H5端WebOffice预览
    });
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};

// 处理文件删除
const handleRemove = (file, index) => {
  emit('remove', file, index);
};
</script>

<style scoped lang="scss">
.uploaded-files-container {
  margin-bottom: 2px;
  overflow: visible;
  padding: 5px 15px;

  .file-list-scroll {
    width: 100%;
    white-space: nowrap;
  }

  .file-list {
    display: inline-flex;
    align-items: center;
    padding: 4px 0;
    max-height: 40px;
    .img-item {
      position: relative;
      margin-right: 10px;
      .file-image {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-size: 100% 100%;
      }
    }
    .file-item {
      position: relative;
      display: flex;
      align-items: center;
      margin-right: 10px;
      max-width: 160px;
      background-color: #ffff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      border-radius: 10px;
      max-height: 40px;
      min-height: 40px;
      padding: 0 10px;
      cursor: pointer;

      .file-thumbnail {
        height: 20px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;


        .file-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .file-type-icon {
          width: 28px;
          height: 28px;
          object-fit: contain;
        }
      }

      .file-name {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        color: #3E4551;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .file-delete {
      position: absolute;
      top: -2px;
      right: -4px;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      z-index: 2;
      .delete-icon {
        font-size: 12px;
        line-height: 1;
      }
    }
  }
}
</style>