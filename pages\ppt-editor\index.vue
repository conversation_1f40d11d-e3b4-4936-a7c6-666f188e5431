<template>
  <view class="docmee-ui-container">
    <!-- PPT编辑器容器 - SDK会自动挂载iframe到这里 -->
    <!-- #ifdef H5 -->
    <view ref="pptContainer" class="ppt-container"></view>
    <!-- #endif -->
    <!-- APP端使用web-view组件 :webview-styles="webviewStyles"  -->
    <!-- #ifdef APP-PLUS -->
    <web-view
      :src="appWebViewSrc"
      @load="onAppLoad"
      @error="onAppError"
      class="app-webview"
    ></web-view>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { getDocmeeToken } from "@/http/docmee-ppt.js";

// 页面参数
const pptId = ref(""); // 默认PPT ID
const token = ref(null);
// 响应式数据
const pptContainer = ref(null);
let docmeeUI = null; // SDK实例

/**
 * 计算属性：APP端WebView源地址
 */
const appWebViewSrc = computed(() => {
  // #ifdef APP-PLUS
  try {
    // 手动构建查询参数，兼容不支持URLSearchParams的环境
    const params = [
      "token=" + encodeURIComponent(token.value),
      "pptId=" + encodeURIComponent(pptId.value),
      "timestamp=" + Date.now(), // 避免缓存
    ];
    const url = `/static/ppt-editor.html?${params.join("&")}`;
    console.log("🌐 APP端WebView URL:", url);
    return url;
  } catch (error) {
    console.error("❌ APP端URL构建失败:", error);
    return "/static/ppt-editor.html";
  }
  // #endif
  // #ifndef APP-PLUS
  return "";
  // #endif
});


/**
 * APP端WebView加载完成
 */
const onAppLoad = (event) => {
  // #ifdef APP-PLUS
  console.log('📱 APP端WebView加载完成:', event.detail)
  // #endif
}

/**
 * APP端WebView错误处理
 */
const onAppError = (event) => {
  // #ifdef APP-PLUS
  console.error('❌ APP端WebView加载错误:', event.detail)
  // #endif
}

/**
 * 检查SDK是否加载
 */
const checkSDK = () => {
  // #ifdef H5
  return typeof window !== "undefined" && window.DocmeeUI;
  // #endif
  // #ifndef H5
  return false; // APP端通过webview处理
  // #endif
};

/**
 * 初始化PPT编辑器
 */
const initPPTEditor = () => {
  // #ifdef H5
  if (!checkSDK()) {
    console.error("PPT编辑器SDK未加载");
    return;
  }
  try {
    // 确保获取到真正的DOM元素
    let containerElement = null;
    // 通过ref获取
    if (pptContainer.value) {
      containerElement = pptContainer.value;
      // 如果是vue组件实例，获取其$el
      if (containerElement.$el) {
        containerElement = containerElement.$el;
      }
    }
    console.log("✅ 成功获取容器元素:", containerElement);
    const config = {
      pptId: pptId.value,
      token: token.value,
      lang:'zh',
      mode:'light',
      page:'editor',
      container: containerElement, // 使用真实的DOM元素
    };
    console.log("🚀 初始化PPT编辑器", config);
    // 创建DocmeeUI实例
    docmeeUI = new window.DocmeeUI(config);
  } catch (err) {
    console.error("❌ 初始化PPT编辑器失败:", err);
  }
  // #endif
};

/**
 * 加载SDK脚本
 */
const loadSDK = () => {
  // #ifdef H5
  return new Promise((resolve, reject) => {
    // 如果SDK已存在，直接返回
    if (checkSDK()) {
      resolve();
      return;
    }
    // 动态加载SDK脚本
    const script = document.createElement("script");
    script.src = "/static/js/docmee-ui-sdk-iframe.min.js"; // 从static目录加载
    script.onload = () => {
      console.log("✅ DocmeeUI SDK加载成功");
      resolve();
    };
    script.onerror = () => {
      console.error("❌ DocmeeUI SDK加载失败");
      reject(new Error("SDK加载失败"));
    };
    document.head.appendChild(script);
  });
  // #endif

  // #ifndef H5
  return Promise.reject(new Error("当前平台不支持"));
  // #endif
};

/**
 * 从后端获取token（简化版本）
 */
const fetchTokenFromServer = async () => {
  try {
    let params = {
      ppt_id: pptId.value,
    };
    console.log("🔍 当前pptId.value:", pptId.value);
    const response = await getDocmeeToken(params);
    if (response.code === 0 && response.data?.token) {
      token.value = response.data?.token;
      if (checkSDK()) {
        initPPTEditor();
      }
    } else {
      throw new Error(response.message || "Token获取失败");
    }
  } catch (error) {
    console.error("❌ 获取token失败:", error);
  }
};

/**
 * 页面加载时的参数处理
 */
onLoad((options) => {
  console.log("📄 PPT编辑器页面加载，参数:", options);
  // 处理URL参数
  if (options.ppt_id || options.pptId) {
    pptId.value = options.ppt_id || options.pptId;
    console.log("📋 从URL获取到PPT ID:", pptId.value);
  }
});

onMounted(async () => {
  try {
    // #ifdef H5
    // H5端：直接加载SDK并初始化
    loadSDK();
    // #endif
    await fetchTokenFromServer();
  } catch (err) {
    console.error("❌ 组件初始化失败:", err);
  }
});

onUnmounted(() => {
  // 清理资源
  if (docmeeUI) {
    docmeeUI = null;
    console.log("🗑️ PPT编辑器实例已清理");
  }
});
</script>

<style scoped lang="scss">
.docmee-ui-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.ppt-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.app-webview {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  display: block;
}
</style>