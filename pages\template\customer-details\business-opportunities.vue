<template>
  <!--关联商机模块 -->
  <view class="business-details-container">
    <!-- 当前 -->
    <view class="section-box s-p-b">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">当前</view>
      </view>
      <uni-icons type="plusempty" size="20" color="#4D5BDE" @click="handeAddActivityClick"></uni-icons>
    </view>
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
        <!-- 添加条件判断，有数据时显示列表 -->
        <template v-if="getCompletedTasks && getCompletedTasks.length > 0">
          <block v-for="(item, index) in getCompletedTasks" :key="index">
            <view class="business-item">
              <image class="public-icon-img" src="/static/global/briefcase.svg"></image>
              <view class="content-wrapper" @click.stop="handeToEdit(item, index)">
                <view class="business-title" v-if="item.title">{{item.title}}</view>
                <view class="title-row">
                  <view class="todo-row">
                    <view class="business-time" v-if="item.todo?.due_date">{{
                      filterDateTime(item.todo?.due_date)
                    }}</view>
                    <view class="todo-text">{{ item.todo?.title || "-" }}</view>
                  </view>
                  <view>{{ getStatusText(item.status) || "-" }}</view>
                </view>
              </view>
            </view>
          </block>
        </template>
        <!-- 添加缺省状态 -->
        <template v-else>
          <view class="empty-placeholder" @click="handeAddActivityClick">
            <text class="empty-text">暂无关联商机，点击添加</text>
          </view>
        </template>
      </view>
    </transition>
    <!-- 已关闭 -->
    <view class="section-box" v-if="hasClosedTasks">
      <view class="section-left" @click="toggleExpand('closed')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.closed
              ? 'rotate(0deg)'
              : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">已关闭</view>
      </view>
    </view>
    <transition name="slide-fade" >
      <block v-if="hasClosedTasks">
        <view class="todo-card" v-show="expandStates.closed">
          <block v-for="(item, index) in closeCompleteTasks" :key="index">
            <view class="business-item">
              <image class="public-icon-img" src="/static/global/briefcase.svg"></image>
              <view class="content-wrapper" @click.stop="handeToEdit(item, index)">
                <view class="business-title" v-if="item.title">{{item.title}}</view>
                <view class="title-row">
                  <view class="todo-row">
                    <view class="business-time" v-if="item.todo?.due_date">{{
                      filterDateTime(item.todo?.due_date)
                    }}</view>
                    <view class="todo-text">{{ item.todo?.title || "-" }}</view>
                  </view>
                  <view>{{ getStatusText(item.status) || "-" }}</view>
                </view>
              </view>
            </view>
          </block>
        </view>
      </block>
    </transition>
  </view>
    <!-- 新建商机表单弹窗 -->
    <BusinessFormActionSheet
      v-if="showActiveForm"
      v-model:show="showActiveForm"
      @submit="handleTodoSubmit"
      :initialData="initialData"
   />
</template>
        
<script setup>
import { computed, ref, onMounted, watch } from "vue";
import { createOpportunity,removeOpportunity } from "@/http/business.js";
import { fetchUserConfig } from "@/http/user.js"; // 引入配置接口
import BusinessFormActionSheet from "../../tabBar/business/BusinessFormActionSheet.vue";
import { filterDateTime } from "@/utils/formatTime.js";

const emit = defineEmits(["refresh"]);
const props = defineProps({
  // 初始表单数据
  businessList: {
    type: Array,
    default: [],
  },
  businessId: {
    type: String,
  },
});
// 修改状态管理
const expandStates = ref({
  current: true,
  closed: true,
});
// 修改状态管理
const initialData = ref({
  customer_id: '',
});
// 是否展示新建客户下拉
const showActiveForm = ref(false)
// 商机阶段选项 - 改为动态获取
const stages = ref([]);
// 获取当前任务列表
const getCompletedTasks = ref([]);
// 获取关闭的任务列表
const closeCompleteTasks = ref([]);

// 添加计算属性，判断是否有已关闭的任务
const hasClosedTasks = computed(() => {
  return closeCompleteTasks.value && closeCompleteTasks.value.length > 0;
});
// 获取状态文本的函数
const getStatusText = (status) => {
  const stage = stages.value.find(stage => stage.value === status);
  return stage ? stage.text : "未知状态";
};
// 弹出客户新建文件
const handeAddActivityClick = () => {
  showActiveForm.value = true
};
// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
  expandStates.value[type] = !expandStates.value[type];
};
// 点击项时的处理 进入商机详情
const handeToEdit = (item) => {
  uni.navigateTo({
    url: `/pages/template/business-detail/index?business_id=${item.bo_id}&title=${item.title}`,
  });
};
// 分类任务
const splitTasksByStatus = async () => {
  try {
    // 清空之前的任务数据
    closeCompleteTasks.value = [];
    getCompletedTasks.value = [];
    initialData.value.customer_id = Number(props.businessId)
    // todoObj.value.customer_id =  props.businessList[0]?.customer_id
    // 遍历后端返回的任务数组，根据 status 进行分类
    props.businessList.forEach((task) => {
      // 根据 status 值分类任务
      if (task.status === 6 || task.status === 7) {
        // 如果是赢单关闭或者丢单关闭，将任务放入 closeCompleteTasks
        closeCompleteTasks.value.push(task);
      } else {
        // 其他任务放入 getCompletedTasks
        getCompletedTasks.value.push(task);
      }
    });
    // 输出已分类的任务
    // console.log("当前的任务:关联商机", getCompletedTasks.value);
    // console.log("关闭的任务:关联商机", closeCompleteTasks.value);
  } catch (err) {
    console.error("请求失败:", err);
  }
};
// 处理表单提交
const handleTodoSubmit = async (formData) => {
  try {
    formData.customer_id = props.businessId
    const res = await createOpportunity({
      ...formData,
    })
    if (res.code === 0) {
      uni.showToast({
        title: "创建成功",
        icon: "success",
      });
      emit("refresh");
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
}
// 获取商机阶段配置
const getBusinessStages = async () => {
  try {
    const res = await fetchUserConfig();
    console.log("获取商机阶段配置:", res);
    if (res.code === 0 && res.data && res.data.business_opportunities) {
      const statusMap = res.data.business_opportunities.status_map || {};
      // 将接口返回的状态映射转换为下拉选项格式
      stages.value = Object.entries(statusMap).map(([value, text]) => ({
        value: parseInt(value), // 确保值是数字类型
        text
      }));
    }
  } catch (err) {
    console.error("获取商机阶段配置失败:", err);
  }
};
// 添加数据监听
watch(
  () => props.businessList,
  (newVal) => {
    if (newVal && newVal.length >= 0) {
      splitTasksByStatus();
      // 获取商机阶段配置
      getBusinessStages();
    }
  },
  { deep: true, immediate: true }
);

// onMounted(() => {
//   setTimeout(() => {
//     splitTasksByStatus();
//   }, 600);
// });

</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.business-details-container {
  box-sizing: border-box;
  margin: 0 18px;
  .section-box {
    display: flex;
    align-items: center;
    margin: 15px 0px 15px 0px;
    .section-left {
      display: flex;
      align-items: center;
      .icon-down {
        width: 25px;
        height: 25px;
      }
    }
    .section-title {
      color: var(---, #787d86);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
  .s-p-b {
    justify-content: space-between;
  }
  .todo-card {
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background: #fff;
    .empty-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      text-align: center;
      cursor: pointer;
    }

    .empty-text {
      font-size: 14px;
      color: #999;
    }
    .business-item {
      display: flex;
      align-items: center;
      margin: 10px 12px 5px 12px;
      &:last-child {
        .content-wrapper{
          border-bottom: none;
        }
      }
      .content-wrapper {
        flex: 1;
        margin-left: 6px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 2px;
        overflow: hidden;
      }
      .business-title {
        color: var(---, #000);
        text-overflow: ellipsis;
        font-size: 14px;
        white-space: nowrap; /* 确保文本不换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        max-width: 100%; /* 确保宽度不超过父容器 */
      }
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 4px;
        color: var(---, #adb1ba);
        font-size: 12px;
        .todo-row {
          display: flex;
          align-items: center;
          max-width: 62vw;
          max-height: 20px;
          .business-time {
            // max-width: 150px;
            min-width: 95px;
            margin-right: 4px;
            max-width: 100%;
          }
          .todo-text {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            max-width: 100%;
          }
        }
      }
    }
  }
}
</style>