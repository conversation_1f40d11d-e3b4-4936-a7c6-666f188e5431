// 引用网络请求中间件
import request from '@/utils/request';

// 创建任务
export function createTask(data, customHeaders = {}) {
    return request({
        url: '/api/task/create',
        method: 'POST',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 会话列表
export function getSessionList(data = {}, customHeaders = {}) {
    return request({
        url: '/api/session/list',
        method: 'GET',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 设为已读
export function setSessionRead(data, customHeaders = {}) {
    return request({
        url: '/api/session/setRead',
        method: 'POST',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 聊天记录列表
export function getChatHistoryList (data, customHeaders = {}) {
    return request({
        url: '/api/chat/list',
        method: 'GET',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 获取语音识别token
export function fetchSpeechRecognitionToken(data, customHeaders = {}) {
    return request({
        url: '/api/asr/token',
        method: 'GET',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 清空上下文
export function clearContext(data = {}, customHeaders = {}) {
    return request({
        url: '/api/task/contextClear',
        method: 'POST',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 停止任务
export function stopTask(data, customHeaders = {}) {
    return request({
        url: '/api/task/stop',
        method: 'POST',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 获取聊天指导技能
export function getChatGuidance(data = {}, customHeaders = {}) {
    return request({
        url: '/api/chat/guidance',
        method: 'GET',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 删除聊天消息
export function deleteChatMessage(data, customHeaders = {}) {
    return request({
        url: '/api/chat/del',
        method: 'POST',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}

// 保存聊天消息反馈（点赞/点踩）
export function saveChatFeedback(data, customHeaders = {}) {
    return request({
        url: '/api/chat/save',
        method: 'POST',
        data,
        headers: customHeaders // 透传自定义请求头
    });
}
// 参数：{
//     "id": 32,
//     "content": "dolor minim nulla sunt",
//     "content_type": "consectetur culpa",
//     "attachment_id": 6,
//     "task_id": 31,
//     "is_robot": 46,
//     "is_like": 91,
//     "is_dislike": 26,
//     "dislike_reason": "dolore velit elit"
// }
