var __renderjsModules={};
__renderjsModules["5a1e922e"]=(()=>{var u=Object.defineProperty;var c=Object.getOwnPropertyDescriptor;var f=Object.getOwnPropertyNames;var p=Object.prototype.hasOwnProperty;var m=(t,e)=>{for(var o in e)u(t,o,{get:e[o],enumerable:!0})},g=(t,e,o,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of f(e))!p.call(t,i)&&i!==o&&u(t,i,{get:()=>e[i],enumerable:!(a=c(e,i))||a.enumerable});return t};var v=t=>g(u({},"__esModule",{value:!0}),t);var S={};m(S,{default:()=>D});var l={showWatch(t,e,o,a,i){var s=i.state,r=o.$el||o.$vm&&o.$vm.$el;if(r){if(this.getDom(a,o,i),t&&t!=="none"){this.openState(t,a,o,i);return}s.left&&this.openState("none",a,o,i),this.resetTouchStatus(a,i)}},touchstart(t,e,o){let a=t.instance,i=a.getDataset().disabled,s=o.state;this.getDom(a,e,o),i=this.getDisabledType(i),!i&&(a.requestAnimationFrame(function(){a.removeClass("ani"),e.callMethod("closeSwipe")}),s.x=s.left||0,this.stopTouchStart(t,e,o))},touchmove(t,e,o){let a=t.instance;if(!a)return;let i=a.getDataset().disabled,s=o.state;if(i=this.getDisabledType(i),i||(this.stopTouchMove(t,o),s.direction!=="horizontal"))return;t.preventDefault&&t.preventDefault();let r=s.x+s.deltaX;this.move(r,a,e,o)},touchend(t,e,o){let a=t.instance,i=a.getDataset().disabled,s=o.state;i=this.getDisabledType(i),!i&&this.moveDirection(s.left,a,e,o)},move(t,e,o,a){t=t||0;let i=a.state,s=i.leftWidth,r=i.rightWidth;i.left=this.range(t,-r,s),e.requestAnimationFrame(function(){e.setStyle({transform:"translateX("+i.left+"px)","-webkit-transform":"translateX("+i.left+"px)"})})},getDom(t,e,o){var a=o.state,i=e.$el||e.$vm&&e.$vm.$el,s=i.querySelector(".button-group--left"),r=i.querySelector(".button-group--right");a.leftWidth=s.offsetWidth||0,a.rightWidth=r.offsetWidth||0,a.threshold=t.getDataset().threshold},getDisabledType(t){return(typeof t=="string"?JSON.parse(t):t)||!1},range(t,e,o){return Math.min(Math.max(t,e),o)},moveDirection(t,e,o,a){var i=a.state,s=i.threshold,r=i.position,h=i.isopen||"none",n=i.leftWidth,d=i.rightWidth;if(i.deltaX===0){this.openState("none",e,o,a);return}h==="none"&&d>0&&-t>s||h!=="none"&&d>0&&d+t<s?this.openState("right",e,o,a):h==="none"&&n>0&&t>s||h!=="none"&&n>0&&n-t<s?this.openState("left",e,o,a):this.openState("none",e,o,a)},openState(t,e,o,a){let i=a.state,s=i.leftWidth,r=i.rightWidth,h="";switch(i.isopen=i.isopen?i.isopen:"none",t){case"left":h=s;break;case"right":h=-r;break;default:h=0}i.isopen!==t&&(i.throttle=!0,o.callMethod("change",{open:t})),i.isopen=t,e.requestAnimationFrame(()=>{e.addClass("ani"),this.move(h,e,o,a)})},getDirection(t,e){return t>e&&t>10?"horizontal":e>t&&e>10?"vertical":""},resetTouchStatus(t,e){let o=e.state;o.direction="",o.deltaX=0,o.deltaY=0,o.offsetX=0,o.offsetY=0},stopTouchStart(t,e,o){let a=t.instance,i=o.state;this.resetTouchStatus(a,o);var s=t.touches[0];i.startX=s.clientX,i.startY=s.clientY},stopTouchMove(t,e){let o=t.instance,a=e.state,i=t.touches[0];a.deltaX=i.clientX-a.startX,a.deltaY=i.clientY-a.startY,a.offsetY=Math.abs(a.deltaY),a.offsetX=Math.abs(a.deltaX),a.direction=a.direction||this.getDirection(a.offsetX,a.offsetY)}};var D={mounted(t,e,o){this.state={}},methods:{showWatch(t,e,o,a){l.showWatch(t,e,o,a,this)},touchstart(t,e){l.touchstart(t,e,this)},touchmove(t,e){l.touchmove(t,e,this)},touchend(t,e){l.touchend(t,e,this)}}};return v(S);})();

__renderjsModules["463e3cc3"]=(()=>{var d=Object.defineProperty;var O=Object.getOwnPropertyDescriptor;var v=Object.getOwnPropertyNames,T=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,Y=Object.prototype.propertyIsEnumerable;var y=(e,t,n)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,g=(e,t)=>{for(var n in t||(t={}))w.call(t,n)&&y(e,n,t[n]);if(T)for(var n of T(t))Y.call(t,n)&&y(e,n,t[n]);return e};var A=(e,t)=>{for(var n in t)d(e,n,{get:t[n],enumerable:!0})},E=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of v(t))!w.call(e,r)&&r!==n&&d(e,r,{get:()=>t[r],enumerable:!(o=O(t,r))||o.enumerable});return e};var L=e=>E(d({},"__esModule",{value:!0}),e);var ne={};A(ne,{default:()=>te});var p={};var x={version:"2.8.6",delayTime:100,errorUpdateKey:"z-paging-error-emit",completeUpdateKey:"z-paging-complete-emit",cachePrefixKey:"z-paging-cache",listCellIndexKey:"zp_index",listCellIndexUniqueKey:"zp_unique_index"};var P="Z-PAGING-REFRESHER-TIME-STORAGE-KEY",c=null,I=!1,l=null,m={};function b(e,t){return()=>{if(W(),!c)return t;let n=c[e];return n===void 0?t:n}}function U(e){let t=null;if(e.touches&&e.touches.length)t=e.touches[0];else if(e.changedTouches&&e.changedTouches.length)t=e.changedTouches[0];else if(e.datail&&e.datail!={})t=e.datail;else return{touchX:0,touchY:0};return{touchX:t.clientX,touchY:t.clientY}}function R(e){if(e&&e.tagName&&e.tagName!=="BODY"&&e.tagName!=="UNI-PAGE-BODY"){let t=e.classList;return t&&t.contains("z-paging-content")?{isFromZp:!0,isPageScroll:t.contains("z-paging-content-page"),isReachedTop:t.contains("z-paging-reached-top"),isUseChatRecordMode:t.contains("z-paging-use-chat-record-mode")}:R(e.parentNode)}else return{isFromZp:!1}}function S(e){return e?e.$refs.paging?e:S(e.$parent):null}function N(e){console.error(`[z-paging]${e}`)}function Z(e,t=x.delayTime,n){let o=setTimeout(e,t);return n&&(m[n]&&clearTimeout(m[n]),m[n]=o),o}function K(e,t){let n=$()||{};n[t]=e,uni.setStorageSync(P,n)}function $(){return uni.getStorageSync(P)}function M(e){let t=$();return t&&t[e]?t[e]:null}function H(e,t){let n=M(e),o=n?Q(n,t):t.none;return`${t.title}${o}`}function j(e){if(Object.prototype.toString.call(e)==="[object Number]")return e;let n=!1;return e.indexOf("rpx")!==-1||e.indexOf("upx")!==-1?(e=e.replace("rpx","").replace("upx",""),n=!0):e.indexOf("px")!==-1&&(e=e.replace("px","")),isNaN(e)?0:Number(n?_(e):e)}function _(e){return uni.upx2px(e)}function B(e=!1){if(e&&l)return l;let t=["DeviceInfo","AppBaseInfo","WindowInfo"],{deviceInfo:n,appBaseInfo:o,windowInfo:r}=t.reduce((f,a)=>{let s=`get${a}`;return uni[s]&&uni.canIUse(s)&&(f[a.charAt(0).toLowerCase()+a.slice(1)]=uni[s]()),f},{});return n&&o&&r?l=g(g(g({},n),o),r):l=uni.getSystemInfoSync(),l}function z(){return new Date().getTime()}function G(){let e=[],t="0123456789abcdef";for(let n=0;n<10;n++)e[n]=t.substr(Math.floor(Math.random()*16),1);return e.join("")+z()}function X(e){return new Promise(t=>{setTimeout(t,e)})}function q(e){return Object.prototype.toString.call(e)==="[object Promise]"}function J(e,t){if(Object.prototype.toString.call(e)==="[object String]"){let n=e;n=n.replace("rpx","").replace("upx","").replace("px",""),e.indexOf("rpx")===-1&&e.indexOf("upx")===-1&&e.indexOf("px")!==-1&&(n=parseFloat(n)*2),e=n}return t==="rpx"?e+"rpx":e/2+"px"}function C(e){if(typeof e!="object"||e===null)return e;let t=Array.isArray(e)?[]:{};for(let n in e)e.hasOwnProperty(n)&&(t[n]=C(e[n]));return t}function W(){I||(p&&Object.keys(p).length&&(c=p),!c&&uni.$zp&&(c=uni.$zp.config),c=c?Object.keys(c).reduce((e,t)=>(e[ee(t)]=c[t],e),{}):null,I=!0)}function Q(e,t){let n=new Date(e),o=new Date,r=new Date(e).setHours(0,0,0,0),f=new Date().setHours(0,0,0,0),a=r-f,s="",F=k(n);return a===0?s=t.today:a===-864e5?s=t.yesterday:s=V(n,n.getFullYear()!==o.getFullYear()),`${s} ${F}`}function V(e,t=!0){let n=e.getFullYear(),o=e.getMonth()+1,r=e.getDate();return t?`${n}-${u(o)}-${u(r)}`:`${u(o)}-${u(r)}`}function k(e){let t=e.getHours(),n=e.getMinutes();return`${u(t)}:${u(n)}`}function u(e){return e=e.toString(),e.length===1?"0"+e:e}function ee(e){return e.replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}var h={gc:b,setRefesrherTime:K,getRefesrherFormatTimeByKey:H,getTouch:U,getTouchFromZPaging:R,getParent:S,convertToPx:j,getTime:z,getInstanceId:G,consoleErr:N,delay:Z,wait:X,isPromise:q,addUnit:J,deepCopy:C,rpx2px:_,getSystemInfoSync:B};var i={startY:0,isTouchFromZPaging:!1,isUsePageScroll:!1,isReachedTop:!0,isIosAndH5:!1,useChatRecordMode:!1,appLaunched:!1},D={mounted(){window&&(this._handleTouch(),this.$ownerInstance.callMethod("_handlePageLaunch"))},methods:{renderPropIsIosAndH5Change(e){e!==-1&&(i.isIosAndH5=e)},_handleTouch(){window.$zPagingRenderJsInited||(window.$zPagingRenderJsInited=!0,window.addEventListener("touchstart",this._handleTouchstart,{passive:!0}),window.addEventListener("touchmove",this._handleTouchmove,{passive:!1}))},_handleTouchstart(e){let t=h.getTouch(e);i.startY=t.touchY;let n=h.getTouchFromZPaging(e.target);i.isTouchFromZPaging=n.isFromZp,i.isUsePageScroll=n.isPageScroll,i.isReachedTop=n.isReachedTop,i.useChatRecordMode=n.isUseChatRecordMode},_handleTouchmove(e){let n=h.getTouch(e).touchY-i.startY;i.isTouchFromZPaging&&(i.isReachedTop&&(i.useChatRecordMode?n<0:n>0)||!i.useChatRecordMode&&i.isIosAndH5&&!i.isUsePageScroll&&n<0)&&e.cancelable&&!e.defaultPrevented&&e.preventDefault()},_removeAllEventListener(){window.removeEventListener("touchstart"),window.removeEventListener("touchmove")}}};var te={name:"z-paging",mixins:[D]};return L(ne);})();

__renderjsModules["06bd5139"]=(()=>{var g=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var W=Object.getOwnPropertyNames,S=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable;var j=(n,e,t)=>e in n?g(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,$=(n,e)=>{for(var t in e||(e={}))k.call(e,t)&&j(n,t,e[t]);if(S)for(var t of S(e))_.call(e,t)&&j(n,t,e[t]);return n};var B=(n,e)=>{for(var t in e)g(n,t,{get:e[t],enumerable:!0})},D=(n,e,t,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of W(e))!k.call(n,r)&&r!==t&&g(n,r,{get:()=>e[r],enumerable:!(o=R(e,r))||o.enumerable});return n};var U=n=>D(g({},"__esModule",{value:!0}),n);var v=(n,e,t)=>new Promise((o,r)=>{var d=a=>{try{i(t.next(a))}catch(c){r(c)}},s=a=>{try{i(t.throw(a))}catch(c){r(c)}},i=a=>a.done?o(a.value):Promise.resolve(a.value).then(d,s);i((t=t.apply(n,e)).next())});var K={};B(K,{default:()=>G});function M(n,e){return v(this,null,function*(){let t=n.getReader(),o;for(;!(o=yield t.read()).done;)e(o.value)})}function A(n){let e,t,o,r=!1;return function(s){e===void 0?(e=s,t=0,o=-1):e=q(e,s);let i=e.length,a=0;for(;t<i;){r&&(e[t]===10&&(a=++t),r=!1);let c=-1;for(;t<i&&c===-1;++t)switch(e[t]){case 58:o===-1&&(o=t-a);break;case 13:r=!0;case 10:c=t;break}if(c===-1)break;n(e.subarray(a,c),o),a=t,o=-1}a===i?e=void 0:a!==0&&(e=e.subarray(a),t-=a)}}function H(n,e,t){let o=L(),r;return r={decode(d){return decodeURIComponent(escape(String.fromCharCode(...d)))}},r=new TextDecoder,function(s,i){if(s.length===0)t==null||t(o),o=L();else if(i>0){let a=r.decode(s.subarray(0,i)),c=i+(s[i+1]===32?2:1),u=r.decode(s.subarray(c));switch(a){case"data":o.data=o.data?o.data+`
`+u:u;break;case"event":o.event=u;break;case"id":n(o.id=u);break;case"retry":let f=parseInt(u,10);isNaN(f)||e(o.retry=f);break;default:let w=r.decode(s,{stream:!0});o.data=w,t(o);break}}}}function q(n,e){let t=new Uint8Array(n.length+e.length);return t.set(n),t.set(e,n.length),t}function L(){return{data:"",event:"",id:"",retry:void 0}}var F=function(n,e){var t={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&e.indexOf(o)<0&&(t[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(n);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(t[o[r]]=n[o[r]]);return t},b="text/event-stream",V=1e3,J="last-event-id";function x(n,e){var{signal:t,headers:o,onopen:r,onmessage:d,onclose:s,onerror:i,openWhenHidden:a,fetch:c}=e,u=F(e,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise((f,w)=>{let h=Object.assign({},o);h.accept||(h.accept=b);let p;function T(){p.abort(),document.hidden||C()}a||document.addEventListener("visibilitychange",T);let I=V,m=0;function O(){document.removeEventListener("visibilitychange",T),window.clearTimeout(m),p.abort()}t==null||t.addEventListener("abort",()=>{O(),f()});let N=c!=null?c:window.fetch,P=r!=null?r:z;function C(){return v(this,null,function*(){var E;p=new AbortController;try{let y=yield N(n,Object.assign(Object.assign({},u),{headers:h,signal:p.signal}));yield P(y),yield M(y.body,A(H(l=>{l?h[J]=l:delete h[J]},l=>{I=l},d))),s==null||s(),O(),f()}catch(y){if(!p.signal.aborted)try{let l=(E=i==null?void 0:i(y))!==null&&E!==void 0?E:I;window.clearTimeout(m),m=window.setTimeout(C,l)}catch(l){O(),w(l)}}})}C()})}function z(n){let e=n.headers.get("content-type");if(!(e!=null&&e.startsWith(b)))throw new Error(`Expected content-type to be ${b}, Actual: ${e}`)}var G={data(){return{ctrl:null}},methods:{objToJson(n){let e={};for(let t in n){let o=n[t];(typeof o=="string"||typeof o=="number"||typeof o=="boolean")&&(e[t]=o)}return e},stopChatCore(){var n;(n=this.ctrl)==null||n.abort()},startChatCore({url:n,body:e,headers:t,method:o}){if(n)try{this.ctrl=new AbortController,x(n,{readJson:!0,method:o,openWhenHidden:!0,signal:this.ctrl.signal,headers:$({"Content-Type":"application/json"},t),body:e||void 0,onopen:r=>{this.$ownerInstance.callMethod("open",this.objToJson(r))},onmessage:r=>{this.$ownerInstance.callMethod("message",r)},onerror:r=>{console.log(r),this.$ownerInstance.callMethod("error",JSON.stringify(r))}}).then(()=>{this.$ownerInstance.callMethod("finish")}).catch(r=>{console.log(r),this.$ownerInstance.callMethod("error",r)})}catch(r){console.log(r)}}}};return U(K);})();
