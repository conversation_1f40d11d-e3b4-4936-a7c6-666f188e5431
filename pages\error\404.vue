<template>
  <view class="container">
    <uni-icons type="help-filled" size="120" color="#909399" />
    <text class="title">404</text>
    <text class="description">您访问的页面不存在～</text>
    <!-- <button type="primary" plain="true" @click="goHome">返回首页</button> -->
  </view>
</template>
  
  <script setup>
// 修改后（正确）
const goHome = () => {
  uni.reLaunch({
    url: "/pages/tabBar/todo/todo", // ✅ 注意路径层级
  });
};
</script>
  
  <style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f8f9fa;

  .title {
    font-size: 96rpx;
    font-weight: 800;
    color: #343a40;
    margin: 32rpx 0;
    letter-spacing: 8rpx;
  }
  .description {
    font-size: 32rpx;
    color: #6c757d;
    margin-bottom: 64rpx;
  }
}
</style>