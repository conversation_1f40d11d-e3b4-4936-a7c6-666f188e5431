/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-d31e1c47]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-d31e1c47]:before {
  content: "\e6c4";
}
.uniui-color[data-v-d31e1c47]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-d31e1c47]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-d31e1c47]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-d31e1c47]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-d31e1c47]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-d31e1c47]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-d31e1c47]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-d31e1c47]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-d31e1c47]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-d31e1c47]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-d31e1c47]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-d31e1c47]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-d31e1c47]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-d31e1c47]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-d31e1c47]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-d31e1c47]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-d31e1c47]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-d31e1c47]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-d31e1c47]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-d31e1c47]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-d31e1c47]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-d31e1c47]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-d31e1c47]:before {
  content: "\e6d4";
}
.uniui-back[data-v-d31e1c47]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-d31e1c47]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-d31e1c47]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-d31e1c47]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-d31e1c47]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-d31e1c47]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-d31e1c47]:before {
  content: "\e6d1";
}
.uniui-down[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-d31e1c47]:before {
  content: "\e6d5";
}
.uniui-right[data-v-d31e1c47]:before {
  content: "\e6b5";
}
.uniui-up[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-top[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-left[data-v-d31e1c47]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-d31e1c47]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-d31e1c47]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-d31e1c47]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-d31e1c47]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-d31e1c47]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-d31e1c47]:before {
  content: "\e649";
}
.uniui-reload[data-v-d31e1c47]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-d31e1c47]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-d31e1c47]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-d31e1c47]:before {
  content: "\e6ad";
}
.uniui-location[data-v-d31e1c47]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-d31e1c47]:before {
  content: "\e683";
}
.uniui-star[data-v-d31e1c47]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-d31e1c47]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-d31e1c47]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-d31e1c47]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-d31e1c47]:before {
  content: "\e6a2";
}
.uniui-font[data-v-d31e1c47]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-d31e1c47]:before {
  content: "\e6a4";
}
.uniui-link[data-v-d31e1c47]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-d31e1c47]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-d31e1c47]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-d31e1c47]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-d31e1c47]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-d31e1c47]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-d31e1c47]:before {
  content: "\e6ab";
}
.uniui-person[data-v-d31e1c47]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-d31e1c47]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-d31e1c47]:before {
  content: "\e69b";
}
.uniui-phone[data-v-d31e1c47]:before {
  content: "\e69c";
}
.uniui-email[data-v-d31e1c47]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-d31e1c47]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-d31e1c47]:before {
  content: "\e692";
}
.uniui-contact[data-v-d31e1c47]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-d31e1c47]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-d31e1c47]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-d31e1c47]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-d31e1c47]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-d31e1c47]:before {
  content: "\e68e";
}
.uniui-upload[data-v-d31e1c47]:before {
  content: "\e690";
}
.uniui-weixin[data-v-d31e1c47]:before {
  content: "\e691";
}
.uniui-compose[data-v-d31e1c47]:before {
  content: "\e67f";
}
.uniui-qq[data-v-d31e1c47]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-d31e1c47]:before {
  content: "\e681";
}
.uniui-pyq[data-v-d31e1c47]:before {
  content: "\e682";
}
.uniui-sound[data-v-d31e1c47]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-d31e1c47]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-d31e1c47]:before {
  content: "\e686";
}
.uniui-trash[data-v-d31e1c47]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-d31e1c47]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-d31e1c47]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-d31e1c47]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-d31e1c47]:before {
  content: "\e68c";
}
.uniui-download[data-v-d31e1c47]:before {
  content: "\e68d";
}
.uniui-help[data-v-d31e1c47]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-d31e1c47]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-d31e1c47]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-d31e1c47]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-d31e1c47]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-d31e1c47]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-d31e1c47]:before {
  content: "\e66c";
}
.uniui-clear[data-v-d31e1c47]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-d31e1c47]:before {
  content: "\e66e";
}
.uniui-minus[data-v-d31e1c47]:before {
  content: "\e66f";
}
.uniui-image[data-v-d31e1c47]:before {
  content: "\e670";
}
.uniui-mic[data-v-d31e1c47]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-d31e1c47]:before {
  content: "\e672";
}
.uniui-close[data-v-d31e1c47]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-d31e1c47]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-d31e1c47]:before {
  content: "\e675";
}
.uniui-plus[data-v-d31e1c47]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-d31e1c47]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-d31e1c47]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-d31e1c47]:before {
  content: "\e668";
}
.uniui-info[data-v-d31e1c47]:before {
  content: "\e669";
}
.uniui-locked[data-v-d31e1c47]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-d31e1c47]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-d31e1c47]:before {
  content: "\e659";
}
.uniui-camera[data-v-d31e1c47]:before {
  content: "\e65a";
}
.uniui-circle[data-v-d31e1c47]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-d31e1c47]:before {
  content: "\e65c";
}
.uniui-chat[data-v-d31e1c47]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-d31e1c47]:before {
  content: "\e65e";
}
.uniui-flag[data-v-d31e1c47]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-d31e1c47]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-d31e1c47]:before {
  content: "\e661";
}
.uniui-home[data-v-d31e1c47]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-d31e1c47]:before {
  content: "\e663";
}
.uniui-gear[data-v-d31e1c47]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-d31e1c47]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-d31e1c47]:before {
  content: "\e666";
}
.uniui-map[data-v-d31e1c47]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-d31e1c47]:before {
  content: "\e656";
}
.uniui-refresh[data-v-d31e1c47]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-d31e1c47]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-d31e1c47]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-d31e1c47]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-d31e1c47]:before {
  content: "\e648";
}
.uniui-redo[data-v-d31e1c47]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-d31e1c47]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-d31e1c47]:before {
  content: "\e64c";
}
.uniui-more[data-v-d31e1c47]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-d31e1c47]:before {
  content: "\e64e";
}
.uniui-undo[data-v-d31e1c47]:before {
  content: "\e64f";
}
.uniui-images[data-v-d31e1c47]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-d31e1c47]:before {
  content: "\e652";
}
.uniui-settings[data-v-d31e1c47]:before {
  content: "\e653";
}
.uniui-search[data-v-d31e1c47]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-d31e1c47]:before {
  content: "\e655";
}
.uniui-list[data-v-d31e1c47]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-d31e1c47]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-d31e1c47]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-d31e1c47]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-d31e1c47]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-d31e1c47]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-d31e1c47]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-d31e1c47]:before {
  content: "\e643";
}
.uniui-heart[data-v-d31e1c47]:before {
  content: "\e639";
}
.uniui-loop[data-v-d31e1c47]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-d31e1c47]:before {
  content: "\e632";
}
.uniui-scan[data-v-d31e1c47]:before {
  content: "\e62a";
}
.uniui-bars[data-v-d31e1c47]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-d31e1c47]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-d31e1c47]:before {
  content: "\e62c";
}
.uniui-shop[data-v-d31e1c47]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-d31e1c47]:before {
  content: "\e630";
}
.uniui-cart[data-v-d31e1c47]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-d31e1c47] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #666666;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0deg);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(90deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(270deg);
}
100% {
    transform: rotate(360deg);
}
}
[data-v-8845ff2f] .hl-code,[data-v-8845ff2f] .hl-pre{color:#ccc;background:0 0;font-family:Consolas,Monaco,'Andale Mono','Ubuntu Mono',monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;tab-size:4;-webkit-hyphens:none;hyphens:none}[data-v-8845ff2f] .hl-pre{padding:1em;margin:.5em 0;overflow:auto}[data-v-8845ff2f] .hl-pre{background:#2d2d2d}[data-v-8845ff2f] .hl-block-comment,[data-v-8845ff2f] .hl-cdata,[data-v-8845ff2f] .hl-comment,[data-v-8845ff2f] .hl-doctype,[data-v-8845ff2f] .hl-prolog{color:#999}[data-v-8845ff2f] .hl-punctuation{color:#ccc}[data-v-8845ff2f] .hl-attr-name,[data-v-8845ff2f] .hl-deleted,[data-v-8845ff2f] .hl-namespace,[data-v-8845ff2f] .hl-tag{color:#e2777a}[data-v-8845ff2f] .hl-function-name{color:#6196cc}[data-v-8845ff2f] .hl-boolean,[data-v-8845ff2f] .hl-function,[data-v-8845ff2f] .hl-number{color:#f08d49}[data-v-8845ff2f] .hl-class-name,[data-v-8845ff2f] .hl-constant,[data-v-8845ff2f] .hl-property,[data-v-8845ff2f] .hl-symbol{color:#f8c555}[data-v-8845ff2f] .hl-atrule,[data-v-8845ff2f] .hl-builtin,[data-v-8845ff2f] .hl-important,[data-v-8845ff2f] .hl-keyword,[data-v-8845ff2f] .hl-selector{color:#cc99cd}[data-v-8845ff2f] .hl-attr-value,[data-v-8845ff2f] .hl-char,[data-v-8845ff2f] .hl-regex,[data-v-8845ff2f] .hl-string,[data-v-8845ff2f] .hl-variable{color:#7ec699}[data-v-8845ff2f] .hl-entity,[data-v-8845ff2f] .hl-operator,[data-v-8845ff2f] .hl-url{color:#67cdcc}[data-v-8845ff2f] .hl-bold,[data-v-8845ff2f] .hl-important{font-weight:700}[data-v-8845ff2f] .hl-italic{font-style:italic}[data-v-8845ff2f] .hl-entity{cursor:help}[data-v-8845ff2f] .hl-inserted{color:green}[data-v-8845ff2f] .md-p {
  -webkit-margin-before: 1em;
          margin-block-start: 1em;
  -webkit-margin-after: 1em;
          margin-block-end: 1em;
}
[data-v-8845ff2f] .md-table,[data-v-8845ff2f] .md-blockquote {
  margin-bottom: 16px;
}
[data-v-8845ff2f] .md-table {
  box-sizing: border-box;
  width: 100%;
  overflow: auto;
  border-spacing: 0;
  border-collapse: collapse;
}
[data-v-8845ff2f] .md-tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}
.md-table .md-tr[data-v-8845ff2f]:nth-child(2n) {
  background-color: #f6f8fa;
}
[data-v-8845ff2f] .md-th,[data-v-8845ff2f] .md-td {
  padding: 6px 13px !important;
  border: 1px solid #dfe2e5;
}
[data-v-8845ff2f] .md-th {
  font-weight: 600;
}
[data-v-8845ff2f] .md-blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}
[data-v-8845ff2f] .md-code {
  padding: 0.2em 0.4em;
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}
[data-v-8845ff2f] .md-pre .md-code {
  padding: 0;
  font-size: 100%;
  background: transparent;
  border: 0;
}
/* a 标签默认效果 */
._a[data-v-8845ff2f] {
  padding: 1.5px 0 1.5px 0;
  color: #4D5BDE;
  word-break: break-all;
  font-weight: 600;
  cursor: pointer;
}

/* a 标签点击态效果 */
._hover[data-v-8845ff2f] {
  text-decoration: underline;
  opacity: 0.7;
}

/* 图片默认效果 */
._img[data-v-8845ff2f] {
  max-width: 100%;
  -webkit-touch-callout: none;
}

/* 内部样式 */
._block[data-v-8845ff2f] {
  display: block;
}
._b[data-v-8845ff2f],
._strong[data-v-8845ff2f] {
  font-weight: bold;
}
._code[data-v-8845ff2f] {
  font-family: monospace;
}
._del[data-v-8845ff2f] {
  text-decoration: line-through;
}
._em[data-v-8845ff2f],
._i[data-v-8845ff2f] {
  font-style: italic;
}
._h1[data-v-8845ff2f] {
  font-size: 2em;
}
._h2[data-v-8845ff2f] {
  font-size: 1.5em;
}
._h3[data-v-8845ff2f] {
  font-size: 1.17em;
}
._h5[data-v-8845ff2f] {
  font-size: 0.83em;
}
._h6[data-v-8845ff2f] {
  font-size: 0.67em;
}
._h1[data-v-8845ff2f],
._h2[data-v-8845ff2f],
._h3[data-v-8845ff2f],
._h4[data-v-8845ff2f],
._h5[data-v-8845ff2f],
._h6[data-v-8845ff2f] {
  display: block;
  font-weight: bold;
}
._image[data-v-8845ff2f] {
  height: 1px;
}
._ins[data-v-8845ff2f] {
  text-decoration: underline;
}
._li[data-v-8845ff2f] {
  display: list-item;
}
._ol[data-v-8845ff2f] {
  list-style-type: decimal;
}
._ol[data-v-8845ff2f],
._ul[data-v-8845ff2f] {
  display: block;
  padding-left: 40px;
  margin: 1em 0;
}
._q[data-v-8845ff2f]::before {
  content: '"';
}
._q[data-v-8845ff2f]::after {
  content: '"';
}
._sub[data-v-8845ff2f] {
  font-size: smaller;
  vertical-align: sub;
}
._sup[data-v-8845ff2f] {
  font-size: smaller;
  vertical-align: super;
}
._thead[data-v-8845ff2f],
._tbody[data-v-8845ff2f],
._tfoot[data-v-8845ff2f] {
  display: table-row-group;
}
._tr[data-v-8845ff2f] {
  display: table-row;
}
._td[data-v-8845ff2f],
._th[data-v-8845ff2f] {
  display: table-cell;
  vertical-align: middle;
}
._th[data-v-8845ff2f] {
  font-weight: bold;
  text-align: center;
}
._ul[data-v-8845ff2f] {
  list-style-type: disc;
}
._ul ._ul[data-v-8845ff2f] {
  margin: 0;
  list-style-type: circle;
}
._ul ._ul ._ul[data-v-8845ff2f] {
  list-style-type: square;
}
._abbr[data-v-8845ff2f],
._b[data-v-8845ff2f],
._code[data-v-8845ff2f],
._del[data-v-8845ff2f],
._em[data-v-8845ff2f],
._i[data-v-8845ff2f],
._ins[data-v-8845ff2f],
._label[data-v-8845ff2f],
._q[data-v-8845ff2f],
._span[data-v-8845ff2f],
._strong[data-v-8845ff2f],
._sub[data-v-8845ff2f],
._sup[data-v-8845ff2f] {
  display: inline;
}
._video[data-v-8845ff2f] {
  width: 300px;
  height: 225px;
}




/* 根节点样式 */
._root[data-v-a290f043] {
  padding: 1px 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 长按复制 */
._select[data-v-a290f043] {
  -webkit-user-select: text;
          user-select: text;
}


/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-tag[data-v-1f94d070] {
  line-height: 14px;
  font-size: 12px;
  font-weight: 200;
  padding: 4px 7px;
  color: #fff;
  border-radius: 3px;
  background-color: #8f939c;
  border-width: 1px;
  border-style: solid;
  border-color: #8f939c;
}
.uni-tag--default[data-v-1f94d070] {
  font-size: 12px;
}
.uni-tag--default--inverted[data-v-1f94d070] {
  color: #8f939c;
  border-color: #8f939c;
}
.uni-tag--small[data-v-1f94d070] {
  padding: 2px 5px;
  font-size: 12px;
  border-radius: 2px;
}
.uni-tag--mini[data-v-1f94d070] {
  padding: 1px 3px;
  font-size: 12px;
  border-radius: 2px;
}
.uni-tag--primary[data-v-1f94d070] {
  background-color: #2979ff;
  border-color: #2979ff;
  color: #fff;
}
.uni-tag--success[data-v-1f94d070] {
  color: #fff;
  background-color: #18bc37;
  border-color: #18bc37;
}
.uni-tag--warning[data-v-1f94d070] {
  color: #fff;
  background-color: #f3a73f;
  border-color: #f3a73f;
}
.uni-tag--error[data-v-1f94d070] {
  color: #fff;
  background-color: #e43d33;
  border-color: #e43d33;
}
.uni-tag--primary--inverted[data-v-1f94d070] {
  color: #2979ff;
  border-color: #2979ff;
}
.uni-tag--success--inverted[data-v-1f94d070] {
  color: #18bc37;
  border-color: #18bc37;
}
.uni-tag--warning--inverted[data-v-1f94d070] {
  color: #f3a73f;
  border-color: #f3a73f;
}
.uni-tag--error--inverted[data-v-1f94d070] {
  color: #e43d33;
  border-color: #e43d33;
}
.uni-tag--inverted[data-v-1f94d070] {
  background-color: #fff;
}
.uni-tag--circle[data-v-1f94d070] {
  border-radius: 15px;
}
.uni-tag--mark[data-v-1f94d070] {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.uni-tag--disabled[data-v-1f94d070] {
  opacity: 0.5;
}
.uni-tag-text[data-v-1f94d070] {
  color: #fff;
  font-size: 14px;
}
.uni-tag-text--primary[data-v-1f94d070] {
  color: #2979ff;
}
.uni-tag-text--success[data-v-1f94d070] {
  color: #18bc37;
}
.uni-tag-text--warning[data-v-1f94d070] {
  color: #f3a73f;
}
.uni-tag-text--error[data-v-1f94d070] {
  color: #e43d33;
}
.uni-tag-text--small[data-v-1f94d070] {
  font-size: 12px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-scroll-wrapper[data-v-1849c0d0] {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.chat-scroll-container[data-v-1849c0d0] {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column-reverse;
}
.message-list-wrapper[data-v-1849c0d0] {
  display: flex;
  flex-direction: column-reverse;
  max-width: 750px;
  margin: 0 auto;
}
.message-list-wrapper.full-height[data-v-1849c0d0] {
  min-height: 100%;
}
.bottom-anchor[data-v-1849c0d0] {
  height: 1px;
  opacity: 0;
  flex-shrink: 0;
}
.message-list[data-v-1849c0d0] {
  padding: 10px 15px;
  box-sizing: border-box;
}
.timestamp[data-v-1849c0d0] {
  text-align: center;
  font-size: 12px;
  color: #999;
  padding: 15px 0;
  flex-shrink: 0;
  margin-top: 55px;
}
.load-more-container[data-v-1849c0d0] {
  display: flex;
  justify-content: center;
  padding: 15px 0;
  flex-shrink: 0;
  margin-top: 55px;
}
.load-more-container .load-more-button[data-v-1849c0d0] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #ffffff;
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.load-more-container .load-more-button[data-v-1849c0d0]:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}
.load-more-container .load-more-button.loading[data-v-1849c0d0] {
  opacity: 0.7;
  pointer-events: none;
}
.load-more-container .load-more-button .load-more-text[data-v-1849c0d0] {
  margin-left: 5px;
}
.chat-scroll-container[data-v-1849c0d0] {
  height: calc(100vh - 183px);
}
@keyframes fadeIn-1849c0d0 {
from {
    opacity: 0;
    transform: translateY(10px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.message-list > uni-view[data-v-1849c0d0] {
  animation: fadeIn-1849c0d0 0.3s ease-out;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.popup-mask[data-v-74534e6a] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-mask .popup-content[data-v-74534e6a] {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  max-width: 750px;
  max-width: 750px;
  margin: 0 auto;
}
.popup-mask .popup-header[data-v-74534e6a] {
  position: relative;
  padding: 20px 0;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}
.popup-mask .popup-title[data-v-74534e6a] {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}
.popup-mask .close-icon[data-v-74534e6a] {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  padding: 5px;
}
.popup-mask .popup-body[data-v-74534e6a] {
  padding: 20px;
}
.popup-mask .input-group[data-v-74534e6a] {
  margin-bottom: 15px;
}
.popup-mask .input-field[data-v-74534e6a] {
  width: 100%;
  height: 45px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 15px;
  box-sizing: border-box;
}
.popup-mask .submit-button[data-v-74534e6a] {
  height: 45px;
  background-color: #000;
  color: #fff;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin-top: 20px;
}
.popup-mask .submit-button.disabled[data-v-74534e6a] {
  background-color: #ccc;
}
@media screen and (max-width: 480px) {
.login-verify-container .popup-content[data-v-74534e6a] {
    max-width: 375px;
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 登录验证弹窗容器
 */
.login-verify-container .popup-mask[data-v-6c9f485e] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.login-verify-container .popup-content[data-v-6c9f485e] {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  max-width: 750px;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
.login-verify-container .popup-header[data-v-6c9f485e] {
  position: relative;
  padding: 20px 0;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}
.login-verify-container .popup-header .popup-title[data-v-6c9f485e] {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}
.login-verify-container .popup-header .close-icon[data-v-6c9f485e] {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  padding: 5px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.login-verify-container .popup-header .close-icon[data-v-6c9f485e]:hover {
  opacity: 0.7;
}
.login-verify-container .popup-header .close-icon[data-v-6c9f485e]:active {
  opacity: 0.5;
}
.login-verify-container .popup-body[data-v-6c9f485e] {
  padding: 20px;
}
.login-verify-container .popup-body .input-group[data-v-6c9f485e] {
  margin-bottom: 15px;
}
.login-verify-container .popup-body .input-group .input-field[data-v-6c9f485e] {
  width: 100%;
  height: 45px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 15px;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}
.login-verify-container .popup-body .input-group .input-field[data-v-6c9f485e]:focus {
  border-color: #000;
  outline: none;
}
.login-verify-container .popup-body .input-group .input-field[data-v-6c9f485e]::-webkit-input-placeholder {
  color: #999;
}
.login-verify-container .popup-body .input-group .input-field[data-v-6c9f485e]::placeholder {
  color: #999;
}
.login-verify-container .popup-body .input-group.code-group[data-v-6c9f485e] {
  display: flex;
  align-items: center;
}
.login-verify-container .popup-body .input-group.code-group .input-field[data-v-6c9f485e] {
  flex: 1;
}
.login-verify-container .popup-body .input-group.code-group .code-button[data-v-6c9f485e] {
  width: 110px;
  height: 45px;
  background-color: #f5f5f5;
  color: #333;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-user-select: none;
          user-select: none;
}
.login-verify-container .popup-body .input-group.code-group .code-button[data-v-6c9f485e]:hover:not(.disabled) {
  background-color: #e8e8e8;
}
.login-verify-container .popup-body .input-group.code-group .code-button[data-v-6c9f485e]:active:not(.disabled) {
  background-color: gainsboro;
}
.login-verify-container .popup-body .input-group.code-group .code-button.disabled[data-v-6c9f485e] {
  color: #999;
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}
.login-verify-container .popup-body .submit-button[data-v-6c9f485e] {
  height: 45px;
  background-color: #000;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  margin-top: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-user-select: none;
          user-select: none;
}
.login-verify-container .popup-body .submit-button[data-v-6c9f485e]:hover:not(.disabled) {
  background-color: #1a1a1a;
}
.login-verify-container .popup-body .submit-button[data-v-6c9f485e]:active:not(.disabled) {
  background-color: black;
  transform: translateY(1px);
}
.login-verify-container .popup-body .submit-button.disabled[data-v-6c9f485e] {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}
.login-verify-container .popup-body .submit-button.disabled[data-v-6c9f485e]:hover {
  background-color: #ccc;
  transform: none;
}
@media screen and (max-width: 480px) {
.login-verify-container .popup-content[data-v-6c9f485e] {
    margin: 0 10px;
    max-width: 375px;
}
.login-verify-container .popup-body[data-v-6c9f485e] {
    padding: 15px;
}
.login-verify-container .popup-body .input-group.code-group .code-button[data-v-6c9f485e] {
    width: 90px;
    font-size: 12px;
}
}
.popup-mask[data-v-6c9f485e] {
  animation: fadeIn-6c9f485e 0.3s ease-out;
}
.popup-content[data-v-6c9f485e] {
  animation: slideUp-6c9f485e 0.3s ease-out;
}
@keyframes fadeIn-6c9f485e {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp-6c9f485e {
from {
    opacity: 0;
    transform: translateY(50px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-header-container[data-v-9a40a12e] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
}
.chat-header-container .chat-header[data-v-9a40a12e] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 0 auto;
  max-width: 730px;
}
.chat-header-container .chat-header .header-left[data-v-9a40a12e] {
  display: flex;
  cursor: pointer;
  position: relative;
  /* 为红点定位添加相对定位 */
  /* 未读消息红点样式 */
}
.chat-header-container .chat-header .header-left .left-bar[data-v-9a40a12e] {
  width: 26px;
  height: 26px;
}
.chat-header-container .chat-header .header-left .unread-dot[data-v-9a40a12e] {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: var(---, #FA5151);
  border-radius: 50%;
  border: 1px solid #ffffff;
  z-index: 1;
}
.chat-header-container .chat-header .header-right[data-v-9a40a12e] {
  display: flex;
}
.chat-header-container .chat-header .header-right .header-btn-login[data-v-9a40a12e] {
  background-color: black;
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: white;
  border-radius: 10px;
}
.chat-header-container .chat-header .header-right .actions-right[data-v-9a40a12e] {
  display: flex;
  height: 30px;
  padding: 0 10px 0 8px;
  align-items: center;
  gap: 4px;
  border-radius: 10px;
  line-height: normal;
  cursor: pointer;
}
.chat-header-container .chat-header .header-right .actions-right .chat-scale-icon[data-v-9a40a12e] {
  width: 26px;
  height: 26px;
}
.chat-header-container .chat-header .avatar-container[data-v-9a40a12e] {
  display: flex;
  align-items: center;
}
.chat-header-container .chat-header .avatar-container .avatar[data-v-9a40a12e] {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin-right: 10px;
}
.chat-header-container .chat-header .avatar-container .agent-name[data-v-9a40a12e] {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b], .uni-popup.left[data-v-4dd3c44b], .uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b], .uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {
  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.drawer-popup[data-v-0a0b5b61] {
  z-index: 9999;
}
.drawer-popup .drawer-container[data-v-0a0b5b61] {
  display: flex;
  flex-direction: column;
  width: 286px;
  height: calc(100vh - 46px);
  background: #F8F7F6;
}
.drawer-popup .drawer-container .drawer-header[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 12px 12px 0px 12px;
}
.drawer-popup .drawer-container .drawer-header .tab-item[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  padding: 0 0 12px 0;
  border-radius: 8px;
  cursor: pointer;
}
.drawer-popup .drawer-container .drawer-header .tab-item .tab-text[data-v-0a0b5b61] {
  color: var(---, #1a1a1a);
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.drawer-popup .drawer-container .drawer-header .search-icon[data-v-0a0b5b61] {
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.drawer-popup .drawer-container .assistant-section[data-v-0a0b5b61] {
  margin: 0 10px;
}
.drawer-popup .drawer-container .assistant-section .assistant-item[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 10px;
  border: 1px solid var(---normal, #E2E4E9);
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.drawer-popup .drawer-container .assistant-section .assistant-item.active[data-v-0a0b5b61] {
  background-color: #EEEDEC;
}
.drawer-popup .drawer-container .assistant-section .assistant-item.unread .assistant-name[data-v-0a0b5b61] {
  font-weight: 600;
}
.drawer-popup .drawer-container .assistant-section .assistant-item .unread-dot-ivy[data-v-0a0b5b61] {
  width: 7px;
  height: 7px;
  background: var(---, #FA5151);
  border-radius: 50%;
  margin-top: 6px;
}
.drawer-popup .drawer-container .assistant-section .assistant-item .assistant-avatar[data-v-0a0b5b61] {
  width: 28px;
  height: 28px;
  border-radius: 10px;
  margin-right: 6px;
  flex-shrink: 0;
}
.drawer-popup .drawer-container .assistant-section .assistant-item .assistant-info[data-v-0a0b5b61] {
  flex: 1;
  min-width: 0;
  font-size: 12px;
  /* 确保 flex 子元素能够收缩 */
}
.drawer-popup .drawer-container .assistant-section .assistant-item .assistant-info .assistant-name[data-v-0a0b5b61] {
  color: var(---, #1A1A1A);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: block;
}
.drawer-popup .drawer-container .assistant-section .assistant-item .assistant-info .assistant-message[data-v-0a0b5b61] {
  color: var(---, #787D86);
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: block;
}
.drawer-popup .drawer-container .section-title[data-v-0a0b5b61] {
  padding: 20px 12px 10px 12px;
}
.drawer-popup .drawer-container .section-title .title-text[data-v-0a0b5b61] {
  color: var(---, #787D86);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.drawer-popup .drawer-container .drawer-content[data-v-0a0b5b61] {
  flex: 1;
  overflow: hidden;
  display: flex;
}
.drawer-popup .drawer-container .drawer-content .empty-state[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 53px;
  margin: 0px 8px;
  border-radius: 10px;
  border: 1px dashed var(---normal, #E2E4E9);
  width: 100%;
}
.drawer-popup .drawer-container .drawer-content .empty-state .empty-text[data-v-0a0b5b61] {
  font-size: 14px;
  color: #999999;
  text-align: center;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-container[data-v-0a0b5b61] {
  /* 列表容器不设置固定高度，让内容自然撑开 */
  min-height: 100%;
  /* 确保至少占满父容器 */
  display: flex;
  flex-direction: column;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin: 5px 12px 5px 12px;
  padding: 8px;
  border-radius: 10px;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item .item-content[data-v-0a0b5b61] {
  flex: 1;
  min-width: 0;
  /* 确保 flex 子元素能够收缩 */
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item .item-content .item-title[data-v-0a0b5b61] {
  color: var(---, #1A1A1A);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 1px;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item .item-content .item-description[data-v-0a0b5b61] {
  color: var(---, #787D86);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: opacity 0.3s ease;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item .unread-dot[data-v-0a0b5b61] {
  width: 7px;
  height: 7px;
  background: var(---, #FA5151);
  border-radius: 50%;
  margin-top: -16px;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item.unread .item-title[data-v-0a0b5b61] {
  font-weight: 500;
}
.drawer-popup .drawer-container .drawer-content .opportunity-list .list-item.active[data-v-0a0b5b61] {
  background-color: #EEEDEC;
}
.drawer-popup .drawer-container .drawer-footer[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 14px 33px 14px;
  border-top: 1px solid var(---normal, #E2E4E9);
}
.drawer-popup .drawer-container .drawer-footer .user-info[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.drawer-popup .drawer-container .drawer-footer .user-info .user-avatar[data-v-0a0b5b61] {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid var(---normal, #E2E4E9);
}
.drawer-popup .drawer-container .drawer-footer .user-info .user-name[data-v-0a0b5b61] {
  color: var(---, #1A1A1A);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
.drawer-popup .drawer-container .drawer-footer .footer-actions[data-v-0a0b5b61] {
  display: flex;
  align-items: center;
}
.drawer-popup .drawer-container .drawer-footer .footer-actions .action-icon[data-v-0a0b5b61] {
  cursor: pointer;
}
.drawer-popup .drawer-container .drawer-footer .footer-actions .action-icon .setting-icon[data-v-0a0b5b61] {
  width: 26px;
  height: 26px;
  background-size: 100% 100%;
}
.drawer-popup .drawer-container .drawer-footer .footer-actions .notification-icon[data-v-0a0b5b61] {
  position: relative;
}
.drawer-popup .drawer-container .drawer-footer .footer-actions .notification-dot[data-v-0a0b5b61] {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: var(---, #FA5151);
  border-radius: 50%;
  border: 1px solid #ffffff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.voice-player[data-v-f9a97dab] {
  display: none;
}
.voice-player-module[data-v-f9a97dab] {
  display: none;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-mix-container[data-v-e0c7f44e] {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}
.message-images-container[data-v-e0c7f44e],
.message-files-container[data-v-e0c7f44e],
.message-text-container[data-v-e0c7f44e] {
  max-width: 365px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.message-images-container .message-images-row[data-v-e0c7f44e] {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
  white-space: nowrap;
}
.message-images-container .message-images-row .message-image-item[data-v-e0c7f44e] {
  display: inline-block;
  width: 100px;
  height: 100px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 10px;
  min-width: 100px;
}
.message-images-container .message-images-row .message-image-item[data-v-e0c7f44e]:last-child {
  margin-right: 0;
}
.message-images-container .message-images-row .message-image-item uni-image[data-v-e0c7f44e] {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.message-files-container .message-files-row[data-v-e0c7f44e] {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}
.message-files-container .message-files-row .message-file-item[data-v-e0c7f44e] {
  display: inline-block;
  min-width: 100px;
  max-width: 300px;
  margin: 5px;
}
.message-files-container .message-files-row .message-file-item[data-v-e0c7f44e]:last-child {
  margin: 0;
}
.message-files-container .message-files-row .message-file-item .file-info[data-v-e0c7f44e] {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px;
  border-radius: 10px;
  background-color: #FFFFFF;
  border: solid 1px #E2E4E9;
}
.message-files-container .message-files-row .message-file-item .file-info .file-type-icon[data-v-e0c7f44e] {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}
.message-files-container .message-files-row .message-file-item .file-info .file-details[data-v-e0c7f44e] {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  align-items: flex-end;
}
.message-files-container .message-files-row .message-file-item .file-info .file-details .file-name[data-v-e0c7f44e] {
  font-size: 12px;
  font-weight: 400;
  color: #3E4551;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  width: 100%;
}
.message-text-container .message-text-row[data-v-e0c7f44e] {
  min-width: 60px;
  font-size: 14px;
  font-weight: 400;
  color: black;
  text-align: justify;
}
.message-text-container .message-text-row .message-text[data-v-e0c7f44e] {
  background-color: #A4E17B;
  padding: 10px;
  border-radius: 10px 0 10px 10px;
  width: auto;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  user-select: text;
  /* 允许文本选择 */
  -webkit-user-select: text;
  /* 针对 Safari */
  -moz-user-select: text;
  /* 针对 Firefox */
  -ms-user-select: text;
  /* 针对 IE 和 Edge */
}

/* 针对 PC 端的样式，最大宽度为 100% */
@media (min-width: 1024px) {
.message-images-container[data-v-e0c7f44e],
.message-files-container[data-v-e0c7f44e],
.message-text-container[data-v-e0c7f44e] {
    max-width: 700px;
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uploaded-files-container[data-v-68080a02] {
  margin-bottom: 2px;
  overflow: visible;
  padding: 5px 15px;
}
.uploaded-files-container .file-list-scroll[data-v-68080a02] {
  width: 100%;
  white-space: nowrap;
}
.uploaded-files-container .file-list[data-v-68080a02] {
  display: inline-flex;
  align-items: center;
  padding: 4px 0;
  max-height: 40px;
}
.uploaded-files-container .file-list .img-item[data-v-68080a02] {
  position: relative;
  margin-right: 10px;
}
.uploaded-files-container .file-list .img-item .file-image[data-v-68080a02] {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-size: 100% 100%;
}
.uploaded-files-container .file-list .file-item[data-v-68080a02] {
  position: relative;
  display: flex;
  align-items: center;
  margin-right: 10px;
  max-width: 160px;
  background-color: #ffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  max-height: 40px;
  min-height: 40px;
  padding: 0 10px;
  cursor: pointer;
}
.uploaded-files-container .file-list .file-item .file-thumbnail[data-v-68080a02] {
  height: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.uploaded-files-container .file-list .file-item .file-thumbnail .file-image[data-v-68080a02] {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.uploaded-files-container .file-list .file-item .file-thumbnail .file-type-icon[data-v-68080a02] {
  width: 28px;
  height: 28px;
  object-fit: contain;
}
.uploaded-files-container .file-list .file-item .file-name[data-v-68080a02] {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #3E4551;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.uploaded-files-container .file-list .file-delete[data-v-68080a02] {
  position: absolute;
  top: -2px;
  right: -4px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 2;
}
.uploaded-files-container .file-list .file-delete .delete-icon[data-v-68080a02] {
  font-size: 12px;
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.skill-selector[data-v-a0e50f30] {
  border-radius: 10px 10px 0 0;
  overflow: hidden;
  height: 70vh;
  max-height: 700px;
  max-width: 750px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1000;
  transition: all 0.3s ease;
}
.skill-selector.pc-centered[data-v-a0e50f30] {
  height: 50vh;
  max-height: 500px;
  min-width: 750px;
  max-width: 750px;
  width: 100%;
  margin: 0 auto;
  border-radius: 10px;
}
[data-v-a0e50f30] .uni-popup {
  z-index: 999 !important;
}
[data-v-a0e50f30] .uni-popup__wrapper {
  z-index: 999 !important;
}
.skill-header[data-v-a0e50f30] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #fff;
  position: relative;
  z-index: 10001;
}
.skill-header .skill-title[data-v-a0e50f30] {
  color: var(---, #000);
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.skill-header .close-icon[data-v-a0e50f30] {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.skill-header .close-icon[data-v-a0e50f30]:active {
  opacity: 0.6;
}
.skill-content[data-v-a0e50f30] {
  flex: 1;
  padding-left: 20px;
  overflow-y: auto;
  position: relative;
  z-index: 10001;
}
.skill-category[data-v-a0e50f30] {
  margin-bottom: 18px;
}
.skill-category[data-v-a0e50f30]:last-child {
  margin-bottom: 0;
}
.skill-category .category-title[data-v-a0e50f30] {
  color: var(---, #787D86);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.skill-category .category-title[data-v-a0e50f30]::before {
  content: "";
  width: 3px;
  height: 12px;
  margin-top: 2px;
  background-color: #4D5BDE;
  border-radius: 2px;
  margin-right: 8px;
}
.skill-category .skill-tags[data-v-a0e50f30] {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.skill-category .skill-tag[data-v-a0e50f30] {
  border-radius: 10px;
  border: 1px solid var(---normal, #E2E4E9);
  background: #FFF;
  padding: 4px 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.skill-category .skill-tag[data-v-a0e50f30]::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}
.skill-category .skill-tag[data-v-a0e50f30]:active {
  transform: scale(0.96);
}
.skill-category .skill-tag[data-v-a0e50f30]:active::before {
  left: 100%;
}
.skill-category .skill-tag.skill-tag-active[data-v-a0e50f30] {
  border-color: #4D5BDE;
  background: #EEEFFC;
}
.skill-category .skill-tag.skill-tag-active .skill-tag-text[data-v-a0e50f30] {
  color: #4D5BDE;
  font-weight: 500;
}
.skill-category .skill-tag .skill-tag-text[data-v-a0e50f30] {
  transition: all 0.3s ease;
  color: var(---, #000);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.skill-content[data-v-a0e50f30]::-webkit-scrollbar {
  width: 4px;
}
.skill-content[data-v-a0e50f30]::-webkit-scrollbar-track {
  background: transparent;
}
.skill-content[data-v-a0e50f30]::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}
.skill-content[data-v-a0e50f30]::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
.loading-container[data-v-a0e50f30] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 200px;
}

.uni-tooltip[data-v-5fb5c624] {
		position: relative;
		cursor: pointer;
		display: inline-block;
}
.uni-tooltip-popup[data-v-5fb5c624] {
		z-index: 1;
		display: none;
		position: absolute;
		background-color: #333;
		border-radius: 8px;
		color: #fff;
		font-size: 12px;
		text-align: left;
		line-height: 16px;
		padding: 12px;
		overflow: auto;
}
.uni-tooltip:hover .uni-tooltip-popup[data-v-5fb5c624] {
		display: block;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-actions[data-v-49c55965] {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-left: 4px;
  height: 20px;
  opacity: 0;
  width: -webkit-max-content;
  width: max-content;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}
.message-actions.actions-visible[data-v-49c55965] {
  opacity: 1;
  visibility: visible;
}
.message-actions .uni-icons[data-v-49c55965] {
  margin-right: 10px;
}
.message-actions[data-v-49c55965] .uni-tooltip-popup {
  width: -webkit-max-content;
  width: max-content;
}
.item-icon[data-v-49c55965] {
  height: 20px;
  width: 20px;
  margin-right: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.item-icon .item-icon-img[data-v-49c55965] {
  height: 20px;
  width: 20px;
  transition: all 0.2s ease;
}
.item-icon .item-icon-img.icon-active[data-v-49c55965] {
  transform: scale(1.05);
}
.play-icon[data-v-49c55965] {
  width: 20px;
  height: 20px;
}
.voice-playing-animation[data-v-49c55965] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
}
.voice-playing-animation .voice-bar[data-v-49c55965] {
  width: 2px;
  height: 10px;
  background-color: #007aff;
  margin: 0 1px;
  border-radius: 1px;
  animation: sound-wave-49c55965 1s infinite ease-in-out;
}
.voice-playing-animation .voice-bar[data-v-49c55965]:nth-child(1) {
  animation-delay: 0s;
}
.voice-playing-animation .voice-bar[data-v-49c55965]:nth-child(2) {
  animation-delay: 0.2s;
  height: 13px;
}
.voice-playing-animation .voice-bar[data-v-49c55965]:nth-child(3) {
  animation-delay: 0.4s;
  height: 16px;
}
.voice-playing-animation .voice-bar[data-v-49c55965]:nth-child(4) {
  animation-delay: 0.6s;
  height: 10px;
}
@keyframes sound-wave-49c55965 {
0%, 100% {
    transform: scaleY(1);
}
50% {
    transform: scaleY(0.3);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-message-actions[data-v-bc1ff85f] {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-right: 4px;
  height: 20px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}
.user-message-actions.actions-visible[data-v-bc1ff85f] {
  opacity: 1;
  visibility: visible;
}
.user-message-actions[data-v-bc1ff85f] .uni-tooltip-popup {
  width: -webkit-max-content;
  width: max-content;
}
.item-icon[data-v-bc1ff85f] {
  height: 20px;
  width: 20px;
  margin-left: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.item-icon .item-icon-img[data-v-bc1ff85f] {
  height: 20px;
  width: 20px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.feedback-modal[data-v-541c25f6] {
  width: 350px;
  max-width: 90vw;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
}
.feedback-modal .feedback-header[data-v-541c25f6] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}
.feedback-modal .feedback-header .feedback-title[data-v-541c25f6] {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.feedback-modal .feedback-content[data-v-541c25f6] {
  padding: 20px;
}
.feedback-modal .feedback-content .feedback-textarea[data-v-541c25f6] {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background-color: #fafafa;
  resize: none;
  box-sizing: border-box;
  transition: border-color 0.2s, background-color 0.2s;
}
.feedback-modal .feedback-content .feedback-textarea[data-v-541c25f6]:focus {
  border-color: #007aff;
  background-color: #fff;
  outline: none;
}
.feedback-modal .feedback-content .feedback-textarea[data-v-541c25f6]::-webkit-input-placeholder {
  color: #999;
}
.feedback-modal .feedback-content .feedback-textarea[data-v-541c25f6]::placeholder {
  color: #999;
}
.feedback-modal .feedback-content .char-count[data-v-541c25f6] {
  margin-top: 4px;
  text-align: right;
}
.feedback-modal .feedback-content .char-count .count-text[data-v-541c25f6] {
  font-size: 12px;
  color: #999;
}
.feedback-modal .feedback-footer[data-v-541c25f6] {
  display: flex;
  padding: 0px 20px 20px;
  gap: 12px;
}
.feedback-modal .feedback-footer .btn-cancel[data-v-541c25f6],
.feedback-modal .feedback-footer .btn-submit[data-v-541c25f6] {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}
.feedback-modal .feedback-footer .btn-cancel .btn-text[data-v-541c25f6],
.feedback-modal .feedback-footer .btn-submit .btn-text[data-v-541c25f6] {
  font-size: 16px;
  font-weight: 500;
}
.feedback-modal .feedback-footer .btn-cancel[data-v-541c25f6] {
  background-color: #f5f5f5;
}
.feedback-modal .feedback-footer .btn-cancel .btn-text[data-v-541c25f6] {
  color: #666;
}
.feedback-modal .feedback-footer .btn-cancel[data-v-541c25f6]:hover {
  background-color: #ebebeb;
}
.feedback-modal .feedback-footer .btn-cancel[data-v-541c25f6]:active {
  background-color: #e0e0e0;
}
.feedback-modal .feedback-footer .btn-submit[data-v-541c25f6] {
  background-color: #007aff;
}
.feedback-modal .feedback-footer .btn-submit .btn-text[data-v-541c25f6] {
  color: #fff;
}
.feedback-modal .feedback-footer .btn-submit[data-v-541c25f6]:hover {
  background-color: #0056d6;
}
.feedback-modal .feedback-footer .btn-submit[data-v-541c25f6]:active {
  background-color: #004bb8;
}
.feedback-modal .feedback-footer .btn-submit.btn-disabled[data-v-541c25f6] {
  background-color: #ccc;
  cursor: not-allowed;
}
.feedback-modal .feedback-footer .btn-submit.btn-disabled[data-v-541c25f6]:hover, .feedback-modal .feedback-footer .btn-submit.btn-disabled[data-v-541c25f6]:active {
  background-color: #ccc;
}
@media (max-width: 768px) {
.feedback-modal[data-v-541c25f6] {
    width: 320px;
    max-width: 85vw;
}
.feedback-modal .feedback-header[data-v-541c25f6] {
    padding: 16px 20px 12px;
}
.feedback-modal .feedback-header .feedback-title[data-v-541c25f6] {
    font-size: 16px;
}
.feedback-modal .feedback-content[data-v-541c25f6] {
    padding: 15px 20px;
}
.feedback-modal .feedback-content .feedback-textarea[data-v-541c25f6] {
    min-height: 100px;
    padding: 10px;
    font-size: 14px;
}
.feedback-modal .feedback-footer[data-v-541c25f6] {
    padding: 0px 20px 20px;
    gap: 10px;
}
.feedback-modal .feedback-footer .btn-cancel[data-v-541c25f6],
.feedback-modal .feedback-footer .btn-submit[data-v-541c25f6] {
    height: 40px;
}
.feedback-modal .feedback-footer .btn-cancel .btn-text[data-v-541c25f6],
.feedback-modal .feedback-footer .btn-submit .btn-text[data-v-541c25f6] {
    font-size: 14px;
}
}

.xe-upload[data-v-d0fd8334] {
  display: none;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@keyframes voiceWave-fe64a9fc {
0%, 100% {
    transform: scaleY(0.6);
}
50% {
    transform: scaleY(1.2);
}
}
@keyframes spin-fe64a9fc {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes sound-wave-fe64a9fc {
0% {
    transform: scaleY(0.5);
}
50% {
    transform: scaleY(1);
}
100% {
    transform: scaleY(0.5);
}
}
@media (max-width: 768px) {
.chat-container .chat-input-container[data-v-fe64a9fc]::before {
    width: calc(100% - 33px) !important;
}
.chat-container .chat-input-container .input-wrapper[data-v-fe64a9fc] {
    margin: 0 !important;
}
}
.chat-container[data-v-fe64a9fc] {
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  top: 53px;
  /* 顶部导航栏的高度 */
  height: 100vh;
  background: #F8F9FA;
}
.chat-container .chat-header-container[data-v-fe64a9fc] {
  top: 40px;
}
.chat-container .chat-input-container[data-v-fe64a9fc] {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  padding: 0 15px 0 15px;
  z-index: 9;
  max-width: 750px;
  margin: 0 auto;
  padding-bottom: calc(10px + constant(safe-area-inset-bottom));
  padding-bottom: calc(10px + env(safe-area-inset-bottom));
}
.chat-container .chat-input-container[data-v-fe64a9fc]::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 30px;
  pointer-events: none;
  bottom: 100px;
  background: linear-gradient(180deg, rgba(248, 249, 250, 0) 0%, #f8f9fa 100%);
  z-index: -1;
  margin: 0 auto;
  max-width: 750px;
  width: calc(100% - 60px);
}
.chat-container .chat-input-container .uploaded-files-container[data-v-fe64a9fc] {
  margin-bottom: 2px;
  overflow: visible;
  padding: 5px 15px;
}
.chat-container .chat-input-container .uploaded-files-container .file-list-scroll[data-v-fe64a9fc] {
  width: 100%;
  white-space: nowrap;
}
.chat-container .chat-input-container .uploaded-files-container .file-list[data-v-fe64a9fc] {
  display: inline-flex;
  align-items: center;
  padding: 4px 0;
  max-height: 40px;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .img-item[data-v-fe64a9fc] {
  position: relative;
  margin-right: 10px;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .img-item .file-image[data-v-fe64a9fc] {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-size: 100% 100%;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-item[data-v-fe64a9fc] {
  position: relative;
  display: flex;
  align-items: center;
  margin-right: 10px;
  max-width: 160px;
  background-color: #ffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  max-height: 40px;
  min-height: 40px;
  padding: 0 10px;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-item .file-thumbnail[data-v-fe64a9fc] {
  width: 20px;
  height: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-item .file-thumbnail .file-image[data-v-fe64a9fc] {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-item .file-thumbnail .file-type-icon[data-v-fe64a9fc] {
  width: 28px;
  height: 28px;
  object-fit: contain;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-item .file-name[data-v-fe64a9fc] {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #3e4551;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-delete[data-v-fe64a9fc] {
  position: absolute;
  top: -2px;
  right: -4px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 2;
}
.chat-container .chat-input-container .uploaded-files-container .file-list .file-delete .delete-icon[data-v-fe64a9fc] {
  font-size: 12px;
  line-height: 1;
}
.chat-container .chat-input-container .input-wrapper[data-v-fe64a9fc] {
  background-color: #ffffff;
  padding: 8px 12px;
  box-shadow: 0px 2px 12px 0px rgba(68, 73, 77, 0.13);
  border-radius: 10px;
  border: 1px solid var(---normal, #e2e4e9);
  margin: 0 15px;
  /* PC端专用样式 */
  /* 自定义滚动条样式 */
}
.chat-container .chat-input-container .input-wrapper .chat-input[data-v-fe64a9fc] {
  height: auto;
  min-height: 44px;
  max-height: 260px;
  font-size: 14px;
  overflow-y: auto;
  /* 添加垂直滚动条 */
  padding-right: 5px;
  /* 为滚动条留出空间 */
  width: 100%;
}
.chat-container .chat-input-container .input-wrapper .pc-input[data-v-fe64a9fc] {
  padding: 0;
}
.chat-container .chat-input-container .input-wrapper .chat-input[data-v-fe64a9fc]::-webkit-scrollbar {
  width: 2px;
}
.chat-container .chat-input-container .input-wrapper .chat-input[data-v-fe64a9fc]::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}
.chat-container .chat-input-container .input-wrapper .chat-input[data-v-fe64a9fc]::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 2px;
}
.chat-container .chat-input-container .input-wrapper .chat-input[data-v-fe64a9fc]::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.chat-container .chat-input-container .input-wrapper .input-tools[data-v-fe64a9fc] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-left[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-left .clear-input-button[data-v-fe64a9fc] {
  width: 30px;
  height: 30px;
  border-radius: 10px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-left .clear-input-button .clear-icon[data-v-fe64a9fc] {
  width: 20px;
  height: 20px;
  background-size: 100% 100%;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-left .tool-item[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-left: 5px;
  background: var(---bg_hover, #f8f9fa);
  color: var(---, #adb1ba);
  border-radius: 10px;
  padding: 5px 8px 5px 5px;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-left .tool-item .tag-btn-icon[data-v-fe64a9fc] {
  width: 20px;
  height: 20px;
  background-size: 100% 100%;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-left .tool-item-active[data-v-fe64a9fc] {
  background: #eeeffc;
  color: var(---, #4d5bde);
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-right[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-right .file-btn-item[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-right .tool-button[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 10px;
  margin-left: 8px;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-right .tool-button.send-button[data-v-fe64a9fc] {
  background-color: #4d5bde;
  transition: all 0.3s;
}
.chat-container .chat-input-container .input-wrapper .input-tools .tool-right .tool-button.send-button.send-button-disabled[data-v-fe64a9fc] {
  background: var(---, #d0d1d6);
  cursor: not-allowed;
  opacity: 0.6;
}
.speech-item[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}
.speech-item.voice-tool[data-v-fe64a9fc] {
  width: 30px;
  height: 30px;
  border-radius: 10px;
  transition: all 0.3s;
  position: relative;
}
.speech-item.voice-tool uni-image[data-v-fe64a9fc] {
  height: 30px;
}
.speech-item.voice-tool.voice-active[data-v-fe64a9fc] {
  background-color: rgba(77, 91, 222, 0.1);
}
.speech-item.voice-tool .voice-wave-container[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 15px;
  height: 15px;
}
.speech-item.voice-tool .voice-wave-bar[data-v-fe64a9fc] {
  width: 2px;
  background-color: #4d5bde;
  border-radius: 2px;
  animation: voiceWave-fe64a9fc 1s infinite ease-in-out;
}
.speech-item.voice-tool .voice-wave-bar[data-v-fe64a9fc]:nth-child(1) {
  height: 8px;
  animation-delay: 0s;
}
.speech-item.voice-tool .voice-wave-bar[data-v-fe64a9fc]:nth-child(2) {
  height: 16px;
  animation-delay: 0.2s;
}
.speech-item.voice-tool .voice-wave-bar[data-v-fe64a9fc]:nth-child(3) {
  height: 10px;
  animation-delay: 0.4s;
}
.speech-item.voice-tool .voice-wave-bar[data-v-fe64a9fc]:nth-child(4) {
  height: 14px;
  animation-delay: 0.6s;
}
.speech-item.voice-tool .voice-tooltip[data-v-fe64a9fc] {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}
.speech-item.voice-tool .voice-tooltip[data-v-fe64a9fc]:after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.7);
}
.message-image-container[data-v-fe64a9fc] {
  max-width: 100px;
  padding: 0 5px;
}
.message-image-container .message-image[data-v-fe64a9fc] {
  height: 100px;
  width: 100px;
  border-radius: 10px;
  background-size: 100% 100%;
}
.message-image-container .image-filename[data-v-fe64a9fc] {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-message .message-file-container[data-v-fe64a9fc] {
  padding: 8px;
  border-radius: 8px;
  background-color: #ffffff;
}
.user-message .message-file-container .file-info[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
}
.user-message .message-file-container .file-info .file-type-icon[data-v-fe64a9fc] {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  flex-shrink: 0;
}
.user-message .message-file-container .file-info .file-details[data-v-fe64a9fc] {
  flex: 1;
  overflow: hidden;
}
.user-message .message-file-container .file-info .file-details .file-name[data-v-fe64a9fc] {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-message .message-file-container .file-info .file-details .file-size[data-v-fe64a9fc] {
  font-size: 12px;
  color: #999;
}
.user-message .file-deleted-message .file-info[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
}
.user-message .file-deleted-message .file-info .file-type-icon[data-v-fe64a9fc] {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  flex-shrink: 0;
}
.assistant-message .message-file-container[data-v-fe64a9fc] {
  padding: 6px;
}
.assistant-message .message-file-container .file-info[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
}
.assistant-message .message-file-container .file-info .file-type-icon[data-v-fe64a9fc] {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.assistant-message .message-file-container .file-info .file-details[data-v-fe64a9fc] {
  flex: 1;
  overflow: hidden;
}
.assistant-message .message-file-container .file-info .file-details .file-name[data-v-fe64a9fc] {
  font-size: 12px;
  font-weight: 400;
  color: #787d86;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.assistant-message .message-file-container .file-info .file-details .file-size[data-v-fe64a9fc] {
  font-size: 12px;
  color: #999;
}
.empty-content[data-v-fe64a9fc] {
  min-height: 20px;
  padding: 5px 0;
  display: block;
}
.input-btn-icon[data-v-fe64a9fc] {
  height: 30px;
  width: 30px;
}
.tool-container[data-v-fe64a9fc] {
  margin-bottom: 10px;
  border-radius: 10px;
  border: solid 1px #e2e4e9;
  max-width: 90%;
}
.tool-container.expanded[data-v-fe64a9fc] {
  padding: 10px;
}
.tool-container.expanded .Title-status[data-v-fe64a9fc] {
  margin: -10px -10px 0 -10px;
}
.tool-container .processing-status[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  height: 40px;
  box-sizing: border-box;
}
.tool-container .processing-status .status-text[data-v-fe64a9fc] {
  font-size: 14px;
  color: #3e4551;
  font-weight: 400;
  margin-right: 5px;
}
.tool-container .Title-status[data-v-fe64a9fc] {
  border-bottom: 1px solid var(---normal, #e2e4e9);
}
.tool-container .search-result-item[data-v-fe64a9fc] {
  padding: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tool-container .search-result-item .search-result-title[data-v-fe64a9fc] {
  overflow: hidden;
  /* 超出内容隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}
.tool-container .search-result-item .search-link[data-v-fe64a9fc] {
  color: #787d86;
  white-space: nowrap;
  /* 防止文本换行 */
  overflow: hidden;
  /* 超出内容隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  text-decoration: underline;
}
.tool-container .search-result-item .search-title-text[data-v-fe64a9fc] {
  color: #333333;
  font-weight: 600;
  white-space: nowrap;
  /* 防止文本换行 */
  overflow: hidden;
  /* 超出内容隐藏 */
}
.tool-container .search-result-item .search-result-date[data-v-fe64a9fc] {
  color: #999999;
  font-size: 12px;
  min-width: 65px;
  max-width: 80px;
}
.conversation-time[data-v-fe64a9fc] {
  width: 100%;
  font-size: 12px;
  font-weight: 400;
  color: #adb1ba;
  text-align: center;
  margin-top: 4px;
}
.clear-context-msg[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;
  color: #adb1ba;
  text-align: center;
  margin-top: 4px;
  width: 100%;
}
.clear-context-msg[data-v-fe64a9fc]::before, .clear-context-msg[data-v-fe64a9fc]::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #e2e4e9;
  margin: 0 10px;
  /* 线条与文字的间距 */
}
.processing-text-container[data-v-fe64a9fc] {
  border: solid 1px #e2e4e9;
  border-radius: 10px;
  padding: 8px 12px;
}
.processing-text-container .processing-text[data-v-fe64a9fc] {
  font-size: 14px;
  color: #666;
  text-align: center;
}
.chat-container .chat-scroll-container .tool-call-container[data-v-fe64a9fc] {
  width: 100%;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-header[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  padding: 5px;
  cursor: pointer;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-header .status-icon[data-v-fe64a9fc] {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-header .status-icon.loading[data-v-fe64a9fc] {
  width: 16px;
  height: 16px;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-header .status-icon.loading .tool-loading[data-v-fe64a9fc] {
  transform: scale(0.8);
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-header .status-icon.completed[data-v-fe64a9fc] {
  width: 16px;
  height: 16px;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-header .tool-info[data-v-fe64a9fc] {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #3e4551;
  font-family: PingFang SC;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content[data-v-fe64a9fc] {
  max-height: 520px;
  margin-left: 24px;
  color: #787d86;
  overflow: auto;
  scroll-behavior: smooth;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .tool-args[data-v-fe64a9fc],
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .tool-result[data-v-fe64a9fc] {
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .args-title-1[data-v-fe64a9fc] {
  padding: 0 5px;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .args-title-2[data-v-fe64a9fc] {
  padding: 0 5px;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .tool-content-title[data-v-fe64a9fc] {
  font-weight: 400;
  color: #787d86;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .tool-content-text[data-v-fe64a9fc] {
  font-weight: 400;
  margin-bottom: 8px;
  color: #787d86;
}
.chat-container .chat-scroll-container .tool-call-container .tool-call-content .tool-content-result[data-v-fe64a9fc] {
  color: #787d86;
}
.chat-container .chat-scroll-container .message-list[data-v-fe64a9fc] {
  padding: 10px 18px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}
.chat-container .chat-scroll-container .message-list .message-item[data-v-fe64a9fc] {
  margin-bottom: 10px;
}
.chat-container .chat-scroll-container .message-list .message-item.user-message .message-content[data-v-fe64a9fc] {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.chat-container .chat-scroll-container .message-list .message-item.user-message .message-content .message-bubble[data-v-fe64a9fc] {
  min-width: 60px;
  font-weight: 400;
  font-size: 15px;
  color: rgba(0, 0, 0, 0.85);
  text-align: justify;
}
.chat-container .chat-scroll-container .message-list .message-item.user-message .message-content .message-bubble .message-text[data-v-fe64a9fc] {
  background-color: #a4e17b;
  padding: 10px;
  border-radius: 10px 0 10px 10px;
  width: auto;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  user-select: text;
  /* 允许文本选择 */
  -webkit-user-select: text;
  /* 针对 Safari */
  -moz-user-select: text;
  /* 针对 Firefox */
  -ms-user-select: text;
  /* 针对 IE 和 Edge */
}
.chat-container .chat-scroll-container .message-list .message-item.user-message .message-content .message-bubble .file-deleted-message[data-v-fe64a9fc] {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 10px 0 10px 10px;
}
.chat-container .chat-scroll-container .message-list .message-item.user-message .message-actions[data-v-fe64a9fc] {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-left: 4px;
  height: 20px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}
.chat-container .chat-scroll-container .message-list .message-item.user-message .message-actions.actions-visible[data-v-fe64a9fc] {
  opacity: 1;
  visibility: visible;
}
.chat-container .chat-scroll-container .message-list .message-item.assistant-message .message-content[data-v-fe64a9fc] {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.chat-container .chat-scroll-container .message-list .message-item.assistant-message .message-content .message-bubble[data-v-fe64a9fc] {
  max-width: 95%;
  width: auto;
  background-color: #ffffff;
  padding: 0 10px;
  font-size: 15px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
  border-radius: 0 10px 10px 10px;
}