/**
 * 全局状态管理
 * 用于跟踪页面间的导航状态
 */

// 全局变量：是否通过链接点击进行导航
let isFromLinkTap = false;

/**
 * 设置链接点击状态
 * @param {boolean} status - 是否来自链接点击
 */
export const setLinkTapStatus = (status) => {
  isFromLinkTap = status;
  // console.log('🔗 设置链接点击状态:', status);
};

/**
 * 获取链接点击状态
 * @returns {boolean} 是否来自链接点击
 */
export const getLinkTapStatus = () => {
  return isFromLinkTap;
};

/**
 * 清除链接点击状态
 */
export const clearLinkTapStatus = () => {
  isFromLinkTap = false;
  // console.log('🗑️ 清除链接点击状态');
};