<template>
  <!-- 【新建】商机-->
  <ActionSheet
    v-model:show="showForm"
    :custom-panel="true"
    :close-on-click-mask="false"
    @cancel="handleCancel"
  >
    <template #custom-panel>
      <!-- 顶部操作栏 -->
      <view class="form-header">
        <view class="form-header-btn" @click="handleCancel">取消</view>
        <view class="form-header-title">{{ title }}</view>
        <!-- :class="{ 'confirm-btn--disabled': isSubmitting }" -->
        <view class="form-header-btn confirm-btn" @click="handleSubmit">
          {{ confirmText }}
        </view>
      </view>
      <!-- 表单内容 -->
      <scroll-view scroll-y class="form-content">
        <uni-forms
          ref="formRef"
          class="task-form"
          :model-value="formData"
          :rules="rules"
          validate-trigger="submit"
        >
          <uni-forms-item label="标题" name="title" required>
            <uni-easyinput
              v-model="formData.title"
              placeholder="请输入商机标题"
              maxlength="50"
            />
          </uni-forms-item>
          <uni-forms-item label="客户名称" name="customer_id" required>
            <uni-data-select
              v-model="formData.customer_id"
              :localdata="customerList"
              placeholder="请选择客户名称"
              maxlength="50"
            />
          </uni-forms-item>
          <uni-forms-item label="当前阶段" name="status" required>
            <uni-data-select
              v-model="formData.status"
              :localdata="stages"
              placeholder="请选择当前阶段"
            />
          </uni-forms-item>
          <uni-forms-item label="需求内容" name="content">
            <uni-easyinput
              v-model="formData.content"
              placeholder="请输入客户需求内容"
              type="textarea"
              maxlength="150"
            />
          </uni-forms-item>
          <uni-forms-item label="预期成交" name="budget">
            <uni-easyinput
              v-model="formattedBudget"
              type="text"
              placeholder="请输入预期成交"
              maxlength="50"
              @input="handleBudgetInput"
            />
          </uni-forms-item>
          <uni-forms-item label="商机来源" name="source">
            <uni-easyinput
              v-model="formData.source"
              placeholder="请输入商机来源"
              maxlength="150"
            />
          </uni-forms-item>
          <uni-forms-item label="商机备注" name="notes">
            <uni-easyinput
              v-model="formData.notes"
              placeholder="请输入备注信息"
              type="textarea"
              maxlength="150"
            />
          </uni-forms-item>
        </uni-forms>
      </scroll-view>
    </template>
  </ActionSheet>
</template>
    
<script setup>
import { ref, reactive, watch, onMounted, computed } from "vue";
import { getCustomerList } from "@/http/customer.js";
import { fetchUserConfig } from "@/http/user.js"; // 引入配置接口
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
const emit = defineEmits(["update:show", "submit", "cancel"]);
const props = defineProps({
  // 是否显示
  show: Boolean,
  // 标题
  title: {
    type: String,
    default: "新建商机",
  },
  // 确认按钮文字
  confirmText: {
    type: String,
    default: "确定",
  },
  // 初始表单数据
  initialData: {
    type: Object,
    default: () => ({}),
  },
});


const showForm = ref(props.show);
const isSubmitting = ref(false);
const formRef = ref(null);
// 商机来源选项
const customerList = ref([]);
// 商机阶段选项 - 改为动态获取
const stages = ref([]);

// 表单数据
const formData = reactive({
  title: "",
  customer_id: "",
  content: "",
  status: "",
  budget: null,
  source: "",
  notes: "",
  ...props.initialData,
});

// 验证规则
const rules = reactive({
  title: {
    rules: [{ required: true, errorMessage: "请输入商机标题" }],
  },
  customer_id: {
    rules: [{ required: true, errorMessage: "请输入客户名称" }],
  },
  // content: {
  //   rules: [{ required: true, errorMessage: "请输入需求内容" }],
  // },
  status: {
    rules: [{ required: true, errorMessage: "请选择当前阶段" }],
  },
});

// 格式化预期成交金额（添加千位符）
const formattedBudget = computed({
  get: () => {
    if (!formData.budget) return '';
    // 转为整数并添加千位符
    return parseInt(formData.budget).toLocaleString('zh-CN');
  },
  set: (val) => {
    // 移除非数字字符
    const numericValue = val.replace(/[^\d]/g, '');
    formData.budget = numericValue || null;
  }
});

// 处理输入事件
const handleBudgetInput = (e) => {
  const value = e;
  // 移除非数字字符
  const numericValue = value.replace(/[^\d]/g, '');
  formData.budget = numericValue;
};

// 提交表单
// 修改提交表单方法，确保budget是数字类型
const handleSubmit = async () => {
  if (isSubmitting.value) return;
  isSubmitting.value = true;
  try {
    // 验证表单
    const validate = await formRef.value.validate();
    // 确保budget是数字类型
    const submitData = { ...formData };
    if (submitData.budget) {
      submitData.budget = parseInt(submitData.budget);
    }
    // 验证通过，提交数据
    emit("submit", submitData);
    showForm.value = false;
  } catch (err) {
    console.log("表单验证失败:", err);
  } finally {
    isSubmitting.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit("cancel");
  showForm.value = false;
};

// 初始化客户列表
const queryCustomerList = async () => {
  try {
    const res = await getCustomerList({
      page: 1,
      limit: 300,
    });
    console.log("初始化客户列表:", res);
    if (res.code === 0 && res.data.list.length) {
      customerList.value = res.data?.list.map((item) => ({
        text: item.company_short_name, // 显示的文本
        value: item.customer_id, // 对应的值
      }));
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};

// 获取商机阶段配置
const getBusinessStages = async () => {
  try {
    const res = await fetchUserConfig();
    console.log("获取商机阶段配置:", res);
    if (res.code === 0 && res.data && res.data.business_opportunities) {
      const statusMap = res.data.business_opportunities.status_map || {};
      // 将接口返回的状态映射转换为下拉选项格式
      stages.value = Object.entries(statusMap).map(([value, text]) => ({
        value: parseInt(value), // 确保值是数字类型
        text
      }));
    }
  } catch (err) {
    console.error("获取商机阶段配置失败:", err);
  }
};

// 重置表单
const resetForm = () => {
  formRef.value?.clearValidate();
  Object.assign(formData, {
    title: "",
    customer_id: "",
    content: "",
    status: "",
    budget: null,
    source: "",
    notes: "",
  });
};

// 暴露方法
defineExpose({
  show: () => {
    showForm.value = true;
  },
  close: () => {
    showForm.value = false;
  },
  resetForm,
});

// 监听show变化
watch(
  () => props.show,
  (val) => {
    showForm.value = val;
  }
);

// 监听组件内部show变化
watch(showForm, (val) => {
  emit("update:show", val);
  if (!val) {
    resetForm();
  }
});

onMounted(() => {
  // 查询客户列表
  queryCustomerList();
  // 获取商机阶段配置
  getBusinessStages();
});
</script>
    
  <style scoped lang="scss">
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
}

.form-header-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.form-header-btn {
  font-size: 14px;
  color: #666;
  padding: 5px 10px;
}

.confirm-btn {
  color: #007aff;

  &--disabled {
    opacity: 0.5;
  }
}

.form-content {
  max-height: 80vh;
  .task-form {
    padding: 10px 15px;
    :deep(.uni-forms-item__label) {
      width: 80px !important;
      height: 35px;
      color: var(---, #787d86);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 35px;
      font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
    }
  }
}
</style>
  <!-- :deep(.is-input-border) {
        border: unset;
  }
  :deep(.uni-easyinput__content-input) {
      padding-left: 0 !important;
  } -->