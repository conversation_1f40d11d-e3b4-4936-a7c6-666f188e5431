import env from "./env";

// 统一错误提示
function _showError(msg) {
    uni.showToast({
      icon: "none",
      duration: 3000,
      title: msg,
    });
  }

// 封装请求
function service(options = {}) {
  // 处理不同环境的URL
  const isH5Dev = process.env.NODE_ENV === 'development' && process.env.UNI_PLATFORM === 'h5';
  options.url = `${env.baseURL}${options.url}`;
  // 添加认证头获取 token
  const token = uni.getStorageSync("token");
  // 处理请求头
  options.header = {
    "content-type": "application/json",
    // 添加 accept 头
    "accept": "application/json",
    ...(token ? { Authorization: `${token}` } : {}),
    ...options.header
  };
  // 设置uni.request的timeout参数
  options.timeout = options.timeout;
  // console.error('全局token:-----------', token);
  return new Promise((resolved, rejected) => {
    options.success = (res) => {
      if (res.statusCode === 200) {
        // 增加对返回 code 的判断
        if (res.data.code === 1) {
          _showError(res.data?.msg || "参数错误");
          // return;
        }
        if (res.data.code === 401) {
          _showError(res.data?.msg || "请先登录");
          uni.clearStorage();
          uni.reLaunch({
            url: '/pages/ai-chat'
          });
          // rejected(res.data);
          return;
        }
        resolved(res.data);
      } else if (res.statusCode === 500) {
        console.error('服务器错误:', res);
        _showError("服务器内部错误，请稍后再试");
        rejected(res);
      } else {
        console.error('请求失败:', res);
        _showError(res.data?.msg || `请求失败: ${res.statusCode}`);
        rejected(res);
      }
    };

    options.fail = (err) => {
      console.error('网络请求失败:', err);
      // #ifdef APP-PLUS
      // App端特殊处理网络错误
      if (err.errMsg && err.errMsg.includes('request:fail')) {
        if (err.errMsg.includes('timeout')) {
          _showError(`请求超时（${timeoutDuration/1000}秒），请检查网络连接`);
        } else if (err.errMsg.includes('url not in domain list')) {
          _showError("网络域名未配置，请联系管理员");
        } else {
          _showError("网络连接失败，请检查网络设置");
        }
      } else {
        _showError("网络连接失败，请检查网络");
      }
      // #endif
      // #ifndef APP-PLUS
      if (err.errMsg && err.errMsg.includes('timeout')) {
        _showError(`请求超时（${timeoutDuration/1000}秒），请检查网络连接`);
      } else {
        _showError("网络连接失败，请检查网络");
      }
      // #endif
      rejected(err);
    };

    // 移除手动计时器，使用uni.request自带的timeout
    uni.request(options);
  });
}

export default service;