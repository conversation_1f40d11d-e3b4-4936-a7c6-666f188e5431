var __renderjsModules={};

__renderjsModules["79031e87"] = (() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
  var __async = (__this, __arguments, generator) => {
    return new Promise((resolve, reject) => {
      var fulfilled = (value) => {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      };
      var rejected = (value) => {
        try {
          step(generator.throw(value));
        } catch (e) {
          reject(e);
        }
      };
      var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
      step((generator = generator.apply(__this, __arguments)).next());
    });
  };

  // <stdin>
  var stdin_exports = {};
  __export(stdin_exports, {
    default: () => stdin_default
  });
  var stdin_default = {
    data() {
      return {
        funnelChart: null,
        isVChartLoaded: false,
        // 定义颜色映射
        stageColorMap: {
          "\u521D\u6B21\u63A5\u89E6": "#5387EE",
          "\u9700\u6C42\u786E\u8BA4": "#48C5DB",
          "\u7ADE\u54C1\u5206\u6790": "#FF8B2C",
          "\u5546\u52A1\u8C08\u5224": "#FFC60A",
          "\u8D62\u5355\u5173\u95ED": "#7BC335"
        }
      };
    },
    methods: {
      /**
       * 动态加载VChart库
       */
      loadVChart() {
        return new Promise((resolve, reject) => {
          var _a;
          if (this.isVChartLoaded || window.VChart || ((_a = window.VChart) == null ? void 0 : _a.VChart)) {
            this.isVChartLoaded = true;
            resolve();
            return;
          }
          const script = document.createElement("script");
          script.src = "static/js/vchart.min.js";
          script.onload = () => {
            console.log("VChart\u5E93\u52A0\u8F7D\u6210\u529F");
            this.isVChartLoaded = true;
            resolve();
          };
          script.onerror = () => {
            console.error("VChart\u5E93\u52A0\u8F7D\u5931\u8D25");
            reject(new Error("VChart\u5E93\u52A0\u8F7D\u5931\u8D25"));
          };
          document.head.appendChild(script);
        });
      },
      /**
       * 初始化漏斗图
       * @param {Object} funnelData - 漏斗图数据
       * @param {Object} funnelChartData - 完整的图表数据
       */
      initChart(funnelData, funnelChartData) {
        return __async(this, null, function* () {
          var _a, _b;
          try {
            if (!this.isVChartLoaded) {
              yield this.loadVChart();
            }
            if (!window.VChart && !((_a = window.VChart) == null ? void 0 : _a.VChart)) {
              console.error("VChart\u5E93\u672A\u6B63\u786E\u52A0\u8F7D");
              return;
            }
            const VChart = ((_b = window.VChart) == null ? void 0 : _b.VChart) || window.VChart;
            if (!VChart) {
              console.error("\u65E0\u6CD5\u83B7\u53D6VChart\u6784\u9020\u51FD\u6570");
              return;
            }
            const chartDom = document.getElementById("funnel-chart");
            if (!chartDom) {
              console.error("\u56FE\u8868\u5BB9\u5668\u672A\u627E\u5230");
              return;
            }
            if (this.funnelChart) {
              try {
                this.funnelChart.release();
              } catch (error) {
                console.warn("\u56FE\u8868\u9500\u6BC1\u5931\u8D25:", error);
              } finally {
                this.funnelChart = null;
              }
            }
            this.createChart(chartDom, funnelData, funnelChartData);
          } catch (error) {
            console.error("\u521D\u59CB\u5316\u56FE\u8868\u5931\u8D25:", error);
          }
        });
      },
      /**
       * 创建图表实例
       * @param {Element} chartDom - 图表容器 DOM 元素
       * @param {Object} funnelData - 漏斗图数据
       * @param {Object} funnelChartData - 完整的图表数据
       */
      createChart(chartDom, funnelData, funnelChartData) {
        var _a;
        let chartValues = [];
        if (funnelData && typeof funnelData === "object") {
          const filteredData = {};
          Object.keys(funnelData).forEach((key) => {
            if (funnelData[key] !== 0) {
              filteredData[key] = funnelData[key];
            }
          });
          chartValues = Object.entries(filteredData).map(([stageName, count]) => {
            return {
              value: count,
              name: stageName
            };
          }).filter((item) => item !== null);
        } else {
          chartValues = [
            { value: 0, name: "\u521D\u6B21\u63A5\u89E6" },
            { value: 0, name: "\u9700\u6C42\u786E\u8BA4" },
            { value: 0, name: "\u7ADE\u54C1\u5206\u6790" },
            { value: 0, name: "\u5546\u52A1\u8C08\u5224" },
            { value: 0, name: "\u8D62\u5355\u5173\u95ED" }
          ];
        }
        const spec = {
          type: "funnel",
          maxSize: "85%",
          minSize: "15%",
          isTransform: true,
          shape: "rect",
          color: {
            type: "ordinal",
            range: ["#5387EE", "#48C5DB", "#FF8B2C", "#FFC60A", "#7BC335"]
          },
          // 简化的tooltip配置
          tooltip: {
            visible: true,
            renderMode: "html",
            mark: {
              title: {
                visible: false
              },
              content: (datum, data) => {
                return [{
                  key: `${data.datum.name}: ${data.datum.value}`,
                  value: ""
                }];
              }
            },
            style: {
              panel: {
                padding: 10,
                backgroundColor: "#ffffff",
                border: {
                  color: "#e2e4e9",
                  width: 1,
                  radius: 6
                },
                shadow: {
                  x: 0,
                  y: 2,
                  blur: 8,
                  spread: 0,
                  color: "rgba(0, 0, 0, 0.1)"
                },
                maxWidth: 220,
                minWidth: 160
              },
              keyLabel: {
                fontSize: 12,
                fill: "#3e4551",
                fontWeight: "400",
                multiLine: true,
                maxWidth: 200,
                "spacing": 0
              },
              valueLabel: {
                fontSize: 12,
                fill: "#000000",
                fontWeight: "400",
                maxWidth: 100,
                multiLine: true
              }
            }
          },
          funnel: {
            style: {
              cornerRadius: 4,
              stroke: "white",
              lineWidth: 2
            },
            state: {
              hover: {
                stroke: "#4e83fd",
                lineWidth: 1
              }
            }
          },
          title: {
            text: this.generateTitle(funnelChartData),
            orient: "top",
            style: {
              textAlign: "center",
              fontSize: 12
            },
            textStyle: {
              fontSize: 12,
              align: "center"
            },
            subtextStyle: {
              fontSize: 12,
              align: "center"
            }
          },
          totalLabel: {
            visible: true,
            style: {
              fontSize: 12,
              textBaseline: "middle",
              textAlign: "center"
            }
          },
          label: {
            visible: true,
            style: {
              fill: "black",
              lineHeight: 16,
              fontSize: 12,
              limit: Infinity,
              text: (datum) => [`${datum.name}`, `${datum.value}`]
            }
          },
          // transformLabel: {
          //   visible: true,
          //   style: {
          //     fontSize: 10,
          //     fill: "black",
          //   },
          // },
          transform: {
            style: {
              stroke: "white",
              lineWidth: 2
            },
            state: {
              hover: {
                stroke: "#4e83fd",
                lineWidth: 1
              }
            }
          },
          data: [
            {
              name: "funnel",
              values: chartValues
            }
          ],
          categoryField: "name",
          valueField: "value"
        };
        const VChart = ((_a = window.VChart) == null ? void 0 : _a.VChart) || window.VChart;
        if (!VChart) {
          console.error("\u65E0\u6CD5\u83B7\u53D6VChart\u6784\u9020\u51FD\u6570");
          return;
        }
        this.funnelChart = new VChart(spec, { dom: chartDom, supportsTouchEvents: true });
        this.funnelChart.renderSync();
        return this.funnelChart;
      },
      /**
       * 动态生成 title 文本
       * @param {Object} data - 图表数据
       * @returns {string} - 标题文本
       */
      generateTitle(data) {
        if (!data)
          return "";
        let titleText = "";
        if (data.business_total_trans_rate !== 0) {
          titleText += `\u603B\u8F6C\u6362\u7387 ${data.business_total_trans_rate}%  `;
        }
        if (data.business_lost_close_rate !== 0) {
          titleText += ` \u4E22\u5355\u7387 ${data.business_lost_close_rate}%`;
        }
        return titleText;
      },
      /**
       * 接收数据并更新图表
       * @param {Object} data - 传入的数据
       */
      receiveData(data) {
        console.log("renderjs\u63A5\u6536\u5230\u6570\u636E----------:", data);
        if (!data || !data.funnelData) {
          console.warn("\u63A5\u6536\u5230\u7684\u6570\u636E\u65E0\u6548\u6216\u7F3A\u5C11funnelData");
          return;
        }
        this.initChart(data.funnelData, data.funnelChartData);
      },
      /**
       * 销毁图表
       */
      destroyChart() {
        if (this.funnelChart) {
          try {
            this.funnelChart.release();
          } catch (error) {
            console.warn("\u56FE\u8868\u9500\u6BC1\u5931\u8D25:", error);
          } finally {
            this.funnelChart = null;
          }
        }
      }
    },
    // mounted() {
    //   // console.log('renderjs模块已挂载');
    //   // 预加载VChart库
    // },
    beforeDestroy() {
      this.destroyChart();
    }
  };
  return __toCommonJS(stdin_exports);
})();


__renderjsModules["588e4334"] = (() => {
  var __defProp = Object.defineProperty;
  var __defProps = Object.defineProperties;
  var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getOwnPropSymbols = Object.getOwnPropertySymbols;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __propIsEnum = Object.prototype.propertyIsEnumerable;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
      if (__hasOwnProp.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
      for (var prop of __getOwnPropSymbols(b)) {
        if (__propIsEnum.call(b, prop))
          __defNormalProp(a, prop, b[prop]);
      }
    return a;
  };
  var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __async = (__this, __arguments, generator) => {
    return new Promise((resolve, reject) => {
      var fulfilled = (value) => {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      };
      var rejected = (value) => {
        try {
          step(generator.throw(value));
        } catch (e) {
          reject(e);
        }
      };
      var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
      step((generator = generator.apply(__this, __arguments)).next());
    });
  };

  // <stdin>
  var require_stdin = __commonJS({
    "<stdin>"(exports, module) {
      var PCMAudioPlayer = class {
        constructor(sampleRate = 24e3) {
          this.sampleRate = sampleRate;
          this.audioContext = null;
          this.audioQueue = [];
          this.isPlaying = false;
          this.currentSource = null;
          this.onPlaybackEnd = null;
        }
        connect() {
          if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
          }
        }
        pushPCM(arrayBuffer) {
          this.audioQueue.push(arrayBuffer);
          this._playNextAudio();
        }
        _bufferPCMData(pcmData) {
          const sampleRate = this.sampleRate;
          const length = pcmData.byteLength / 2;
          const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);
          const channelData = audioBuffer.getChannelData(0);
          const int16Array = new Int16Array(pcmData);
          for (let i = 0; i < length; i++) {
            channelData[i] = int16Array[i] / 32768;
          }
          return audioBuffer;
        }
        _playAudio(arrayBuffer) {
          return __async(this, null, function* () {
            if (this.audioContext.state === "suspended") {
              yield this.audioContext.resume();
            }
            const audioBuffer = this._bufferPCMData(arrayBuffer);
            this.currentSource = this.audioContext.createBufferSource();
            this.currentSource.buffer = audioBuffer;
            this.currentSource.connect(this.audioContext.destination);
            this.currentSource.onended = () => {
              this.isPlaying = false;
              this.currentSource = null;
              this._playNextAudio();
              if (this.audioQueue.length === 0 && this.onPlaybackEnd) {
                this.onPlaybackEnd();
              }
            };
            this.currentSource.start(0);
            this.isPlaying = true;
          });
        }
        _playNextAudio() {
          if (this.audioQueue.length > 0 && !this.isPlaying) {
            const totalLength = this.audioQueue.reduce((acc, buffer) => acc + buffer.byteLength, 0);
            const combinedBuffer = new Uint8Array(totalLength);
            let offset = 0;
            for (const buffer of this.audioQueue) {
              combinedBuffer.set(new Uint8Array(buffer), offset);
              offset += buffer.byteLength;
            }
            this.audioQueue = [];
            this._playAudio(combinedBuffer.buffer);
          }
        }
        stop() {
          if (this.currentSource) {
            this.currentSource.stop();
            this.currentSource = null;
          }
          this.isPlaying = false;
          this.audioQueue = [];
        }
        setOnPlaybackEndCallback(callback) {
          this.onPlaybackEnd = callback;
        }
      };
      var TTS_CONFIG = {
        appkey: "ZsmxFv9inn9RVE3N",
        url: "wss://nls-gateway-cn-shenzhen.aliyuncs.com/ws/v1",
        voice: "zhixiaoxia",
        format: "PCM",
        sample_rate: 24e3,
        volume: 50,
        speech_rate: 100,
        pitch_rate: 50,
        namespace: "FlowingSpeechSynthesizer"
      };
      function generateUUID() {
        let d = (/* @__PURE__ */ new Date()).getTime();
        let d2 = performance && performance.now && performance.now() * 1e3 || 0;
        return "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx".replace(/[xy]/g, function(c) {
          let r = Math.random() * 16;
          if (d > 0) {
            r = (d + r) % 16 | 0;
            d = Math.floor(d / 16);
          } else {
            r = (d2 + r) % 16 | 0;
            d2 = Math.floor(d2 / 16);
          }
          return (c === "x" ? r : r & 3 | 8).toString(16);
        });
      }
      var getHeader = (config, task_id) => ({
        message_id: generateUUID(),
        task_id,
        namespace: config.namespace,
        name: "",
        appkey: config.appkey
      });
      module.exports = {
        data() {
          return {
            ttsServices: null,
            isInitialized: false
          };
        },
        methods: {
          /**
           * 处理来自逻辑层的动作
           */
          handleAction(newVal) {
            if (!newVal || !newVal.action)
              return;
            const { action, data } = newVal;
            console.log("renderjs \u63A5\u6536\u52A8\u4F5C:", action);
            switch (action) {
              case "initTtsService":
                this.initTtsService(data);
                break;
              case "playVoice":
                this.playVoice(data);
                break;
              case "stopVoice":
                this.stopVoice();
                break;
              case "pauseVoice":
                this.pauseVoice();
                break;
            }
          },
          /**
           * 初始化TTS服务
           */
          initTtsService(token) {
            try {
              if (!token) {
                console.error("Token\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u521D\u59CB\u5316TTS\u670D\u52A1");
                return;
              }
              this.ttsServices = this.createAliyunTts(__spreadProps(__spreadValues({}, TTS_CONFIG), { token }));
              this.isInitialized = true;
              console.log("TTS\u670D\u52A1\u521D\u59CB\u5316\u6210\u529F");
            } catch (error) {
              console.error("TTS\u670D\u52A1\u521D\u59CB\u5316\u5931\u8D25:", error);
            }
          },
          /**
           * 创建阿里云TTS服务
           */
          createAliyunTts(config) {
            let ws = null;
            let task_id = null;
            let isSynthesisStarted = false;
            let player = new PCMAudioPlayer(config.sample_rate);
            let currentPlayingText = "";
            const connectAndStartSynthesis = () => {
              return new Promise((resolve, reject) => {
                ws = new WebSocket(`${config.url}?token=${config.token}`);
                ws.binaryType = "arraybuffer";
                ws.onopen = () => {
                  task_id = generateUUID();
                  const header = getHeader(config, task_id);
                  const params = {
                    header: __spreadProps(__spreadValues({}, header), { name: "StartSynthesis" }),
                    payload: {
                      voice: config.voice,
                      format: config.format,
                      sample_rate: config.sample_rate,
                      volume: config.volume,
                      speech_rate: config.speech_rate,
                      pitch_rate: config.pitch_rate,
                      enable_subtitle: true,
                      platform: "javascript"
                    }
                  };
                  ws.send(JSON.stringify(params));
                };
                ws.onerror = (err) => {
                  console.error("WebSocket\u9519\u8BEF:", err);
                  reject(err);
                };
                ws.onmessage = (event) => {
                  const data = event.data;
                  if (data instanceof ArrayBuffer) {
                    player.pushPCM(data);
                  } else {
                    const body = JSON.parse(data);
                    if (body.header.name === "SynthesisStarted" && body.header.status === 2e7) {
                      isSynthesisStarted = true;
                      resolve();
                    }
                    if (body.header.name === "SynthesisCompleted" && body.header.status === 2e7) {
                      ws = null;
                      isSynthesisStarted = false;
                    }
                  }
                };
                player.connect();
              });
            };
            const sendRunSynthesis = (text) => __async(this, null, function* () {
              currentPlayingText = text;
              if (ws && isSynthesisStarted) {
                const header = getHeader(config, task_id);
                const params = {
                  header: __spreadProps(__spreadValues({}, header), { name: "RunSynthesis" }),
                  payload: { text }
                };
                ws.send(JSON.stringify(params));
              }
            });
            const sendStopSynthesis = () => __async(this, null, function* () {
              if (ws && isSynthesisStarted) {
                const header = getHeader(config, task_id);
                const params = {
                  header: __spreadProps(__spreadValues({}, header), { name: "StopSynthesis" })
                };
                ws.send(JSON.stringify(params));
              }
            });
            const stopAllPlayback = () => {
              if (ws) {
                ws.close();
                ws = null;
              }
              player.stop();
              currentPlayingText = "";
              isSynthesisStarted = false;
            };
            const setOnPlaybackEndCallback = (callback) => {
              player.setOnPlaybackEndCallback(() => {
                currentPlayingText = "";
                if (callback)
                  callback();
              });
            };
            return {
              connectAndStartSynthesis,
              sendRunSynthesis,
              sendStopSynthesis,
              setOnPlaybackEndCallback,
              stopAllPlayback
            };
          },
          /**
           * 播放语音
           */
          playVoice(text) {
            return __async(this, null, function* () {
              try {
                if (!this.isInitialized || !this.ttsServices) {
                  console.error("TTS\u670D\u52A1\u672A\u521D\u59CB\u5316");
                  return;
                }
                this.ttsServices.stopAllPlayback();
                this.ttsServices.setOnPlaybackEndCallback(() => {
                  console.log("\u8BED\u97F3\u64AD\u653E\u7ED3\u675F");
                });
                yield this.ttsServices.connectAndStartSynthesis();
                yield this.ttsServices.sendRunSynthesis(text);
                yield this.ttsServices.sendStopSynthesis();
              } catch (error) {
                console.error("\u64AD\u653E\u8BED\u97F3\u5931\u8D25:", error);
              }
            });
          },
          /**
           * 停止播放
           */
          stopVoice() {
            if (this.ttsServices) {
              this.ttsServices.stopAllPlayback();
            }
          },
          /**
           * 暂停播放
           */
          pauseVoice() {
            this.stopVoice();
          }
        }
      };
    }
  });
  return require_stdin();
})();


__renderjsModules["2651fd3c"] = (() => {
  var __defProp = Object.defineProperty;
  var __defProps = Object.defineProperties;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getOwnPropSymbols = Object.getOwnPropertySymbols;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __propIsEnum = Object.prototype.propertyIsEnumerable;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
      if (__hasOwnProp.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
      for (var prop of __getOwnPropSymbols(b)) {
        if (__propIsEnum.call(b, prop))
          __defNormalProp(a, prop, b[prop]);
      }
    return a;
  };
  var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
  var __async = (__this, __arguments, generator) => {
    return new Promise((resolve, reject) => {
      var fulfilled = (value) => {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      };
      var rejected = (value) => {
        try {
          step(generator.throw(value));
        } catch (e) {
          reject(e);
        }
      };
      var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
      step((generator = generator.apply(__this, __arguments)).next());
    });
  };

  // <stdin>
  var stdin_exports = {};
  __export(stdin_exports, {
    default: () => stdin_default
  });

  // C:/项目代码/sale-agent-web/uni_modules/xe-upload/tools/tools.js
  var awaitWrap = (promise) => promise.then((res) => [null, res]).catch((err) => [err, {}]);
  var fileToBase64 = (file) => {
    if (!file)
      return;
    return new Promise((r, j) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result;
        r(base64String);
      };
      reader.onerror = () => {
        j({ mode: "fileToBase64", data: { errMsg: "File to base64 fail." } });
      };
      reader.readAsDataURL(file);
    });
  };

  // C:/项目代码/sale-agent-web/uni_modules/xe-upload/tools/apis.js
  var appUploadFile = (config, exts = {}, onprogress) => {
    const { url, header, formData } = config;
    return new Promise((r, j) => {
      const xhr = new XMLHttpRequest();
      xhr.open("POST", url, true);
      for (let key in header) {
        xhr.setRequestHeader(key, header[key]);
      }
      if (onprogress) {
        xhr.upload.onprogress = onprogress;
      }
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            r(__spreadProps(__spreadValues({}, exts), { response: JSON.parse(xhr.responseText) }));
          } else {
            j({ mode: "uploadFile", data: { data: xhr.responseText, errMsg: "uploadFile fail." } });
          }
        }
      };
      xhr.send(formData);
    });
  };

  // <stdin>
  var stdin_default = {
    data() {
      return {
        id: 0,
        // 上传框ID
        uploadOptions: {}
        // 上传配置
      };
    },
    methods: {
      // 处理 XeUpload 传入 renderjs 数据，以及调起上传框
      renderProps(info) {
        const { id, renderInput, upload } = info;
        if (!renderInput)
          return;
        this.id = id;
        this.uploadOptions = upload;
        this.$nextTick(() => {
          var _a;
          const dom = document.getElementById(`xe-upload-${id}`);
          dom.addEventListener("change", () => {
            this.handleUpload();
          });
          (_a = dom == null ? void 0 : dom.click) == null ? void 0 : _a.call(dom);
        });
      },
      // 处理文件上传(没有传入url时返回本地链接)
      handleUpload() {
        return __async(this, null, function* () {
          const {
            url,
            name,
            header = {},
            formData = {}
          } = this.uploadOptions || {};
          const dom = document.getElementById(`xe-upload-${this.id}`);
          if (!dom.files[0])
            return;
          const tmpFileList = Array.from(dom.files);
          const tmpUploads = [];
          for (let i = 0; i < tmpFileList.length; i += 1) {
            const e = tmpFileList[i];
            let tmpType = "file";
            if (e.type.includes("image")) {
              tmpType = "image";
            }
            if (e.type.includes("video")) {
              tmpType = "video";
            }
            const tmpExts = {
              size: e.size,
              name: e.name,
              type: e.type,
              fileType: tmpType,
              tempFilePath: "",
              base64Url: ""
            };
            if (!url) {
              const [parseError, parseUrl] = yield awaitWrap(fileToBase64(dom.files[i]));
              if (!parseError) {
                tmpExts.base64Url = parseUrl;
              }
              tmpUploads.push(tmpExts);
              continue;
            }
            ;
            const tmpData = new FormData();
            tmpData.append(name, dom.files[i], e.name);
            for (let key in formData) {
              tmpData.append(key, formData[key]);
            }
            const onprogress = (ev) => {
              if (ev.lengthComputable) {
                var result = ev.loaded / ev.total * 100;
                this.handleRenderEmits({
                  type: "onprogress",
                  data: {
                    progress: Math.floor(result),
                    current: i + 1,
                    total: tmpFileList.length
                  }
                });
              }
              ;
            };
            tmpUploads.push(appUploadFile({
              url,
              header,
              formData: tmpData
            }, tmpExts, onprogress));
          }
          if (!url) {
            return this.handleRenderEmits({
              type: "choose",
              data: tmpUploads
            });
          }
          this.handleRenderEmits({
            type: "onprogress",
            data: {
              progress: 0,
              current: 1,
              total: tmpFileList.length
            }
          });
          const [err, res] = yield awaitWrap(Promise.all(tmpUploads));
          if (err) {
            return this.handleRenderEmits({
              type: "warning",
              data: err
            });
          }
          this.handleRenderEmits({
            type: "success",
            data: res
          });
        });
      },
      // 数据传输到XeUpload组件
      handleRenderEmits(data) {
        this.$ownerInstance.callMethod("handleEmits", data);
      }
    }
  };
  return __toCommonJS(stdin_exports);
})();


__renderjsModules["463e3cc3"] = (() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getOwnPropSymbols = Object.getOwnPropertySymbols;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __propIsEnum = Object.prototype.propertyIsEnumerable;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
      if (__hasOwnProp.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
      for (var prop of __getOwnPropSymbols(b)) {
        if (__propIsEnum.call(b, prop))
          __defNormalProp(a, prop, b[prop]);
      }
    return a;
  };
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

  // <stdin>
  var stdin_exports = {};
  __export(stdin_exports, {
    default: () => stdin_default
  });

  // C:/项目代码/sale-agent-web/uni_modules/z-paging/components/z-paging/config/index.js
  var config_default = {};

  // C:/项目代码/sale-agent-web/uni_modules/z-paging/components/z-paging/js/z-paging-constant.js
  var z_paging_constant_default = {
    // 当前版本号
    version: "2.8.6",
    // 延迟操作的通用时间
    delayTime: 100,
    // 请求失败时候全局emit使用的key
    errorUpdateKey: "z-paging-error-emit",
    // 全局emit complete的key
    completeUpdateKey: "z-paging-complete-emit",
    // z-paging缓存的前缀key
    cachePrefixKey: "z-paging-cache",
    // 虚拟列表中列表index的key
    listCellIndexKey: "zp_index",
    // 虚拟列表中列表的唯一key
    listCellIndexUniqueKey: "zp_unique_index"
  };

  // C:/项目代码/sale-agent-web/uni_modules/z-paging/components/z-paging/js/z-paging-utils.js
  var storageKey = "Z-PAGING-REFRESHER-TIME-STORAGE-KEY";
  var config = null;
  var configLoaded = false;
  var cachedSystemInfo = null;
  var timeoutMap = {};
  function gc(key, defaultValue) {
    return () => {
      _handleDefaultConfig();
      if (!config)
        return defaultValue;
      const value = config[key];
      return value === void 0 ? defaultValue : value;
    };
  }
  function getTouch(e) {
    let touch = null;
    if (e.touches && e.touches.length) {
      touch = e.touches[0];
    } else if (e.changedTouches && e.changedTouches.length) {
      touch = e.changedTouches[0];
    } else if (e.datail && e.datail != {}) {
      touch = e.datail;
    } else {
      return { touchX: 0, touchY: 0 };
    }
    return {
      touchX: touch.clientX,
      touchY: touch.clientY
    };
  }
  function getTouchFromZPaging(target) {
    if (target && target.tagName && target.tagName !== "BODY" && target.tagName !== "UNI-PAGE-BODY") {
      const classList = target.classList;
      if (classList && classList.contains("z-paging-content")) {
        return {
          isFromZp: true,
          isPageScroll: classList.contains("z-paging-content-page"),
          isReachedTop: classList.contains("z-paging-reached-top"),
          isUseChatRecordMode: classList.contains("z-paging-use-chat-record-mode")
        };
      } else {
        return getTouchFromZPaging(target.parentNode);
      }
    } else {
      return { isFromZp: false };
    }
  }
  function getParent(parent) {
    if (!parent)
      return null;
    if (parent.$refs.paging)
      return parent;
    return getParent(parent.$parent);
  }
  function consoleErr(err) {
    console.error(`[z-paging]${err}`);
  }
  function delay(callback, ms = z_paging_constant_default.delayTime, key) {
    const timeout = setTimeout(callback, ms);
    ;
    if (!!key) {
      timeoutMap[key] && clearTimeout(timeoutMap[key]);
      timeoutMap[key] = timeout;
    }
    return timeout;
  }
  function setRefesrherTime(time, key) {
    const datas = getRefesrherTime() || {};
    datas[key] = time;
    uni.setStorageSync(storageKey, datas);
  }
  function getRefesrherTime() {
    return uni.getStorageSync(storageKey);
  }
  function getRefesrherTimeByKey(key) {
    const datas = getRefesrherTime();
    return datas && datas[key] ? datas[key] : null;
  }
  function getRefesrherFormatTimeByKey(key, textMap) {
    const time = getRefesrherTimeByKey(key);
    const timeText = time ? _timeFormat(time, textMap) : textMap.none;
    return `${textMap.title}${timeText}`;
  }
  function convertToPx(text) {
    const dataType = Object.prototype.toString.call(text);
    if (dataType === "[object Number]")
      return text;
    let isRpx = false;
    if (text.indexOf("rpx") !== -1 || text.indexOf("upx") !== -1) {
      text = text.replace("rpx", "").replace("upx", "");
      isRpx = true;
    } else if (text.indexOf("px") !== -1) {
      text = text.replace("px", "");
    }
    if (!isNaN(text)) {
      if (isRpx)
        return Number(rpx2px(text));
      return Number(text);
    }
    return 0;
  }
  function rpx2px(rpx) {
    return uni.upx2px(rpx);
  }
  function getSystemInfoSync(useCache = false) {
    if (useCache && cachedSystemInfo) {
      return cachedSystemInfo;
    }
    const infoTypes = ["DeviceInfo", "AppBaseInfo", "WindowInfo"];
    const { deviceInfo, appBaseInfo, windowInfo } = infoTypes.reduce((acc, key) => {
      const method = `get${key}`;
      if (uni[method] && uni.canIUse(method)) {
        acc[key.charAt(0).toLowerCase() + key.slice(1)] = uni[method]();
      }
      return acc;
    }, {});
    if (deviceInfo && appBaseInfo && windowInfo) {
      cachedSystemInfo = __spreadValues(__spreadValues(__spreadValues({}, deviceInfo), appBaseInfo), windowInfo);
    } else {
      cachedSystemInfo = uni.getSystemInfoSync();
    }
    return cachedSystemInfo;
  }
  function getTime() {
    return (/* @__PURE__ */ new Date()).getTime();
  }
  function getInstanceId() {
    const s = [];
    const hexDigits = "0123456789abcdef";
    for (let i = 0; i < 10; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 16), 1);
    }
    return s.join("") + getTime();
  }
  function wait(ms) {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }
  function isPromise(func) {
    return Object.prototype.toString.call(func) === "[object Promise]";
  }
  function addUnit(value, unit) {
    if (Object.prototype.toString.call(value) === "[object String]") {
      let tempValue = value;
      tempValue = tempValue.replace("rpx", "").replace("upx", "").replace("px", "");
      if (value.indexOf("rpx") === -1 && value.indexOf("upx") === -1 && value.indexOf("px") !== -1) {
        tempValue = parseFloat(tempValue) * 2;
      }
      value = tempValue;
    }
    return unit === "rpx" ? value + "rpx" : value / 2 + "px";
  }
  function deepCopy(obj) {
    if (typeof obj !== "object" || obj === null)
      return obj;
    let newObj = Array.isArray(obj) ? [] : {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        newObj[key] = deepCopy(obj[key]);
      }
    }
    return newObj;
  }
  function _handleDefaultConfig() {
    if (configLoaded)
      return;
    if (config_default && Object.keys(config_default).length) {
      config = config_default;
    }
    if (!config && uni.$zp) {
      config = uni.$zp.config;
    }
    config = config ? Object.keys(config).reduce((result, key) => {
      result[_toCamelCase(key)] = config[key];
      return result;
    }, {}) : null;
    configLoaded = true;
  }
  function _timeFormat(time, textMap) {
    const date = new Date(time);
    const currentDate = /* @__PURE__ */ new Date();
    const dateDay = new Date(time).setHours(0, 0, 0, 0);
    const currentDateDay = (/* @__PURE__ */ new Date()).setHours(0, 0, 0, 0);
    const disTime = dateDay - currentDateDay;
    let dayStr = "";
    const timeStr = _dateTimeFormat(date);
    if (disTime === 0) {
      dayStr = textMap.today;
    } else if (disTime === -864e5) {
      dayStr = textMap.yesterday;
    } else {
      dayStr = _dateDayFormat(date, date.getFullYear() !== currentDate.getFullYear());
    }
    return `${dayStr} ${timeStr}`;
  }
  function _dateDayFormat(date, showYear = true) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return showYear ? `${year}-${_fullZeroToTwo(month)}-${_fullZeroToTwo(day)}` : `${_fullZeroToTwo(month)}-${_fullZeroToTwo(day)}`;
  }
  function _dateTimeFormat(date) {
    const hour = date.getHours();
    const minute = date.getMinutes();
    return `${_fullZeroToTwo(hour)}:${_fullZeroToTwo(minute)}`;
  }
  function _fullZeroToTwo(str) {
    str = str.toString();
    return str.length === 1 ? "0" + str : str;
  }
  function _toCamelCase(value) {
    return value.replace(/-([a-z])/g, (_, group1) => group1.toUpperCase());
  }
  var z_paging_utils_default = {
    gc,
    setRefesrherTime,
    getRefesrherFormatTimeByKey,
    getTouch,
    getTouchFromZPaging,
    getParent,
    convertToPx,
    getTime,
    getInstanceId,
    consoleErr,
    delay,
    wait,
    isPromise,
    addUnit,
    deepCopy,
    rpx2px,
    getSystemInfoSync
  };

  // C:/项目代码/sale-agent-web/uni_modules/z-paging/components/z-paging/wxs/z-paging-renderjs.js
  var data = {
    startY: 0,
    isTouchFromZPaging: false,
    isUsePageScroll: false,
    isReachedTop: true,
    isIosAndH5: false,
    useChatRecordMode: false,
    appLaunched: false
  };
  var z_paging_renderjs_default = {
    mounted() {
      if (window) {
        this._handleTouch();
        this.$ownerInstance.callMethod("_handlePageLaunch");
      }
    },
    methods: {
      // 接收逻辑层发送的数据（是否是ios+h5）
      renderPropIsIosAndH5Change(newVal) {
        if (newVal === -1)
          return;
        data.isIosAndH5 = newVal;
      },
      // 拦截处理touch事件
      _handleTouch() {
        if (!window.$zPagingRenderJsInited) {
          window.$zPagingRenderJsInited = true;
          window.addEventListener("touchstart", this._handleTouchstart, { passive: true });
          window.addEventListener("touchmove", this._handleTouchmove, { passive: false });
        }
      },
      // 处理touch开始
      _handleTouchstart(e) {
        const touch = z_paging_utils_default.getTouch(e);
        data.startY = touch.touchY;
        const touchResult = z_paging_utils_default.getTouchFromZPaging(e.target);
        data.isTouchFromZPaging = touchResult.isFromZp;
        data.isUsePageScroll = touchResult.isPageScroll;
        data.isReachedTop = touchResult.isReachedTop;
        data.useChatRecordMode = touchResult.isUseChatRecordMode;
      },
      // 处理touch中
      _handleTouchmove(e) {
        const touch = z_paging_utils_default.getTouch(e);
        const moveY = touch.touchY - data.startY;
        if (data.isTouchFromZPaging && (data.isReachedTop && (data.useChatRecordMode ? moveY < 0 : moveY > 0) || !data.useChatRecordMode && data.isIosAndH5 && !data.isUsePageScroll && moveY < 0)) {
          if (e.cancelable && !e.defaultPrevented) {
            e.preventDefault();
          }
        }
      },
      // 移除touch相关事件监听
      _removeAllEventListener() {
        window.removeEventListener("touchstart");
        window.removeEventListener("touchmove");
      }
    }
  };

  // <stdin>
  var stdin_default = {
    name: "z-paging",
    mixins: [z_paging_renderjs_default]
  };
  return __toCommonJS(stdin_exports);
})();


__renderjsModules["06bd5139"] = (() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getOwnPropSymbols = Object.getOwnPropertySymbols;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __propIsEnum = Object.prototype.propertyIsEnumerable;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
      if (__hasOwnProp.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
      for (var prop of __getOwnPropSymbols(b)) {
        if (__propIsEnum.call(b, prop))
          __defNormalProp(a, prop, b[prop]);
      }
    return a;
  };
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
  var __async = (__this, __arguments, generator) => {
    return new Promise((resolve, reject) => {
      var fulfilled = (value) => {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      };
      var rejected = (value) => {
        try {
          step(generator.throw(value));
        } catch (e) {
          reject(e);
        }
      };
      var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
      step((generator = generator.apply(__this, __arguments)).next());
    });
  };

  // <stdin>
  var stdin_exports = {};
  __export(stdin_exports, {
    default: () => stdin_default
  });

  // C:/项目代码/sale-agent-web/uni_modules/gao-ChatSSEClient/components/gao-ChatSSEClient/fetch-event-source/parse.js
  function getBytes(stream, onChunk) {
    return __async(this, null, function* () {
      const reader = stream.getReader();
      let result;
      while (!(result = yield reader.read()).done) {
        onChunk(result.value);
      }
    });
  }
  function getLines(onLine) {
    let buffer;
    let position;
    let fieldLength;
    let discardTrailingNewline = false;
    return function onChunk(arr) {
      if (buffer === void 0) {
        buffer = arr;
        position = 0;
        fieldLength = -1;
      } else {
        buffer = concat(buffer, arr);
      }
      const bufLength = buffer.length;
      let lineStart = 0;
      while (position < bufLength) {
        if (discardTrailingNewline) {
          if (buffer[position] === 10) {
            lineStart = ++position;
          }
          discardTrailingNewline = false;
        }
        let lineEnd = -1;
        for (; position < bufLength && lineEnd === -1; ++position) {
          switch (buffer[position]) {
            case 58:
              if (fieldLength === -1) {
                fieldLength = position - lineStart;
              }
              break;
            case 13:
              discardTrailingNewline = true;
            case 10:
              lineEnd = position;
              break;
          }
        }
        if (lineEnd === -1) {
          break;
        }
        onLine(buffer.subarray(lineStart, lineEnd), fieldLength);
        lineStart = position;
        fieldLength = -1;
      }
      if (lineStart === bufLength) {
        buffer = void 0;
      } else if (lineStart !== 0) {
        buffer = buffer.subarray(lineStart);
        position -= lineStart;
      }
    };
  }
  function getMessages(onId, onRetry, onMessage) {
    let message = newMessage();
    let decoder;
    decoder = {
      decode(arraybuffer) {
        return decodeURIComponent(escape(String.fromCharCode(...arraybuffer)));
      }
    };
    decoder = new TextDecoder();
    return function onLine(line, fieldLength) {
      if (line.length === 0) {
        onMessage === null || onMessage === void 0 ? void 0 : onMessage(message);
        message = newMessage();
      } else if (fieldLength > 0) {
        const field = decoder.decode(line.subarray(0, fieldLength));
        const valueOffset = fieldLength + (line[fieldLength + 1] === 32 ? 2 : 1);
        const value = decoder.decode(line.subarray(valueOffset));
        switch (field) {
          case "data":
            message.data = message.data ? message.data + "\n" + value : value;
            break;
          case "event":
            message.event = value;
            break;
          case "id":
            onId(message.id = value);
            break;
          case "retry":
            const retry = parseInt(value, 10);
            if (!isNaN(retry)) {
              onRetry(message.retry = retry);
            }
            break;
          default:
            const msg = decoder.decode(line, { stream: true });
            message.data = msg;
            onMessage(message);
            break;
        }
      }
    };
  }
  function concat(a, b) {
    const res = new Uint8Array(a.length + b.length);
    res.set(a);
    res.set(b, a.length);
    return res;
  }
  function newMessage() {
    return {
      data: "",
      event: "",
      id: "",
      retry: void 0
    };
  }

  // C:/项目代码/sale-agent-web/uni_modules/gao-ChatSSEClient/components/gao-ChatSSEClient/fetch-event-source/fetch.js
  var __rest = function(s, e) {
    var t = {};
    for (var p in s)
      if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
          t[p[i]] = s[p[i]];
      }
    return t;
  };
  var EventStreamContentType = "text/event-stream";
  var DefaultRetryInterval = 1e3;
  var LastEventId = "last-event-id";
  function fetchEventSource(input, _a) {
    var { signal: inputSignal, headers: inputHeaders, onopen: inputOnOpen, onmessage, onclose, onerror, openWhenHidden, fetch: inputFetch } = _a, rest = __rest(_a, ["signal", "headers", "onopen", "onmessage", "onclose", "onerror", "openWhenHidden", "fetch"]);
    return new Promise((resolve, reject) => {
      const headers = Object.assign({}, inputHeaders);
      if (!headers.accept) {
        headers.accept = EventStreamContentType;
      }
      let curRequestController;
      function onVisibilityChange() {
        curRequestController.abort();
        if (!document.hidden) {
          create();
        }
      }
      if (!openWhenHidden) {
        document.addEventListener("visibilitychange", onVisibilityChange);
      }
      let retryInterval = DefaultRetryInterval;
      let retryTimer = 0;
      function dispose() {
        document.removeEventListener("visibilitychange", onVisibilityChange);
        window.clearTimeout(retryTimer);
        curRequestController.abort();
      }
      inputSignal === null || inputSignal === void 0 ? void 0 : inputSignal.addEventListener("abort", () => {
        dispose();
        resolve();
      });
      const fetch = inputFetch !== null && inputFetch !== void 0 ? inputFetch : window.fetch;
      const onopen = inputOnOpen !== null && inputOnOpen !== void 0 ? inputOnOpen : defaultOnOpen;
      function create() {
        return __async(this, null, function* () {
          var _a2;
          curRequestController = new AbortController();
          try {
            const response = yield fetch(input, Object.assign(Object.assign({}, rest), { headers, signal: curRequestController.signal }));
            yield onopen(response);
            yield getBytes(response.body, getLines(getMessages((id) => {
              if (id) {
                headers[LastEventId] = id;
              } else {
                delete headers[LastEventId];
              }
            }, (retry) => {
              retryInterval = retry;
            }, onmessage)));
            onclose === null || onclose === void 0 ? void 0 : onclose();
            dispose();
            resolve();
          } catch (err) {
            if (!curRequestController.signal.aborted) {
              try {
                const interval = (_a2 = onerror === null || onerror === void 0 ? void 0 : onerror(err)) !== null && _a2 !== void 0 ? _a2 : retryInterval;
                window.clearTimeout(retryTimer);
                retryTimer = window.setTimeout(create, interval);
              } catch (innerErr) {
                dispose();
                reject(innerErr);
              }
            }
          }
        });
      }
      create();
    });
  }
  function defaultOnOpen(response) {
    const contentType = response.headers.get("content-type");
    if (!(contentType === null || contentType === void 0 ? void 0 : contentType.startsWith(EventStreamContentType))) {
      throw new Error(`Expected content-type to be ${EventStreamContentType}, Actual: ${contentType}`);
    }
  }

  // <stdin>
  var stdin_default = {
    data() {
      return {
        ctrl: null
      };
    },
    methods: {
      objToJson(obj) {
        const json = {};
        for (const key in obj) {
          const val = obj[key];
          if (typeof val === "string" || typeof val === "number" || typeof val === "boolean") {
            json[key] = val;
          } else {
            json[key] = val.toString();
          }
        }
        return json;
      },
      /**
       * 停止生成
       */
      stopChatCore() {
        var _a;
        (_a = this.ctrl) == null ? void 0 : _a.abort();
      },
      /**
       * 开始对话
       */
      startChatCore({ url, body, headers, method }) {
        if (!url)
          return;
        try {
          this.ctrl = new AbortController();
          fetchEventSource(
            url,
            {
              readJson: true,
              method,
              openWhenHidden: true,
              signal: this.ctrl.signal,
              headers: __spreadValues({
                "Content-Type": "application/json"
              }, headers),
              body: body ? body : void 0,
              onopen: (response) => {
                this.$ownerInstance.callMethod("open", this.objToJson(response));
              },
              onmessage: (data) => {
                this.$ownerInstance.callMethod("message", data);
              },
              onerror: (err) => {
                console.log(err);
                this.$ownerInstance.callMethod("error", JSON.stringify(err));
              }
            }
          ).then(() => {
            this.$ownerInstance.callMethod("finish");
          }).catch((err) => {
            console.log(err);
            this.$ownerInstance.callMethod("error", err);
          });
        } catch (e) {
          console.log(e);
        }
      }
    }
  };
  return __toCommonJS(stdin_exports);
})();
