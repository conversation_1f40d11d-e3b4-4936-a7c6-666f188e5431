<template>
  <!-- 用户消息操作区域 -->
  <view
    class="user-message-actions"
    :class="{ 'actions-visible': isVisible }"
    v-if="showActions"
  >
    <!-- 复制按钮 -->
    <view class="item-icon" @click="handleCopy">
      <!-- PC端添加tooltip -->
      <uni-tooltip v-if="isPCDevice" content="复制" placement="top">
        <image
          src="/static/chatImg/copy.svg"
          class="item-icon-img"
          alt="复制"
        />
      </uni-tooltip>

      <!-- 移动端不显示tooltip -->
      <image
        v-else
        src="/static/chatImg/copy.svg"
        class="item-icon-img"
        alt="复制"
      />
    </view>

    <!-- 删除按钮 -->
    <view class="item-icon" @click="handleDelete">
      <!-- PC端添加tooltip -->
      <uni-tooltip v-if="isPCDevice" content="删除" placement="top">
        <image
          src="/static/chatImg/clear.svg"
          class="item-icon-img"
          alt="删除"
        />
      </uni-tooltip>
      <!-- 移动端不显示tooltip -->
      <image
        v-else
        src="/static/chatImg/clear.svg"
        class="item-icon-img"
        alt="删除"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { copyMessage } from '@/utils/utils.js'

// Props定义
const props = defineProps({
  // 消息内容
  content: {
    type: String,
    required: true,
  },
  // 是否显示操作按钮
  isVisible: {
    type: Boolean,
    default: false,
  },
  // 是否显示操作区域
  showActions: {
    type: Boolean,
    default: true,
  },
  // 消息索引（用于删除）
  messageIndex: {
    type: Number,
    default: -1,
  },
});

// Emits定义
const emit = defineEmits(["delete"]);

// 响应式状态：检测是否为PC设备
const isPCDevice = ref(false);

/**
 * 检测设备类型
 * 基于窗口宽度判断是否为PC端
 */
const detectDeviceType = () => {
  // #ifdef H5
  if (typeof window !== 'undefined') {
    // PC端判断条件：窗口宽度大于768px
    const isPC = window.innerWidth > 768;
    isPCDevice.value = isPC;
  }
  // #endif
  
  // #ifndef H5
  // 在非H5平台，默认为移动端
  isPCDevice.value = false;
  // #endif
};

/**
 * 窗口大小变化监听器
 */
let resizeListener = null;

/**
 * 初始化设备检测和窗口监听
 */
const initDeviceDetection = () => {
  // #ifdef H5
  if (typeof window !== 'undefined') {
    // 初始检测
    detectDeviceType();

    // 添加窗口大小变化监听
    resizeListener = () => {
      detectDeviceType();
    };

    window.addEventListener('resize', resizeListener);
  }
  // #endif
  
  // #ifndef H5
  // 在非H5平台（如APP端），只需要检测设备类型
  detectDeviceType();
  // #endif
};

/**
 * 清理窗口监听器
 */
const cleanupDeviceDetection = () => {
  // #ifdef H5
  if (typeof window !== 'undefined' && resizeListener) {
    window.removeEventListener('resize', resizeListener);
    resizeListener = null;
  }
  // #endif
};

/**
 * 处理复制
 */
const handleCopy = () => {
  copyMessage(props.content);
};

/**
 * 处理删除
 */
const handleDelete = () => {
  // uni.showModal({
  //   title: '提示',
  //   content: '确定要删除这条消息吗？',
  //   confirmText: '删除',
  //   confirmColor: '#FF0000',
  //   success: (res) => {
  //     if (res.confirm) {
  //       emit("delete", props.messageIndex);
  //     }
  //   }
  // });
  emit("delete", props.messageIndex);
};

// 组件挂载时初始化设备检测
onMounted(() => {
  initDeviceDetection();
});

// 组件卸载时清理监听器
onUnmounted(() => {
  cleanupDeviceDetection();
});
</script>

<style scoped lang="scss">
.user-message-actions {
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-right: 4px;
  height: 20px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;

  &.actions-visible {
    opacity: 1;
    visibility: visible;
  }

  :deep(.uni-tooltip-popup) {
    width: max-content;
  }
}

// 消息项
.item-icon {
  height: 20px;
  width: 20px;
  margin-left: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  .item-icon-img {
    height: 20px;
    width: 20px;
  }
}
</style> 