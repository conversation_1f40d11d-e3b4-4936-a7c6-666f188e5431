<template>
  <!-- 【 商机详情模块 TAB】 -->
  <view class="business-details-container">
    <!-- 商机信息 -->
    <view class="section-box">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">商机信息</view>
      </view>
    </view>
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
        <uni-forms
          class="task-form"
          :modelValue="editFormData"
          border
          label-align="center"
        >
          <uni-forms-item>
            <template #label>
              <view class="task-label">商机名称</view>
            </template>
            <view class="item-box">
              <uni-easyinput
                trim="all"
                v-model="editFormData.title"
                placeholder="商机名称"
              ></uni-easyinput>
          </view>
          </uni-forms-item>
          <uni-forms-item>
            <template #label>
              <view class="task-label">客户名称</view>
            </template>
            <view class="item-box">
            <uni-data-select
              v-model="editFormData.customer_id"
              :localdata="oldCustomerList"
              @change="handleCustomerChange"
              :clear="true"
            ></uni-data-select>
          </view>
          </uni-forms-item>
          <uni-forms-item>
            <template #label>
              <view class="task-label">需求内容</view>
            </template>
            <view class="item-box">
              <uni-easyinput
                trim="all"
                v-model="editFormData.content"
                placeholder="添加需求内容"
              ></uni-easyinput>
            </view>
          </uni-forms-item>
          <uni-forms-item label="当前阶段" name="status" required>
            <template #label>
              <view class="task-label">当前阶段</view>
            </template>
            <uni-data-select
              v-model="editFormData.status"
              :localdata="stages"
              placeholder="请选择当前阶段"
            >
            </uni-data-select>
          </uni-forms-item>
          <uni-forms-item>
            <template #label>
              <view class="task-label">预期成交</view>
            </template>
            <view class="item-box">
              <uni-easyinput
                trim="all"
                v-model="formattedBudget"
                type="text"
                placeholder="请输入预期成交"
                maxlength="15"
                @input="handleBudgetInput"
              ></uni-easyinput>
            </view>
          </uni-forms-item>
          <uni-forms-item>
            <template #label>
              <view class="task-label">商机来源</view>
            </template>
            <view class="item-box">
              <uni-easyinput
                trim="all"
                v-model="editFormData.source"
                placeholder="添加"
              ></uni-easyinput>
            </view>
          </uni-forms-item>
          <uni-forms-item label="备注">
            <template #label>
              <view class="task-label">商机备注</view>
            </template>
            <uni-easyinput
              trim="all"
              v-model="editFormData.notes"
              maxlength="70"
              placeholder="添加"
            ></uni-easyinput>
          </uni-forms-item>
        </uni-forms>
      </view>
    </transition>
  </view>
</template>
        
<script setup>
import { reactive, ref, nextTick, watch, toRefs, onBeforeUnmount,computed } from "vue";
import { getCustomerList } from "@/http/customer.js";
import { fetchOpportunityList,updateOpportunity } from "@/http/business.js";
import { fetchUserConfig } from "@/http/user.js"; // 引入配置接口

const emit = defineEmits(["refresh","RefreshList"]);
const props = defineProps({
  // 客户详情数据
  userDetails: {
    type: Object,
    default: {},
  },
});
// 修改状态管理
const expandStates = ref({
  current: true,
  finished: true,
});
const oldCustomerList = ref([]);
// 商机阶段选项 - 改为动态获取
const stages = ref([]);

// 1. 使用带标记位的响应式对象
const state = reactive({
  editFormData: {
    id: "",
    user_id: "",
    customer_id: "",
    company_short_name: "",
    title: "",
    content: "",
    source: "",
    notes: "",
    status: 0,
    budget: undefined,
  },
  isExternalUpdate: false, // 标记是否是外部更新
  formDirty: false, // 表单是否有修改
});

const { editFormData } = toRefs(state);

// 格式化预期成交金额（统一数字类型）
const formattedBudget = computed({
  get: () => {
    if (!editFormData.value.budget && editFormData.value.budget !== 0) return '';
    // 直接返回数字值，不添加千分位格式化
    return String(editFormData.value.budget);
  },
  set: (val) => {
    // 移除非数字字符，统一转换为数字类型
    const numericValue = val.replace(/[^\d]/g, '');
    editFormData.value.budget = numericValue ? parseInt(numericValue) : 0;
  }
});

// 处理输入事件（统一数字类型）
const handleBudgetInput = (e) => {
  const value = e;
  // 移除非数字字符，统一转换为数字类型
  const numericValue = value.replace(/[^\d]/g, '');
  editFormData.value.budget = numericValue ? parseInt(numericValue) : 0;
};

// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
  expandStates.value[type] = !expandStates.value[type];
};

// 初始化客户列表
const queryCustomerList = async () => {
  try {
    const res = await getCustomerList({
      page: 1,
      limit: 200,
    });
    console.log("初始化客户列表:", res);
    if (res.code === 0 && res.data.list.length) {
      oldCustomerList.value = res.data?.list.map((item) => ({
        text: item.company_short_name, // 显示的文本
        value: item.customer_id, // 对应的值
      }));
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
};

// @change 关联客户 事件处理
const handleCustomerChange = async (e) => {
  // 从客户列表中找到对应的公司名称
  // const selectedCustomer = oldCustomerList.value.find(
  //   (item) => item.value === editFormData.customer_id
  // );
  // // 如果找到了客户，更新公司名称
  // if (selectedCustomer) {
  //   editFormData.company_short_name = selectedCustomer.text;
  // }
};

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function(...args) {
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 4. 防抖处理提交（500ms）
const handleBusinessSubmitDebounced = debounce(async (formData) => {
  try {
    // 创建提交数据的副本
    const submitData = { ...formData };
    
    if (submitData.status === '') {
      submitData.status = 0;
    }
    
    // 确保budget是数字类型
    if (submitData.budget === '' || submitData.budget === null || submitData.budget === undefined) {
      submitData.budget = 0;
    } else {
      // 强制转换为数字类型
      submitData.budget = parseInt(submitData.budget) || 0;
    }
    const res = await updateOpportunity(submitData);
    if (res.code === 0) {
      console.log("修改成功:", res);
      // 如果后端返回了budget字段，强制转换为数字类型
      if (res.data && res.data.budget !== undefined) {
        state.isExternalUpdate = true;
        // 强制转换后端返回的数据为数字类型
        editFormData.value.budget = parseInt(res.data.budget) || 0;
        nextTick(() => {
          state.isExternalUpdate = false;
        });
      }
    } else {
      console.error("修改失败:", res);
    }
  } catch (err) {
    console.error("请求失败:", err);
  }
}, 1000);

// 获取商机阶段配置
const getBusinessStages = async () => {
  try {
    const res = await fetchUserConfig();
    console.log("获取商机阶段配置:", res);
    if (res.code === 0 && res.data && res.data.business_opportunities) {
      const statusMap = res.data.business_opportunities.status_map || {};
      // 将接口返回的状态映射转换为下拉选项格式
      stages.value = Object.entries(statusMap).map(([value, text]) => ({
        value: parseInt(value), // 确保值是数字类型
        text
      }));
    }
  } catch (err) {
    console.error("获取商机阶段配置失败:", err);
  }
};
// 2. 监听props变化（外部更新）
watch(
  () => props.userDetails,
  (newVal) => {
    if (newVal.bo_id) {
      state.isExternalUpdate = true; // 标记为外部更新
      Object.assign(state.editFormData, {
        id: newVal.bo_id || "",
        user_id: newVal.user_id || "",
        customer_id: newVal.customer_id || "",
        content: newVal.content || "",
        source: newVal.source || "",
        title: newVal.title || "",
        notes: newVal.notes || "",
        status: newVal.status || 0,
        budget: newVal.budget !== undefined ? (parseInt(newVal.budget) || 0) : 0,
      });
      // 使用 nextTick 确保标记重置在更新完成后
      nextTick(() => {
        state.isExternalUpdate = false;
      });
    }
  },
  { deep: true, immediate: true }
);

// 3. 监听表单变化（用户修改）
watch(
  () => [...Object.values(state.editFormData)], // 转换为可监听的数组
  (newValues, oldValues) => {
    if (state.isExternalUpdate) return // 过滤外部更新
    // 只要表单有变化，标记为修改状态
    state.formDirty = true;
    handleBusinessSubmitDebounced(state.editFormData);
  },
  { deep: true }
)

// 5. 组件销毁时触发提交
onBeforeUnmount(() => {
  // 如果表单有修改，提交数据
  if (state.formDirty) {
    handleBusinessSubmitDebounced(state.editFormData);
    emit('RefreshList')
  }
});

queryCustomerList();
// 获取商机阶段配置
getBusinessStages();

</script>
        
<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.business-details-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .section-box {
    display: flex;
    align-items: center;
    margin: 15px 5px 15px 5px;
    padding: 0px 10px;
    .section-left {
      display: flex;
      align-items: center;
      .icon-down {
        width: 25px;
        height: 25px;
      }
    }
    .section-title {
      color: var(---, #787d86);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
  .s-p-b {
    justify-content: space-between;
  }
  .todo-card {
    margin: 15px;
    padding: 0px 10px;
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;
    .task-form {
      .task-label {
        width: 75px;
        height: 35px;
        color: var(---, #787d86);
        text-overflow: ellipsis;
        font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 35px;
      }
      .item-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 35px;
        // :deep(.uni-date-x--border) {
        //   border: unset;
        // }
        // .picker-list {
        //   flex: 1;
        // }
      }
      // :deep(.is-input-border) {
      //   border: unset;
      // }
      // :deep(.uni-easyinput__content-input) {
      //   padding-left: 0 !important;
      // }
    }
  }
}
</style>