<template>
  <!-- PPT编辑器组件 -->
  <DocmeeUI
    :ppt-id="pptId"
    :token="token"
    :page="page"
    :is-mobile="isMobile"
    @mounted="onEditorMounted"
    @message="onEditorMessage"
  />
</template>

<script setup>
import { ref, computed, onMounted,nextTick } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import DocmeeUI from "@/components/DocmeeUI/DocmeeUI.vue";
import { getDocmeeToken } from "@/http/docmee-ppt.js";

// 页面参数
const pptId = ref(""); // 默认PPT ID
const token = ref(null);
const page = ref("editor"); // 页面类型

// 响应式数据
const isMobile = ref(false);

/**
 * 页面加载时的参数处理
 */
onLoad((options) => {
  console.log("📄 PPT编辑器页面加载，参数:", options);
  // 检测设备类型
  const systemInfo = uni.getSystemInfoSync();
  isMobile.value =
    systemInfo.platform === "android" || systemInfo.platform === "ios";
  // 处理URL参数
  if (options.ppt_id || options.pptId) {
    pptId.value = options.ppt_id || options.pptId;
    console.log("📋 从URL获取到PPT ID:", pptId.value);
  }
});

/**
 * 从后端获取token（简化版本）
 */
const fetchTokenFromServer = async () => {
  try {
    let params = {
      ppt_id: pptId.value
    };
    console.log("🔍 当前pptId.value:", pptId.value);
    const response = await getDocmeeToken(params);
    if (response.code === 0 && response.data?.token) {
      token.value = response.data?.token;
    } else {
      throw new Error(response.message || "Token获取失败");
    }
  } catch (error) {
    console.error("❌ 获取token失败:", error);
  }
};

/**
 * 编辑器挂载完成
 */
const onEditorMounted = () => {
  console.log("🎉 PPT编辑器挂载完成:");
};

/**
 * 编辑器消息处理
 */
const onEditorMessage = (message) => {
  console.log("📨 编辑器消息:", message);
  switch (message.type) {
    case "ppt-created":
      uni.showToast({
        title: "PPT创建成功",
        icon: "success",
      });
      break;
    case "ppt-saved":
      uni.showToast({
        title: "PPT保存成功",
        icon: "success",
      });
      break;
    case "ppt-exported":
      uni.showToast({
        title: "PPT导出成功",
        icon: "success",
      });
      break;
    default:
      // 处理其他消息
      break;
  }
};

// 页面卸载
onUnload(() => {
  console.log("📴 PPT编辑器页面卸载");
});

onMounted(async () => {
  try {
    await fetchTokenFromServer();
  } catch (err) {
    console.error('❌ 组件初始化失败:', err)
  }
})
</script>

<style scoped lang="scss"></style>
