<template>
  <!-- 客户资料 模块 -->
  <view class="recently-details-container">
    <!-- 面包屑 -->
    <view class="section-box s-p-b" v-if="routes.length > 1">
      <uni-breadcrumb separator="/">
        <uni-breadcrumb-item
          v-for="(route, index) in routes"
          :key="index"
          @click="handleBreadcrumbClick(index)"
        >
          {{ route.name }}
        </uni-breadcrumb-item>
      </uni-breadcrumb>
    </view>
    <!-- 文件列表 -->
    <transition name="slide-fade">
      <view class="todo-card" v-show="expandStates.current">
        <block v-for="(item, index) in currentFolderFiles" :key="index">
          <view class="todo-item" @click="item.type === 'dir' ? enterDirectory(item) : undefined">
            <!-- 文件图标/图片预览 -->
            <view class="file-thumbnail">
              <!-- 对于图片类型，直接显示缩略图 -->
              <image
                v-if="isImageFile(item.name)"
                class="file-preview"
                :src="item.url"
                mode="aspectFill"
              ></image>
              <!-- 对于非图片类型，显示文件图标 -->
              <image
                v-else
                class="flie-icon"
                :src="
                  item.type === 'dir'
                    ? '/static/file/document.svg'
                    : getFileIconPath(item.name)
                "
              ></image>
            </view>
            <view class="content-wrapper">
              <uni-easyinput
                v-if="item.isShowInput"
                trim="all"
                v-model="item.name"
                @change="handelInputSetName(item)"
                focus
              ></uni-easyinput>
              <view class="todo-title" v-else>{{ item.name }}</view>
              <!-- 添加上传时间显示 -->
              <view class="title-row">
                <view class="upload-time">{{ filterDateTime(item.created_at) }}</view>
                <!-- <view class="file-size" v-if="item.size">{{ formatFileSize(item.size) }}</view> -->
              </view>
            </view>
            <uni-icons
              v-if="item.type !== 'dir'"
              type="more-filled"
              size="20"
              color="#E2E4E9"
              @click="handeOpenClick(item)"
            ></uni-icons>
          </view>
          <!-- 分割线：除最后一项外 -->
          <view v-if="index !== fileList.length - 1" class="item-divider"></view>
        </block>
      </view>
    </transition>
    <ActionSheet
      v-if="operationFileShow"
      v-model:show="operationFileShow"
      :actions="actions"
      title="文件操作"
      @select="onFileSelect"
    />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import {
  saveOrEditAttachment,
  deleteAttachment,
} from "@/http/attachment.js";
import { filterDateTime } from "@/utils/formatTime.js";
import { isImageFile, formatFileSize } from "@/utils/fileUtils.js";
import { getFileIconPath,getMimeType} from '@/utils/fileUtils.js'
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具
const emit = defineEmits(["refresh"]);
const props = defineProps({
  // 客户文件列表数据
  fileList: {
    type: Array,
    default: [],
  },
});
const currentFile = ref(null); // 当前操作的文件
const operationFileShow = ref(false);
// 修改状态管理
const expandStates = ref({
  current: true,
  finished: true,
});

// 目录导航相关
const routes = ref([
  {
    id: 0,
    name: "全部文件",
  },
]);

// 当前目录ID
const currentFolderId = ref(0);

// 计算当前文件夹内容
const currentFolderFiles = computed(() => {
  if (currentFolderId.value === 0) {
    // 根目录
    return props.fileList;
  } else {
    // 查找子目录
    const findFolder = (list, id) => {
      for (const item of list) {
        if (item.id === id) {
          return item.children || [];
        }
        // 如果当前项有子目录，递归查找
        if (item.children && item.children.length > 0) {
          const found = findFolder(item.children, id);
          if (found.length > 0) return found;
        }
      }
      return [];
    };
    return findFolder(props.fileList, currentFolderId.value);
  }
});

const actions = ref([
  { name: "预览" },
  { name: "下载" },
  { name: "重命名", color: "rgb(79 128 181)" },
  { name: "删除", color: "danger" },
]);

// 进入子目录
const enterDirectory = (folder) => {
  if (folder.type !== 'dir') return;
  // 更新当前目录ID
  currentFolderId.value = folder.id;
  // 更新面包屑
  routes.value.push({
    id: folder.id,
    name: folder.name,
  });
};

// 面包屑导航点击
const handleBreadcrumbClick = (index) => {
  if (index >= routes.value.length - 1) return;
  // 更新面包屑路径
  routes.value = routes.value.slice(0, index + 1);
  // 更新当前目录ID
  currentFolderId.value = routes.value[index].id;
};

// 弹出客户文件下拉
const handeOpenClick = (item) => {
  currentFile.value = item;
  operationFileShow.value = true;
};

// 点击选项时触发，禁用或加载状态下不会触发
const onFileSelect = (item, index) => {
  if (!currentFile.value) return;
  switch (item.name) {
    case "预览":
      previewFile(currentFile.value);
      operationFileShow.value = false;
      break;
    case "下载":
      downloadFile(currentFile.value);
      operationFileShow.value = false;
      break;
    case "删除":
      deleteFile(currentFile.value);
      operationFileShow.value = false;
      break;
    case "重命名":
      setRenameFile(currentFile.value);
      operationFileShow.value = false;
      break;
  }
};

/**
 * 下载文件 - 优化版本，支持所有文件格式真正下载
 * @param {Object} file - 文件对象
 */
 const downloadFile = async (file) => {
  console.log('📥 开始下载文件:', file.name, file.url);
  // 防重复下载机制
  const downloadKey = `${file.url}_${file.name}`.replace(/[^a-zA-Z0-9]/g, '_');
  if (window.downloadingFiles && window.downloadingFiles.has(downloadKey)) {
    console.log('⚠️ 文件正在下载中，跳过重复请求');
    uni.showToast({
      title: '文件正在下载中...',
      icon: 'none'
    });
    return;
  }
  // 初始化下载状态管理
  if (!window.downloadingFiles) {
    window.downloadingFiles = new Set();
  }
  window.downloadingFiles.add(downloadKey);
  // 设置超时清理，防止状态永久锁定
  const timeoutId = setTimeout(() => {
    window.downloadingFiles.delete(downloadKey);
    console.log('🧹 下载状态超时清理:', downloadKey);
  }, 10000); // 10秒超时
  try {
    // #ifdef H5
    try {
      // 显示下载提示
      uni.showLoading({
        title: '下载中...',
        mask: true
      });
      // 方法1：使用fetch + Blob强制下载（支持所有文件格式）
      const response = await fetch(file.url, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Disposition': 'attachment', // 强制下载
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const blob = await response.blob();
      console.log('📦 获取到文件Blob:', blob.type, blob.size);
      // 获取正确的MIME类型，但强制设置为下载类型
      let mimeType = getMimeType(file.name);
      // 对于容易被浏览器预览的文件类型，强制设置为下载类型
      const previewTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'text/plain', 'text/html'];
      if (previewTypes.includes(mimeType) || previewTypes.includes(blob.type)) {
        mimeType = 'application/octet-stream'; // 强制下载
      }
      // 创建新的Blob对象，使用强制下载的MIME类型
      const downloadBlob = new Blob([blob], { type: mimeType });
      // 创建下载链接
      const blobUrl = URL.createObjectURL(downloadBlob);
      const link = document.createElement('a');
      // 设置下载属性 - 关键是这些属性的组合
      link.href = blobUrl;
      link.download = file.name || '下载文件'; // 强制指定文件名
      link.style.display = 'none';
      link.target = '_self'; // 在当前窗口下载
      link.rel = 'noopener noreferrer';
      // 添加到DOM
      document.body.appendChild(link);
      // 触发下载 - 针对iOS Safari优化，避免重复触发
      setTimeout(() => {
        // 检测是否为iOS Safari
        const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        if (isIOSSafari) {
          // iOS Safari：只使用click方法，避免重复触发
          link.click();
          console.log('✅ iOS Safari下载链接已触发');
        } else {
          // 其他浏览器：使用click方法，如果失败则尝试事件触发
          try {
            link.click();
            console.log('✅ 下载链接已触发 - click方法');
          } catch (clickError) {
            console.warn('⚠️ click方法失败，尝试事件触发:', clickError);
            // 只有在click失败时才使用事件触发
            setTimeout(() => {
              const event = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
              });
              link.dispatchEvent(event);
              console.log('✅ 下载链接已触发 - 事件方法');
            }, 50);
          }
        }
        // 清理资源
        setTimeout(() => {
          if (document.body.contains(link)) {
            document.body.removeChild(link);
          }
          URL.revokeObjectURL(blobUrl);
          console.log('🧹 下载资源已清理');
        }, 200);
      }, 10);
      // 隐藏加载提示
      uni.hideLoading();
      // 显示成功提示
      uni.showToast({
        title: '下载已开始',
        icon: 'success',
        duration: 2000
      });
    } catch (error) {
      console.error('❌ 主要下载方法失败:', error);
      uni.hideLoading();
      // 回退方法1：使用更强制的下载方式
      try {
        console.log('🔄 尝试回退方法1: 强制下载链接');
        // 创建一个临时的a标签，使用更强制的方式
        const link = document.createElement('a');
        // 构造带有强制下载参数的URL
        const downloadUrl = file.url + (file.url.includes('?') ? '&' : '?') + 'download=1&attachment=1';
        link.href = downloadUrl;
        link.download = file.name || '下载文件';
        link.style.display = 'none';
        link.target = '_blank'; // 在新窗口中下载
        // 设置强制下载的属性
        link.setAttribute('download', file.name || '下载文件');
        link.setAttribute('type', 'application/octet-stream');
        document.body.appendChild(link);
        // 触发下载
        link.click();
        setTimeout(() => {
          if (document.body.contains(link)) {
            document.body.removeChild(link);
          }
        }, 100);
        uni.showToast({
          title: '正在尝试下载...',
          icon: 'none',
          duration: 2000
        });
      } catch (fallbackError) {
        console.error('❌ 回退方法1也失败:', fallbackError);
        // 回退方法2：使用iframe强制下载
        try {
          console.log('🔄 尝试回退方法2: iframe强制下载');
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          iframe.style.width = '0';
          iframe.style.height = '0';
          // 构造强制下载的URL
          const downloadUrl = file.url + (file.url.includes('?') ? '&' : '?') + 'download=1&attachment=1&force=1';
          iframe.src = downloadUrl;
          iframe.onload = () => {
            setTimeout(() => {
              if (document.body.contains(iframe)) {
                document.body.removeChild(iframe);
              }
            }, 2000);
          };
          iframe.onerror = () => {
            if (document.body.contains(iframe)) {
              document.body.removeChild(iframe);
            }
          };
          document.body.appendChild(iframe);
          uni.showToast({
            title: '正在尝试下载...',
            icon: 'none',
            duration: 2000
          });
        } catch (iframeError) {
          console.error('❌ 回退方法2也失败:', iframeError);
          // 最后的回退：提示用户右键下载
          uni.showModal({
            title: '下载提示',
            content: '自动下载失败，建议您右键点击链接选择"另存为"进行下载，是否在新窗口中打开文件？',
            confirmText: '打开',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 在新窗口打开，用户可以右键另存为
                const newWindow = window.open(file.url, '_blank');
                if (!newWindow) {
                  uni.showToast({
                    title: '请允许弹窗后重试',
                    icon: 'none'
                  });
                }
              }
            }
          });
        }
      }
    }
    // #endif

    // #ifdef MP-WEIXIN || APP
    uni.showLoading({
      title: '下载中...',
      mask: true
    });
    
    uni.downloadFile({
      url: file.url,
      success: (res) => {
        if (res.statusCode === 200) {
          // #ifdef MP-WEIXIN
          uni.saveFile({
            tempFilePath: res.tempFilePath,
            success: (saveRes) => {
              uni.showToast({
                title: "下载成功",
                icon: "success",
              });
            },
            fail: (err) => {
              console.error('保存文件失败:', err);
              uni.showToast({
                title: "保存失败",
                icon: "none",
              });
            }
          });
          // #endif
          
          // #ifdef APP
          plus.gallery.save(
            res.tempFilePath,
            () => {
              uni.showToast({
                title: "已保存到相册",
                icon: "success",
              });
            },
            (err) => {
              console.error('保存到相册失败:', err);
              uni.showToast({
                title: "保存失败",
                icon: "none",
              });
            }
          );
          // #endif
        } else {
          uni.showToast({
            title: "下载失败",
            icon: "none",
          });
        }
      },
      fail: (err) => {
        console.error('下载失败:', err);
        uni.showToast({
          title: "下载失败",
          icon: "none",
        });
      },
      complete: () => {
        uni.hideLoading();
      },
    });
    // #endif
  } catch (globalError) {
    console.error('❌ 下载函数全局错误:', globalError);
    uni.hideLoading();
  } finally {
    // 清理下载状态
    clearTimeout(timeoutId);
    if (window.downloadingFiles) {
      window.downloadingFiles.delete(downloadKey);
    }
    console.log('🧹 下载状态已清理:', downloadKey);
  }
};

// 删除文件
const deleteFile = async (delItem) => {
  try {
    delItem.isShowInput = false;
    const res = await deleteAttachment({
      id: delItem.id,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除文件成功",
        icon: "success",
      });
      emit("refresh");
    }
  } catch (error) {
    console.error("删除失败:", error);
  }
};

// 重命名
const setRenameFile = async (nameItem) => {
  nameItem.isShowInput = true;
};

// 输入框修改事件
const handelInputSetName = async (item) => {
  item.isShowInput = false;
  console.error("输入框修改事件", item);
  try {
    const res = await saveOrEditAttachment({
      id: item.id,
      classify: 4,
      name: item.name,
    });
    if (res.code === 0) {
      emit("refresh");
    }
  } catch (error) {
    console.error("重命名请求失败:", error);
  }
};
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.recently-details-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .todo-card {
    margin: 15px;
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;

    .todo-item {
      display: flex;
      align-items: center;
      margin: 10px 14px 0 14px;
      height: 56px; /* 设置固定高度为56px */
      max-height: 56px;
      
      /* 文件缩略图/图标区域 */
      .file-thumbnail {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        
        .file-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .flie-icon {
          width: 24px;
          height: 24px;
        }
      }
      .content-wrapper {
        flex: 1;
        margin-left: 6px;
        overflow: hidden;
      }
      .todo-title {
        color: var(---, #000);
        font-size: 14px;
        white-space: nowrap; /* 确保文本不换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        text-overflow: ellipsis; /* 显示省略号 */
        max-width: 100%; /* 确保宽度不超过父容器 */
      }
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2px;
        color: var(---, #adb1ba);
        font-size: 10px;
        .upload-time {
          flex: 1;
        }
        .file-size {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
