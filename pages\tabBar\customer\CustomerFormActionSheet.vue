<template>
  <!-- 【新建】客户-->
  <ActionSheet
    v-model:show="showForm"
    :custom-panel="true"
    :close-on-click-mask="false"
    @cancel="handleCancel"
  >
    <template #custom-panel>
      <!-- 顶部操作栏 -->
      <view class="form-header">
        <view class="form-header-btn" @click="handleCancel">取消</view>
        <view class="form-header-title">{{ title }}</view>
         <!-- :class="{ 'confirm-btn--disabled': isSubmitting }" -->
        <view
          class="form-header-btn confirm-btn"
          @click="handleSubmit"
        >
          {{ confirmText }}
        </view>
      </view>

      <!-- 表单内容 -->
      <scroll-view scroll-y class="form-content">
        <uni-forms
          class="task-form"
          ref="formRef"
          :model-value="formData"
          :rules="rules"
          validate-trigger="submit"
          label-width="75px"
        >
          <uni-forms-item label="公司简称" name="company_short_name">
            <uni-easyinput
              v-model="formData.company_short_name"
              placeholder="请输入公司简称"
              maxlength="50"
            />
          </uni-forms-item>
          <uni-forms-item label="公司全称" name="company_name">
            <uni-easyinput
              v-model="formData.company_name"
              placeholder="请输入公司全称"
              maxlength="50"
            />
          </uni-forms-item>
          <uni-forms-item label="客户级别" name="customer_level">
            <uni-data-select
              v-model="formData.customer_level"
              :localdata="levels"
              placeholder="请选择客户级别"
            />
          </uni-forms-item>
          <uni-forms-item label="客户备注" name="notes">
            <uni-easyinput
              v-model="formData.notes"
              placeholder="请输入客户备注"
              type="textarea"
              maxlength="150"
            />
          </uni-forms-item>
          <uni-forms-item label="联系人" name="contact_name">
            <uni-easyinput
              v-model="formData.contact_name"
              placeholder="请输入联系人"
              maxlength="50"
            />
          </uni-forms-item>
          <uni-forms-item label="职位" name="contact_title">
            <uni-easyinput
              v-model="formData.contact_title"
              placeholder="请输入职位"
              maxlength="50"
            />
          </uni-forms-item>
          <uni-forms-item label="手机" name="phone">
            <uni-easyinput
              v-model="formData.phone"
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
            />
          </uni-forms-item>
          <uni-forms-item label="联系人备注" name="contract_remark" label-width="90">
            <uni-easyinput
              v-model="formData.contract_remark"
              placeholder="请输入备注"
              type="textarea"
            />
          </uni-forms-item>
        </uni-forms>
      </scroll-view>
    </template>
  </ActionSheet>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";

const props = defineProps({
  // 是否显示
  show: Boolean,
  // 标题
  title: {
    type: String,
    default: "新增客户",
  },
  // 确认按钮文字
  confirmText: {
    type: String,
    default: "确定",
  },
  // 初始表单数据
  initialData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:show", "submit", "cancel"]);

const showForm = ref(props.show);
const isSubmitting = ref(false);
const formRef = ref(null);

// 客户级别选项
const levels = ref([
  { value: 10, text: "潜在客户" },
  { value: 20, text: "普通客户" },
  { value: 30, text: "重点客户" },
]);

// 表单数据
const formData = reactive({
  company_name: "",
  company_short_name: "",
  contact_name: "",
  contact_title: "",
  phone: "",
  contract_remark: "",
  notes: "",
  customer_level: 0,
  ...props.initialData,
});

// 验证规则
const rules = reactive({
  // company_name: {
  //   rules: [{ required: true, errorMessage: "请输入公司全称" }],
  // },
  company_short_name: {
    rules: [{ required: true, errorMessage: "至少填写公司简称或联系人" }],
  },
  // customer_level: {
  //   rules: [{ required: true, errorMessage: "请选择客户级别" }],
  // },
  contact_name: {
    rules: [{ required: true, errorMessage: "至少填写公司简称或联系人" }],
  },
  // phone: {
  //   rules: [
  //     { required: true, errorMessage: "请输入手机号" },
  //     { pattern: /^1[3-9]\d{9}$/, errorMessage: "手机号格式不正确" },
  //   ],
  // },
});


// 监听show变化
watch(
  () => props.show,
  (val) => {
    showForm.value = val;
  }
);

// 监听组件内部show变化
watch(showForm, (val) => {
  emit("update:show", val);
  if (!val) {
    resetForm();
  }
});

// 监听字段变化，动态修改校验规则
watch(
  () => [formData.company_short_name, formData.contact_name],
  ([companyShort, contactName]) => {
    // 规则1: 如果公司简称有值，联系人校验失效
    if (companyShort) {
      rules.contact_name.rules[0].required = false;
    } else {
      rules.contact_name.rules[0].required = !contactName; // 如果联系人也没填，恢复必填
    }

    // 规则2: 如果联系人有值，公司简称校验失效
    if (contactName) {
      rules.company_short_name.rules[0].required = false;
    } else {
      rules.company_short_name.rules[0].required = !companyShort; // 如果公司简称也没填，恢复必填
    }
    // 规则3: 如果两个都没填，恢复两者必填（通过上述逻辑自动处理）
  },
  { deep: true }
);

// 提交表单
const handleSubmit = async () => {
  if (isSubmitting.value) return;

  isSubmitting.value = true;
  try {
    // 验证表单
    const validate = await formRef.value.validate();
    // 如果公司简称为空 填写了联系人则简称=联系人
    if (validate.company_short_name === '') {
      validate.company_short_name = validate.contact_name
    }
    // 验证通过，提交数据
    emit("submit", { ...validate });
    showForm.value = false;
  } catch (err) {
    console.log("表单验证失败:", err);
  } finally {
    isSubmitting.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit("cancel");
  showForm.value = false;
};

// 重置表单
const resetForm = () => {
  formRef.value.clearValidate();
  Object.assign(formData, {
    title: "",
    company_name: "",
    company_short_name: "",
    customer_level: 0,
    contact_name: "",
    contact_title: "",
    phone: "",
    contract_remark: "",
    ...props.initialData,
  });
};

// 暴露方法
defineExpose({
  show: () => {
    showForm.value = true;
  },
  close: () => {
    showForm.value = false;
  },
  resetForm,
});
</script>

<style scoped lang="scss">
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
}

.form-header-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.form-header-btn {
  font-size: 14px;
  color: #666;
  padding: 5px 10px;
}

.confirm-btn {
  color: #007aff;

  &--disabled {
    opacity: 0.5;
  }
}

.form-content {
  max-height: 80vh;
  .task-form {
    padding: 10px 15px;
    :deep(.uni-forms-item__label) {
      height: 35px;
      color: var(---, #787d86);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 35px;
      font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
    }
  }
}
</style>
