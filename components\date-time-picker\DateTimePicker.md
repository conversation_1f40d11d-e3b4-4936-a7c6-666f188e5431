# DateTimePicker 日期时间选择器组件文档

## 介绍

DateTimePicker 是一个基于 Vue3 和 uni-app 开发的日期时间选择器组件，类似于 Vant UI 的 DatetimePicker。该组件支持多种日期时间选择模式，提供了丰富的自定义选项。

## 安装

组件已内置在项目中，可直接使用：

```vue
<script setup>
import DateTimePicker from "@/components/DateTimePicker/DateTimePicker.vue";
</script>
```

## 基础用法

### 选择日期

```vue
<DateTimePicker v-model="date" type="date" />
```

### 选择时间

```vue
<DateTimePicker v-model="time" type="time" />
```

### 选择完整日期时间

```vue
<DateTimePicker v-model="datetime" type="datetime" />
```

### 选择年月

```vue
<DateTimePicker v-model="yearMonth" type="year-month" />
```

## API

### Props 属性

| 属性名      | 说明                 | 类型                  | 默认值                            |
|------------|---------------------|----------------------|----------------------------------|
| modelValue | 绑定值               | String/Number/Date   | null                             |
| placeholder | 占位符文本           | String               | "请选择日期时间"                    |
| type       | 选择器类型           | String               | "datetime"                       |
| minDate    | 可选的最小日期        | Date/String/Number   | 当前年份往前10年的1月1日             |
| maxDate    | 可选的最大日期        | Date/String/Number   | 当前年份往后10年的12月31日           |
| formatter  | 格式化函数           | Function             | 默认添加中文单位（年、月、日等）      |
| filter     | 选项过滤函数         | Function             | null                             |
| showClear  | 是否显示清除按钮      | Boolean              | true                             |
| format     | 输出的日期格式        | String               | 根据type自动选择格式               |
| timeOnly   | 是否只显示时间部分     | Boolean              | false                            |

#### type 可选值

| 值         | 说明           |
|------------|--------------|
| date       | 日期（年月日）  |
| year-month | 年月          |
| month-day  | 月日          |
| time       | 时间（时分）    |
| datetime   | 日期时间（年月日时分）|
| datehour   | 日期和小时（年月日时）|

#### format 格式化模板

不传则根据`type`自动选择格式。可用的格式化占位符：

- `YYYY`: 四位年份
- `YY`: 两位年份
- `MM`: 两位月份（带前导零）
- `M`: 一位月份（不带前导零）
- `DD`: 两位日期（带前导零）
- `D`: 一位日期（不带前导零）
- `HH`: 两位小时（带前导零）
- `H`: 一位小时（不带前导零）
- `mm`: 两位分钟（带前导零）
- `m`: 一位分钟（不带前导零）
- `ss`: 两位秒数（带前导零）
- `s`: 一位秒数（不带前导零）

### Events 事件

| 事件名            | 说明                | 回调参数              |
|------------------|--------------------|--------------------|
| update:modelValue | 值变化时触发         | 格式化后的日期字符串    |
| change           | 值变化时触发         | 格式化后的日期字符串    |
| confirm          | 点击确认按钮时触发    | 格式化后的日期字符串    |
| cancel           | 点击取消按钮时触发    | -                   |
| click            | 点击选择器时触发      | -                   |

### Methods 方法

通过ref可以获取到组件实例并调用实例方法

| 方法名  | 说明          | 参数  |
|--------|--------------|------|
| open   | 打开选择器     | -    |
| close  | 关闭选择器     | -    |
| clear  | 清除选择的值   | -    |

## 代码示例

### 基础用法示例

```vue
<template>
  <view>
    <view class="item">
      <text class="label">日期选择</text>
      <DateTimePicker v-model="date" type="date" @change="onChange" />
    </view>
    
    <view class="item">
      <text class="label">时间选择</text>
      <DateTimePicker v-model="time" type="time" @change="onChange" />
    </view>
    
    <view class="item">
      <text class="label">日期时间选择</text>
      <DateTimePicker v-model="datetime" type="datetime" @change="onChange" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import DateTimePicker from '@/components/DateTimePicker/DateTimePicker.vue';

const date = ref('');
const time = ref('');
const datetime = ref('');

const onChange = (value) => {
  console.log('选择的值:', value);
};
</script>
```

### 自定义日期范围

```vue
<template>
  <DateTimePicker 
    v-model="date" 
    type="date" 
    :min-date="minDate" 
    :max-date="maxDate" 
  />
</template>

<script setup>
import { ref } from 'vue';
import DateTimePicker from '@/components/DateTimePicker/DateTimePicker.vue';

const date = ref('');
const minDate = new Date(2020, 0, 1); // 2020年1月1日
const maxDate = new Date(2025, 11, 31); // 2025年12月31日
</script>
```

### 自定义格式化输出

```vue
<template>
  <DateTimePicker 
    v-model="date" 
    type="datetime" 
    format="YYYY年MM月DD日 HH时mm分" 
  />
</template>

<script setup>
import { ref } from 'vue';
import DateTimePicker from '@/components/DateTimePicker/DateTimePicker.vue';

const date = ref('');
</script>
```

### 在待办系统中的应用

```vue
<template>
  <uni-forms-item>
    <template #label>
      <view class="task-label">截止时间</view>
    </template>
    <view class="item-box">
      <DateTimePicker v-model="formData.due_date" @change="handleDateChange" />
    </view>
  </uni-forms-item>
  <uni-forms-item>
    <template #label>
      <view class="task-label">提醒时间</view>
    </template>
    <view class="item-box">
      <DateTimePicker v-model="formData.notify_time" @change="handleDateChange" :timeOnly="timeOnly"/>
    </view>
  </uni-forms-item>
</template>
```

## 注意事项

1. 当使用`timeOnly`属性时，会只显示时间部分。这适用于重复提醒等场景。

2. 在使用`v-model`绑定数据时，请确保在更新服务器数据前使用正确的日期格式。如果服务器返回格式为"2025-05-15 00:00:00"，组件可正确解析显示。

3. 为使用自定义格式化功能，可通过`formatter`属性传入自定义函数。 