{"version": 3, "mappings": ";;;;;;yCASK,MAACA,EAAkB,CAEtBC,SAAU,uCACVC,YAAa,uBAGbC,WAAY,SACZC,QAAS,2DAmBTC,eAAgB,CAEdC,IAAO,WACPC,KAAQ,WACRC,IAAO,cACPC,KAAQ,cACRC,IAAO,eACPC,KAAQ,eAGRC,IAAO,MACPC,IAAO,MAGPC,IAAO,OACPC,IAAO,OACPC,IAAO,OACPC,KAAQ,OACRC,IAAO,OAGPC,IAAO,UACPC,IAAO,UACPC,GAAM,OACNC,IAAO,QAITC,WAAY,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,OAAQ,MAAO,QAe3D,SAASC,EAAYC,SAC1B,IAAKA,EAAiB,SAEtB,MAAMC,EAAY,OAAAC,EAASF,EAAAG,MAAM,KAAKC,YAAO,EAAAF,EAAAG,cAC7C,OAAOJ,GAAa1B,EAAgBuB,WAAWQ,SAASL,EAC1D,CAOO,SAASM,EAAuBP,SACrC,IAAKA,EAAiB,SAGtB,GAAID,EAAYC,GAAkB,SAElC,MAAMC,EAAY,OAAAC,EAASF,EAAAG,MAAM,KAAKC,YAAO,EAAAF,EAAAG,cAE7C,OAAOJ,GAAa1B,EAAgBK,eAAe4B,eAAeP,EACpE,CAOO,SAASQ,EAAiBT,SAC/B,IAAKA,EAAiB,iBAEtB,MAAMC,EAAY,OAAAC,EAASF,EAAAG,MAAM,KAAKC,YAAO,EAAAF,EAAAG,cACtC,OAAA9B,EAAgBK,eAAeqB,IAAc,UACtD,CAMO,SAASS,IACd,OAAO,IAAIC,SAAQ,CAACC,EAASC,KAE3B,GAAIC,OAAOC,QAAUD,OAAOC,OAAOC,OAGjC,OAFAC,QAAQpB,IAAI,2BACZe,EAAQE,OAAOC,QAOX,MAAAG,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAM9C,EAAgBI,QAC7BuC,EAAOI,OAAQ,EAEfJ,EAAOK,OAAS,KACdN,QAAQpB,IAAI,8BAERiB,OAAOC,QAA0C,mBAAzBD,OAAOC,OAAOC,QACxCC,QAAQpB,IAAI,yBACZe,EAAQE,OAAOC,UAEfE,QAAQO,MAAM,wBACPX,EAAA,IAAIY,MAAM,2CAClB,EAGHP,EAAOQ,QAAU,KACRb,EAAA,IAAIY,MAAM,kCAAiC,EAG3CN,SAAAQ,KAAKC,YAAYV,EAAM,GAQpC,CAOOI,eAAeO,EAAuBC,GACvC,IAEI,MAAAC,gBAAEA,SAA0BC,GAAA,IAAAC,OAAO,kCAAuB,4BAEhEhB,QAAQpB,IAAI,2BAA4B,CACtCqC,GAAIJ,EAASI,IAAMJ,EAASK,cAC5BC,KAAMN,EAASM,OAIX,MAAAC,QAAiBN,EAAgB,CACrCG,GAAIJ,EAASI,IAAMJ,EAASK,gBAK9B,GAFQlB,QAAApB,IAAI,oBAAqBwC,GAEX,IAAlBA,EAASC,MAAcD,EAASE,KAAM,CAExC,MAAMC,EAAY,CAChBC,YAAaJ,EAASE,KAAKG,MAC3BC,aAAcN,EAASE,KAAKK,KAIvB,OADC3B,QAAApB,IAAI,qBAAsB2C,GAC3BA,CACb,CACM,MAAM,IAAIf,MAAMY,EAASQ,KAAO,WAanC,OAXQrB,GAIP,MAHQP,QAAAO,MAAM,qBAAsBA,GAGhCA,EAAMsB,SAAWtB,EAAMsB,QAAQxC,SAAS,gBACpC,IAAImB,MAAM,mBACPD,EAAMsB,SAAWtB,EAAMsB,QAAQxC,SAAS,WAC3C,IAAImB,MAAM,cAEV,IAAIA,MAAMD,EAAMsB,SAAW,iBAEpC,CACH,CAQA,SAASC,EAAiBC,GACxB,OAAKA,EAGsB,IAAvBA,EAAUC,UAAmD,mBAA1BD,EAAUpB,YACxCoB,EAILA,EAAUE,OAAsC,IAA7BF,EAAUE,MAAMD,SAC9BD,EAAUE,MAIfF,EAAUG,KAAkC,IAA3BH,EAAUG,IAAIF,SAC1BD,EAAUG,IAIM,iBAAdH,EACF7B,SAASiC,cAAcJ,IAGhC/B,QAAQoC,KAAK,uBAAwBL,EAAWA,GACzC,MAvBgB,IAwBzB,CAQA,SAASM,EAAkBN,EAAWO,GAC9B,MAAAC,EAAeT,EAAiBC,GAEtC,IAAKQ,GAAsD,mBAA/BA,EAAaJ,cAEhC,OADCnC,QAAAoC,KAAK,gCAAiCG,GACvC,KAGL,IACK,OAAAA,EAAaJ,cAAcG,EAInC,OAHQ/B,GAEA,OADCP,QAAAO,MAAM,uBAAwBA,GAC/B,IACR,CACH,CAKK,MAACiC,EAAiB,CAErBC,WAEE,MAAMC,EAAYC,UAAUD,UAC5B,MAAO,SAASE,KAAKF,KAAe,SAASE,KAAKF,KAAe,OAAOE,KAAKF,EAK9E,EAGDG,SAAW,IAEF,iEAAiED,KAAKD,UAAUD,WAQzFI,uBACE,OAAOC,KAAKN,YAAcM,KAAKF,UAChC,GAMGG,EAAqB,CAEzBC,cAAgB,IACP,mNAWTC,iBAAmB,IACV,6QAYTC,YAAYC,GACV,IAAKA,EAAQ,OAET,IAAAC,EAASN,KAAKE,gBAEdT,EAAeM,yBACjBO,GAAUN,KAAKG,oBAGjBE,EAAOE,MAAMC,QAAUF,CACxB,EAGDG,qBAAqBzB,GACdA,GAEDS,EAAeM,yBACjBf,EAAUuB,MAAMC,SAAW,8LAO9B,GAWIlD,eAAeoD,EAAwBC,GAC5C,MAAM3B,UAAEA,EAAAlB,SAAWA,EAAUU,aAAcmC,EAEvC,IAEI,MAAA5D,QAAeL,IACrBO,QAAQpB,IAAI,yBAGN,MAAA2D,EAAeT,EAAiBC,GAGtC,IAAKQ,GAAoD,mBAA7BA,EAAa5B,YACvC,MAAM,IAAIH,MAAM,8BAAgC+B,GAGlDvC,QAAQpB,IAAI,gBAAiB,CAC3B+E,QAASpB,EAAaoB,QACtB1C,GAAIsB,EAAatB,GACjB2C,UAAWrB,EAAaqB,UACxBd,qBAAsBN,EAAeM,yBAIvCP,EAAasB,UAAY,GACzBb,EAAmBQ,qBAAqBjB,GAExCvC,QAAQpB,IAAI,sBAGZ,MAAMmB,EAAS,CACb+D,MAAOvB,EACPZ,IAAKJ,EAAUG,cAIbH,EAAUC,cACZxB,QAAQpB,IAAI,wBACZmB,EAAOgE,aAAe,KACpB/D,QAAQpB,IAAI,sBACLc,QAAQC,QAAQ,CACrB8B,MAAOF,EAAUC,YACjBwC,QAAS,SAMfhE,QAAQpB,IAAI,0BACN,MAAAqF,EAAWnE,EAAOC,OAAOA,GAG/B,IAAKkE,EACG,UAAIzD,MAAM,iDAIlB,GAAIe,EAAUC,aAA4C,mBAAtByC,EAASC,SAAyB,CACpElE,QAAQpB,IAAI,aACR,IACFqF,EAASC,SAAS,CAChBzC,MAAOF,EAAUC,YACjBwC,QAAS,OAEXhE,QAAQpB,IAAI,aAIb,OAHQuF,GACCnE,QAAAoC,KAAK,eAAgB+B,EAE9B,CACF,CA0BM,MAvBuB,mBAAnBF,EAASG,QAClBpE,QAAQpB,IAAI,gCACNqF,EAASG,QACfpE,QAAQpB,IAAI,sBAIdyF,YAAW,KACH,MAAAjB,EAASf,EAAkBE,EAAc,UAC3Ca,GACFpD,QAAQpB,IAAI,iBACZoE,EAAmBG,YAAYC,GAG/BvD,OAAOyE,cAAc,IAAIC,MAAM,WAE/BvE,QAAQpB,IAAI,oBAEZoB,QAAQoC,KAAK,iBACd,GACA,KAEHpC,QAAQpB,IAAI,qBACLqF,CAKR,OAHQ1D,GAED,MADEP,QAAAO,MAAM,qBAAsBA,GAC9BA,CACP,CACH,CAKO,MAAMiE,EACXC,YAAY1C,GAEJ,MAAAQ,EAAeT,EAAiBC,GAGtC,IAAKQ,GAAoD,mBAA7BA,EAAa5B,YACvC,MAAM,IAAIH,MAAM,8CAAgD+B,GAGlEQ,KAAKhB,UAAYQ,EACjBQ,KAAKkB,SAAW,KAChBlB,KAAK2B,SAAU,CAChB,CAMDrE,WAAWQ,GACL,IAEE,IAACA,EAASI,GACN,UAAIT,MAAM,YAGd,IAACK,EAASM,KACN,UAAIX,MAAM,WAOlB,GAHAuC,KAAK4B,YAAY,gBAGZrF,EAAuBuB,EAASM,MACnC,MAAM,IAAIX,MAAM,aAAaK,EAASM,QAIxC4B,KAAK4B,YAAY,eACX,MAAApD,QAAkBX,EAAuBC,GAG/CkC,KAAK4B,YAAY,aACZ5B,KAAAkB,eAAiBR,EAAwB,CAC5C1B,UAAWgB,KAAKhB,UAChBlB,WACAU,cAIFwB,KAAK6B,sBAEL7B,KAAK2B,SAAU,EACf3B,KAAK8B,cAGL9B,KAAK+B,gBAEL9E,QAAQpB,IAAI,qBAMb,OAJQ2B,GAGD,MAFEP,QAAAO,MAAM,sBAAuBA,GAChCwC,KAAAgC,UAAUxE,EAAMsB,SACftB,CACP,CACF,CAKDqE,sBACO7B,KAAKkB,UAGHpE,OAAAmF,iBAAiB,WAAYC,IAC9B,IACF,MAAM3D,EAAO4D,KAAKC,MAAMF,EAAM3D,MAE9B,OAAQA,EAAK8D,QACX,IAAK,eACKpF,QAAApB,IAAI,YAAa0C,EAAKA,MAC9B,MAEF,IAAK,gBACHtB,QAAQpB,IAAI,WAAY0C,EAAKA,KAAK+D,WAClC,MAEF,IAAK,gBACKrF,QAAAO,MAAM,iBAAkBe,EAAKA,MAChCyB,KAAAuC,YAAYhE,EAAKA,MAK3B,OAFQiE,GAER,IAEJ,CAMDD,YAAYE,GACV,MASM3D,EATgB,CACpB4D,mBAAsB,SACtBC,oBAAuB,WACvBC,iBAAoB,OACpBC,wBAA2B,OAC3BC,qBAAwB,OACxBC,sBAAyB,QAGGN,EAAUO,SAAW,OACnDhD,KAAKgC,UAAUlD,EAChB,CAKDiD,cAAckB,EAAa,GAGzB3B,YAAW,KACT,MAAMjB,EAASf,EAAkBU,KAAKhB,UAAW,UAE7CqB,GACFpD,QAAQpB,IAAI,mBAAmBoH,EAAa,OAG5ChD,EAAmBG,YAAYC,GAG3B4C,EAZW,GAYgB5C,EAAO6C,aAAe,KACnDjG,QAAQpB,IAAI,oBAAoBoH,EAAa,QACxCjD,KAAA+B,cAAckB,EAAa,IAEhChG,QAAQpB,IAAI,mBAELoH,EAlBM,IAmBfhG,QAAQpB,IAAI,mBAAmBoH,EAAa,QACvCjD,KAAA+B,cAAckB,EAAa,GACjC,GACA,IAAmB,IAAbA,EACV,CAMDrB,YAAY9C,EAAU,UACpBkB,KAAKhB,UAAU8B,UAAY,2HAGKhC,6BAGjC,CAKDgD,cACE,MAAMqB,EAAU7D,EAAkBU,KAAKhB,UAAW,sBAC9CmE,GACFA,EAAQC,QAEX,CAMDpB,UAAUlD,GACRkB,KAAKhB,UAAU8B,UAAY,uHAGMhC,gFAIlC,CAKDuE,UACUpG,QAAAO,MAAM,kBAAkBwC,MACV,OAAlBA,KAAKkB,WAGPlB,KAAKkB,SAASmC,UAGhBrD,KAAK2B,SAAU,EAGX3B,KAAKhB,YACPgB,KAAKhB,UAAU8B,UAAY,IAE9B,EASIxD,eAAegG,EAAuBtE,EAAWlB,GAChD,MAAAyF,EAAU,IAAI9B,EAAiBzC,GAE9B,aADDuE,EAAQC,KAAK1F,GACZyF,CACT", "names": ["webOfficeConfig", "endpoint", "projectName", "sdkVersion", "sdkPath", "supportedTypes", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "ofd", "txt", "rtf", "csv", "json", "xml", "zip", "rar", "md", "log", "imageTypes", "isImageFile", "fileName", "extension", "_a", "split", "pop", "toLowerCase", "includes", "isSupportedByWebOffice", "hasOwnProperty", "getWebOfficeType", "loadWebOfficeSDK", "Promise", "resolve", "reject", "window", "<PERSON><PERSON><PERSON>", "config", "console", "script", "document", "createElement", "src", "async", "onload", "error", "Error", "onerror", "head", "append<PERSON><PERSON><PERSON>", "generateWebOfficeToken", "fileInfo", "getPreviewToken", "__vitePreload", "import", "id", "attachment_id", "name", "response", "code", "data", "tokenInfo", "AccessToken", "token", "WebofficeURL", "url", "msg", "message", "getSafeContainer", "container", "nodeType", "value", "$el", "querySelector", "warn", "safeQuerySelector", "selector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DeviceDetector", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "navigator", "test", "isMobile", "needsSpecialHandling", "this", "IframeStyleManager", "getBaseStyles", "getSpecialStyles", "applyStyles", "iframe", "styles", "style", "cssText", "applyContainerStyles", "createWebOfficeInstance", "options", "tagName", "className", "innerHTML", "mount", "refreshToken", "timeout", "instance", "setToken", "tokenError", "ready", "setTimeout", "dispatchEvent", "Event", "WebOfficePreview", "constructor", "isReady", "showLoading", "setupEventListeners", "hideLoading", "fixIframeSize", "showError", "addEventListener", "event", "JSON", "parse", "action", "pageIndex", "handleError", "e", "errorData", "aliyunOpenFileFail", "aliyunUnsupportFile", "aliyunRequstFail", "aliyunQueryParamInvalid", "aliyunRequestTimeout", "aliyunPasswordInvalid", "result", "retryCount", "offsetHeight", "loading", "remove", "destroy", "createWebOfficePreview", "preview", "init"], "ignoreList": [], "sources": ["../../../../../services/aliyunWebOfficeService.js"], "sourcesContent": [null], "file": "assets/services-aliyunWebOfficeService.BfOiQwJd.js"}