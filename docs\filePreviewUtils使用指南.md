# filePreviewUtils 使用指南

## 🎯 **模块概述**

`filePreviewUtils.js` 是一个通用的文件预览工具模块，提供跨平台的文件预览功能，支持H5、APP、小程序等多种环境。

## 📦 **模块特性**

### **1. 跨平台支持**
- ✅ **H5平台**：WebOffice预览 + 浏览器打开
- ✅ **APP平台**：原生文档预览 + 系统下载
- ✅ **小程序平台**：wx.openDocument + 降级处理
- ✅ **其他平台**：智能降级处理

### **2. 多种文件格式**
- ✅ **图片文件**：jpg, jpeg, png, gif, bmp, webp, svg
- ✅ **文档文件**：doc, docx, xls, xlsx, ppt, pptx, pdf
- ✅ **其他格式**：智能降级处理

### **3. 智能URL选择**
- 优先使用：`downloadSignUrl`（下载签名URL）
- 其次使用：`tempUrl`（临时URL）
- 最后使用：`url`（服务器URL）

## 🔧 **API 接口**

### **主要函数**

#### **1. previewFile(attachment, options)**
```javascript
/**
 * 通用文件预览函数
 * @param {Object} attachment - 文件对象
 * @param {Object} options - 预览选项
 * @returns {Promise} 预览结果
 */
```

#### **2. quickPreviewFile(attachment, onWebOfficePreview)**
```javascript
/**
 * 快捷预览函数 - 适用于大多数场景
 * @param {Object} attachment - 文件对象
 * @param {Function} onWebOfficePreview - WebOffice预览回调（可选）
 * @returns {Promise} 预览结果
 */
```

### **参数说明**

#### **attachment 文件对象结构**
```javascript
{
  name: "document.pdf",           // 文件名（必需）
  url: "https://...",            // 服务器URL（必需）
  tempUrl: "https://...",        // 临时URL（可选）
  downloadSignUrl: "https://...", // 下载签名URL（可选，优先使用）
  size: 1024,                    // 文件大小（可选）
  type: "application/pdf"        // 文件类型（可选）
}
```

#### **options 预览选项**
```javascript
{
  onWebOfficePreview: function(attachment) {
    // H5端WebOffice预览回调
    // 在这里调用您的WebOffice预览逻辑
  },
  onAppPreview: function(attachment) {
    // APP端自定义预览回调（可选）
    // 如果不提供，将使用默认的APP预览逻辑
  },
  onMiniProgramPreview: function(attachment) {
    // 小程序端自定义预览回调（可选）
    // 如果不提供，将使用默认的小程序预览逻辑
  }
}
```

## 📝 **使用示例**

### **示例1：基础使用（推荐）**
```javascript
import { previewFile } from '@/utils/filePreviewUtils.js';

// 在组件中使用
const handleFilePreview = async (attachment) => {
  try {
    const result = await previewFile(attachment, {
      onWebOfficePreview: openWebOfficePreview, // 您的WebOffice预览函数
    });
    console.log('预览结果:', result);
  } catch (error) {
    console.error('预览失败:', error);
  }
};
```

### **示例2：快捷使用**
```javascript
import { quickPreviewFile } from '@/utils/filePreviewUtils.js';

// 简化的使用方式
const handleFilePreview = async (attachment) => {
  const result = await quickPreviewFile(attachment, openWebOfficePreview);
};
```

### **示例3：完整配置**
```javascript
import { previewFile } from '@/utils/filePreviewUtils.js';

const handleFilePreview = async (attachment) => {
  const result = await previewFile(attachment, {
    // H5端WebOffice预览
    onWebOfficePreview: (file) => {
      console.log('启动WebOffice预览:', file.name);
      openWebOfficePreview(file);
    },
    
    // APP端自定义预览（可选）
    onAppPreview: (file) => {
      console.log('启动APP自定义预览:', file.name);
      // 您的自定义APP预览逻辑
    },
    
    // 小程序端自定义预览（可选）
    onMiniProgramPreview: (file) => {
      console.log('启动小程序自定义预览:', file.name);
      // 您的自定义小程序预览逻辑
    }
  });
};
```

### **示例4：在Vue组件中使用**
```vue
<template>
  <view @click="handlePreview">预览文件</view>
</template>

<script setup>
import { previewFile } from '@/utils/filePreviewUtils.js';

const fileData = {
  name: "report.pdf",
  url: "https://example.com/files/report.pdf",
  downloadSignUrl: "https://oss.example.com/signed-url"
};

const handlePreview = async () => {
  try {
    const result = await previewFile(fileData, {
      onWebOfficePreview: (file) => {
        // 调用您的WebOffice预览服务
        openWebOfficePreview(file);
      }
    });
    
    if (result.success) {
      console.log('预览成功:', result.message);
    } else {
      console.log('预览失败:', result.message);
    }
  } catch (error) {
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};
</script>
```

## 🔍 **返回值说明**

### **成功返回**
```javascript
{
  success: true,
  message: "预览成功" // 或其他成功信息
}
```

### **失败返回**
```javascript
{
  success: false,
  message: "具体的错误信息"
}
```

## 🎯 **平台特性说明**

### **H5平台**
- **图片预览**：使用 `uni.previewImage`
- **文档预览**：优先使用WebOffice，不支持则浏览器打开
- **降级处理**：浏览器新窗口打开

### **APP平台**
- **图片预览**：使用 `uni.previewImage`
- **文档预览**：下载到本地后使用 `plus.runtime.openFile`
- **优势**：支持所有系统支持的文件格式

### **小程序平台**
- **图片预览**：使用 `uni.previewImage`
- **文档预览**：下载到本地后使用 `wx.openDocument`
- **支持格式**：doc, docx, xls, xlsx, ppt, pptx, pdf

## ⚠️ **注意事项**

### **1. 文件对象要求**
- `name` 字段是必需的，用于判断文件类型
- 至少需要提供 `url`、`tempUrl` 或 `downloadSignUrl` 中的一个

### **2. WebOffice回调**
- H5端如果需要WebOffice预览，必须提供 `onWebOfficePreview` 回调
- 回调函数中应该调用您的WebOffice预览服务

### **3. 错误处理**
- 所有函数都会返回Promise，建议使用try-catch处理异常
- 内部已包含基础的错误提示，但建议根据返回值进行更详细的处理

### **4. 性能考虑**
- APP和小程序端会先下载文件，大文件可能需要较长时间
- 建议在下载大文件前给用户适当的提示

## 🚀 **迁移指南**

### **从fileUploadService迁移**
```javascript
// ❌ 旧的方式
import { previewFile } from '@/services/fileUploadService.js';
await previewFile(attachment, {
  onWebOfficePreview: openWebOfficePreview
});

// ✅ 新的方式
import { previewFile } from '@/utils/filePreviewUtils.js';
await previewFile(attachment, {
  onWebOfficePreview: openWebOfficePreview
});
```

### **API兼容性**
- 函数名称保持不变：`previewFile`
- 参数结构保持不变：`(attachment, options)`
- 返回值增强：现在返回详细的结果对象

## 📊 **优势对比**

### **使用公共模块的优势**
- ✅ **代码复用**：多个页面可以共享同一套预览逻辑
- ✅ **维护性好**：修改预览逻辑只需要更新一个文件
- ✅ **功能统一**：确保所有页面的预览体验一致
- ✅ **易于测试**：独立的模块更容易进行单元测试
- ✅ **扩展性强**：新增预览功能只需要修改公共模块

### **vs 内联代码**
| 特性 | 公共模块 | 内联代码 |
|------|----------|----------|
| 代码复用 | ✅ 高 | ❌ 低 |
| 维护成本 | ✅ 低 | ❌ 高 |
| 功能一致性 | ✅ 好 | ❌ 差 |
| 代码量 | ✅ 少 | ❌ 多 |
| 测试难度 | ✅ 易 | ❌ 难 |

## 🎯 **最佳实践**

### **1. 统一导入**
```javascript
// 推荐：使用命名导入
import { previewFile, quickPreviewFile } from '@/utils/filePreviewUtils.js';

// 或者：使用默认导入
import previewFile from '@/utils/filePreviewUtils.js';
```

### **2. 错误处理**
```javascript
const handlePreview = async (attachment) => {
  try {
    const result = await previewFile(attachment, options);
    if (!result.success) {
      // 根据具体错误信息进行处理
      console.warn('预览失败:', result.message);
    }
  } catch (error) {
    // 处理异常情况
    console.error('预览异常:', error);
    uni.showToast({
      title: '预览失败，请稍后重试',
      icon: 'none'
    });
  }
};
```

### **3. 回调函数复用**
```javascript
// 定义通用的WebOffice预览回调
const commonWebOfficePreview = (file) => {
  console.log('启动WebOffice预览:', file.name);
  openWebOfficePreview(file);
};

// 在多个地方复用
const handlePreview1 = (file) => previewFile(file, { onWebOfficePreview: commonWebOfficePreview });
const handlePreview2 = (file) => quickPreviewFile(file, commonWebOfficePreview);
```

## 🎉 **总结**

通过使用 `filePreviewUtils.js` 公共模块，您可以：

1. **🎯 简化代码**：从200+行代码减少到10+行代码
2. **🔧 提高维护性**：统一的预览逻辑，易于维护和扩展
3. **🚀 增强复用性**：多个页面可以共享同一套预览功能
4. **🛡️ 保证一致性**：确保所有页面的预览体验一致
5. **📱 跨平台兼容**：自动适配不同平台的预览方式

现在您可以在任何需要文件预览的页面中轻松使用这个公共模块！
