// 引用网络请求中间件
import request from '@/utils/request';

// 登录-手机号登录
export function login(data) {
    return request({
        url: '/api/login/login',
        method: 'POST',
        data
    });
}
// 参数:{
//     "mobile": ***********,
//     "code": "123",
//     "cid": "推送客户端ID"
// }

// 登录-发送验证码-
export function sendCode(data) {
    return request({
        url: '/api/login/sendCode',
        method: 'POST',
        data
    });
}
// 参数:{
//     "mobile": ***********,
// }

// 用户登出
export function logout() {
    return request({
        url: '/api/user/logout',
        method: 'POST'
    });
}

// 激活账号-验证邀请码
export function activateAccount(data) {
    return request({
        url: '/api/login/active',
        method: 'POST',
        data
    });
}
// 参数:{
//     "inviteCode": "ABC123",
//     "token": "用户登录后的临时token"
// }