/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-d31e1c47]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-d31e1c47]:before {
  content: "\e6c4";
}
.uniui-color[data-v-d31e1c47]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-d31e1c47]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-d31e1c47]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-d31e1c47]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-d31e1c47]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-d31e1c47]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-d31e1c47]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-d31e1c47]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-d31e1c47]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-d31e1c47]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-d31e1c47]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-d31e1c47]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-d31e1c47]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-d31e1c47]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-d31e1c47]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-d31e1c47]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-d31e1c47]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-d31e1c47]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-d31e1c47]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-d31e1c47]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-d31e1c47]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-d31e1c47]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-d31e1c47]:before {
  content: "\e6d4";
}
.uniui-back[data-v-d31e1c47]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-d31e1c47]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-d31e1c47]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-d31e1c47]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-d31e1c47]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-d31e1c47]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-d31e1c47]:before {
  content: "\e6d1";
}
.uniui-down[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-d31e1c47]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-d31e1c47]:before {
  content: "\e6d5";
}
.uniui-right[data-v-d31e1c47]:before {
  content: "\e6b5";
}
.uniui-up[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-top[data-v-d31e1c47]:before {
  content: "\e6b6";
}
.uniui-left[data-v-d31e1c47]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-d31e1c47]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-d31e1c47]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-d31e1c47]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-d31e1c47]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-d31e1c47]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-d31e1c47]:before {
  content: "\e649";
}
.uniui-reload[data-v-d31e1c47]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-d31e1c47]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-d31e1c47]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-d31e1c47]:before {
  content: "\e6ad";
}
.uniui-location[data-v-d31e1c47]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-d31e1c47]:before {
  content: "\e683";
}
.uniui-star[data-v-d31e1c47]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-d31e1c47]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-d31e1c47]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-d31e1c47]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-d31e1c47]:before {
  content: "\e6a2";
}
.uniui-font[data-v-d31e1c47]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-d31e1c47]:before {
  content: "\e6a4";
}
.uniui-link[data-v-d31e1c47]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-d31e1c47]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-d31e1c47]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-d31e1c47]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-d31e1c47]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-d31e1c47]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-d31e1c47]:before {
  content: "\e6ab";
}
.uniui-person[data-v-d31e1c47]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-d31e1c47]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-d31e1c47]:before {
  content: "\e69b";
}
.uniui-phone[data-v-d31e1c47]:before {
  content: "\e69c";
}
.uniui-email[data-v-d31e1c47]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-d31e1c47]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-d31e1c47]:before {
  content: "\e692";
}
.uniui-contact[data-v-d31e1c47]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-d31e1c47]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-d31e1c47]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-d31e1c47]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-d31e1c47]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-d31e1c47]:before {
  content: "\e68e";
}
.uniui-upload[data-v-d31e1c47]:before {
  content: "\e690";
}
.uniui-weixin[data-v-d31e1c47]:before {
  content: "\e691";
}
.uniui-compose[data-v-d31e1c47]:before {
  content: "\e67f";
}
.uniui-qq[data-v-d31e1c47]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-d31e1c47]:before {
  content: "\e681";
}
.uniui-pyq[data-v-d31e1c47]:before {
  content: "\e682";
}
.uniui-sound[data-v-d31e1c47]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-d31e1c47]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-d31e1c47]:before {
  content: "\e686";
}
.uniui-trash[data-v-d31e1c47]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-d31e1c47]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-d31e1c47]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-d31e1c47]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-d31e1c47]:before {
  content: "\e68c";
}
.uniui-download[data-v-d31e1c47]:before {
  content: "\e68d";
}
.uniui-help[data-v-d31e1c47]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-d31e1c47]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-d31e1c47]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-d31e1c47]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-d31e1c47]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-d31e1c47]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-d31e1c47]:before {
  content: "\e66c";
}
.uniui-clear[data-v-d31e1c47]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-d31e1c47]:before {
  content: "\e66e";
}
.uniui-minus[data-v-d31e1c47]:before {
  content: "\e66f";
}
.uniui-image[data-v-d31e1c47]:before {
  content: "\e670";
}
.uniui-mic[data-v-d31e1c47]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-d31e1c47]:before {
  content: "\e672";
}
.uniui-close[data-v-d31e1c47]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-d31e1c47]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-d31e1c47]:before {
  content: "\e675";
}
.uniui-plus[data-v-d31e1c47]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-d31e1c47]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-d31e1c47]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-d31e1c47]:before {
  content: "\e668";
}
.uniui-info[data-v-d31e1c47]:before {
  content: "\e669";
}
.uniui-locked[data-v-d31e1c47]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-d31e1c47]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-d31e1c47]:before {
  content: "\e659";
}
.uniui-camera[data-v-d31e1c47]:before {
  content: "\e65a";
}
.uniui-circle[data-v-d31e1c47]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-d31e1c47]:before {
  content: "\e65c";
}
.uniui-chat[data-v-d31e1c47]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-d31e1c47]:before {
  content: "\e65e";
}
.uniui-flag[data-v-d31e1c47]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-d31e1c47]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-d31e1c47]:before {
  content: "\e661";
}
.uniui-home[data-v-d31e1c47]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-d31e1c47]:before {
  content: "\e663";
}
.uniui-gear[data-v-d31e1c47]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-d31e1c47]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-d31e1c47]:before {
  content: "\e666";
}
.uniui-map[data-v-d31e1c47]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-d31e1c47]:before {
  content: "\e656";
}
.uniui-refresh[data-v-d31e1c47]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-d31e1c47]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-d31e1c47]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-d31e1c47]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-d31e1c47]:before {
  content: "\e648";
}
.uniui-redo[data-v-d31e1c47]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-d31e1c47]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-d31e1c47]:before {
  content: "\e64c";
}
.uniui-more[data-v-d31e1c47]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-d31e1c47]:before {
  content: "\e64e";
}
.uniui-undo[data-v-d31e1c47]:before {
  content: "\e64f";
}
.uniui-images[data-v-d31e1c47]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-d31e1c47]:before {
  content: "\e652";
}
.uniui-settings[data-v-d31e1c47]:before {
  content: "\e653";
}
.uniui-search[data-v-d31e1c47]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-d31e1c47]:before {
  content: "\e655";
}
.uniui-list[data-v-d31e1c47]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-d31e1c47]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-d31e1c47]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-d31e1c47]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-d31e1c47]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-d31e1c47]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-d31e1c47]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-d31e1c47]:before {
  content: "\e643";
}
.uniui-heart[data-v-d31e1c47]:before {
  content: "\e639";
}
.uniui-loop[data-v-d31e1c47]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-d31e1c47]:before {
  content: "\e632";
}
.uniui-scan[data-v-d31e1c47]:before {
  content: "\e62a";
}
.uniui-bars[data-v-d31e1c47]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-d31e1c47]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-d31e1c47]:before {
  content: "\e62c";
}
.uniui-shop[data-v-d31e1c47]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-d31e1c47]:before {
  content: "\e630";
}
.uniui-cart[data-v-d31e1c47]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-d31e1c47] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.customer-service[data-v-fec1fb0d] {
  position: fixed;
  z-index: 999;
  border-radius: 50%;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}
.customer-service.is-dragging[data-v-fec1fb0d] {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}
.customer-service.is-dragging .customer-icon[data-v-fec1fb0d] {
  opacity: 0.9;
}
.customer-service.is-pc[data-v-fec1fb0d]:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.2);
}
.customer-service .customer-icon[data-v-fec1fb0d] {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.customer-service .drag-indicator[data-v-fec1fb0d] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: pulse-fec1fb0d 1s infinite;
  pointer-events: none;
}
@keyframes pulse-fec1fb0d {
0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}
100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
}
}
@media (max-width: 768px) {
.customer-service[data-v-fec1fb0d]:active {
    transform: scale(0.95);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.more-profile-section[data-v-3d8e8844] {
  padding: 0px 20px 0px 20px;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}
.more-profile-section .profile-header[data-v-3d8e8844] {
  display: flex;
  align-items: center;
  padding: 20px 0px 0px 0px;
}
.more-profile-section .avatar-container[data-v-3d8e8844] {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  border-radius: 10px;
}
.more-profile-section .profile-info[data-v-3d8e8844] {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.more-profile-section .username[data-v-3d8e8844] {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}
.more-profile-section .phone[data-v-3d8e8844] {
  font-size: 14px;
  color: #999;
}
.more-profile-section .menu-section[data-v-3d8e8844] {
  margin-top: 10px;
}
.more-profile-section .menu-section .menu-item[data-v-3d8e8844] {
  display: flex;
  align-items: center;
  border-radius: 10px;
  border: 1px solid var(---normal, #e2e4e9);
  background: #fff;
  max-height: 65px;
  height: 60px;
  padding: 0 14px;
  margin: 20px 0;
}
.more-profile-section .menu-icon[data-v-3d8e8844] {
  width: 28px;
  height: 28px;
  margin-right: 15px;
}
.more-profile-section .menu-content[data-v-3d8e8844] {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.more-profile-section .menu-title[data-v-3d8e8844] {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  flex: 1;
}
.more-profile-section .menu-subtitle[data-v-3d8e8844] {
  font-size: 14px;
  color: #2196f3;
  margin-top: 4px;
}
.more-profile-section .version-section[data-v-3d8e8844] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  margin-top: 20px;
}
.more-profile-section .version-text[data-v-3d8e8844] {
  font-size: 14px;
  color: #999;
  text-align: center;
}