<template>
  <!-- 最近上传 模块 -->
  <view class="recently-details-container">
    <!-- 全部 -->
    <view class="section-box s-p-b">
      <view class="section-left" @click="toggleExpand('current')">
        <image
          class="icon-down"
          :src="'/static/tabBar/down.png'"
          :style="{
            transform: expandStates.current ? 'rotate(0deg)' : 'rotate(-90deg)',
          }"
        ></image>
        <view class="section-title">全部</view>
      </view>
    </view>
    <!-- 文件列表 -->
    <transition name="slide-fade">
      <view class="file-card" v-show="expandStates.current">
        <block v-for="(item, index) in fileList" :key="index">
          <!-- 文件行 -->
          <view class="file-item">
            <!-- 文件缩略图/图标 -->
            <view class="file-thumbnail">
              <image
                v-if="isImageFile(item.name)"
                class="file-preview"
                :src="item.url"
                mode="aspectFill"
              ></image>
              <image
                v-else
                class="flie-icon"
                :src="item.type === 'dir' ? '/static/file/document.svg' : getFileIconPath(item.name)"
              ></image>
            </view>
            <!-- 文件信息 -->
            <view class="content-wrapper">
              <uni-easyinput
                v-if="item.isShowInput"
                trim="all"
                v-model="item.name"
                @change="handelInputSetName(item)"
                focus
              ></uni-easyinput>
              <view
                class="todo-title"
                v-else
                @click="handleFilePreview(item)"
              >
                {{ item.name }}
              </view>
              <view class="title-row">
                <view class="upload-time">{{ filterDateTime(item.created_at) }}</view>
              </view>
            </view>
            <!-- 更多操作 -->
            <uni-icons
              v-if="item.type !== 'dir'"
              type="more-filled"
              size="20"
              color="#E2E4E9"
              @click="handeOpenClick(item)"
            ></uni-icons>
          </view>
          <!-- 分割线：除最后一项外 -->
          <view v-if="index !== fileList.length - 1" class="item-divider"></view>
        </block>
      </view>
    </transition>
    <!-- 操作弹窗 -->
    <ActionSheet
      v-if="operationFileShow"
      v-model:show="operationFileShow"
      :actions="actions"
      title="文件操作"
      @select="onFileSelect"
    />
  </view>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from "vue";
import ActionSheet from "@/components/action-sheet/action-sheet.vue";
import {
  saveOrEditAttachment,
  deleteAttachment,
} from "@/http/attachment.js";
import { filterDateTime } from "@/utils/formatTime.js";
import { isImageFile, openWebOfficePreview, getMimeType, getFileIconPath, downloadFile } from "@/utils/fileUtils.js";
import { previewFile } from '@/utils/filePreviewUtils.js'; // 导入文件预览工具
const emit = defineEmits(["refresh", "onAddAttachment"]);
const props = defineProps({
  // 客户文件列表数据
  fileList: {
    type: Array,
    default: [],
  },
});
const currentFile = ref(null); // 当前操作的文件
const operationFileShow = ref(false);

// 修改状态管理
const expandStates = ref({
  current: true,
  finished: true,
});

const actions = ref([
  { name: "预览" },
  { name: "下载" },
  { name: "重命名", color: "rgb(79 128 181)" },
  { name: "删除", color: "danger" },
  // { name: '重命名', subname: '描述信息' },
  // { name: '禁用选项', disabled: true },
  // { name: '加载选项', loading: true },
]);

// 修改切换方法，增加类型参数
const toggleExpand = (type) => {
  expandStates.value[type] = !expandStates.value[type];
};
// 弹出客户文件下拉
const handeOpenClick = (item) => {
  currentFile.value = item;
  operationFileShow.value = true;
};

// 点击选项时触发，禁用或加载状态下不会触发
const onFileSelect = (item, index) => {
  if (!currentFile.value) return;
  switch (item.name) {
    case "预览":
      handleFilePreview(currentFile.value);
      break;
    case "下载":
    oneClickDownload(currentFile.value);
      break;
    case "删除":
      handleRecentUploadDelete(currentFile.value);
      break;
    case "重命名":
      setRenameFile(currentFile.value);
      break;
  }
  operationFileShow.value = false;
};

/**
 * 处理文件预览
 * @param {Object} file - 文件对象
 */
const handleFilePreview = async (file) => {
  try {
    // 调用预览服务，传入不同平台的预览回调
    await previewFile(file, {
      onWebOfficePreview: openWebOfficePreview,      // H5端WebOffice预览
      // onAppPreview: openAppPreview,              // APP端原生预览
    });
  } catch (error) {
    console.error('文件预览失败:', error);
    uni.showToast({
      title: '预览失败',
      icon: 'none'
    });
  }
};

/**
 * 打开APP端预览
 * @param {Object} file - 文件对象
 */
const openAppPreview = (file) => {
  console.log('📱 打开APP端预览:', file.name);
  currentAppPreviewFile.value = file;
  showAppPreview.value = true;
};

/**
 * 下载文件 - 调用公共下载函数
 * @param {Object} file - 文件对象，包含url和name属性
 */
const oneClickDownload = async (file) => {
  try {
    await downloadFile(file.url, file.name);
  } catch (error) {
    console.error('下载失败:', error);
    // 公共函数内部已经处理了错误提示，这里不需要重复提示
  }
};

// 最近上传模块-删除文件
const handleRecentUploadDelete = async (delItem) => {
  try {
    // 确保在删除操作前就将输入框状态设为false，防止闪现
    if(delItem) {
      delItem.isShowInput = false;
    }
    // 显示加载中，阻止用户操作，防止输入框闪现
    uni.showLoading({
      title: '删除中...'
    });
    const res = await deleteAttachment({
      id: delItem.id,
    });
    if (res.code === 0) {
      uni.showToast({
        title: "删除文件成功",
        icon: "success",
      });
      emit("refresh");
    }
  } catch (error) {
    console.error("删除失败:", error);
    uni.showToast({
      title: "删除失败",
      icon: "none",
    });
  } finally {
    // 无论成功或失败，都隐藏加载状态
    uni.hideLoading();
  }
};

// 重命名
const setRenameFile = async (nameItem) => {
  nameItem.isShowInput = true;
};

// 输入框修改事件
const handelInputSetName = async (item) => {
  item.isShowInput = false;
  console.error("输入框修改事件", item);
  try {
    const res = await saveOrEditAttachment({
      id: item.id,
      type: item.type,
      // classify: 2,
      name: item.name,
    });
    if (res.code === 0) {
      emit("refresh");
    }
  } catch (error) {
    console.error("重命名请求失败:", error);
  }
};
</script>

<style scoped lang="scss">
// 全局样式
@import '/styles/common-styles.scss';
.recently-details-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  .section-box {
    display: flex;
    align-items: center;
    margin: 15px 5px 15px 5px;
    padding: 0px 10px;
    .section-left {
      display: flex;
      align-items: center;
      .icon-down {
        width: 25px;
        height: 25px;
      }
    }
    .section-title {
      color: var(---, #787d86);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
  .s-p-b {
    justify-content: space-between;
  }
  .file-card {
    margin: 15px;
    border-radius: 10px;
    border: 1px solid #e2e4e9;
    background-color: #fff;

    .file-item {
      display: flex;
      align-items: center;
      margin: 5px 14px 0 14px;
      height: 56px; /* 设置固定高度为56px */
      max-height: 56px;
      /* 结构：缩略图 + 内容 + 操作图标 */
    }
    /* 分割线 */
    .item-divider {
      height: 1px;
      background-color: #f0f0f0;
      margin-left: 44px; /* 14px 左外边距 + 24px 缩略图 + 6px 间距 */
      margin-right: 16px;
    }
    /* 文件缩略图/图标区域 */
    .file-thumbnail {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      .flie-icon {
        width: 24px;
        height: 24px;
      }
    }
    .content-wrapper {
      flex: 1;
      margin-left: 6px;
      overflow: hidden;
    }
    .todo-title {
      color: var(---, #000);
      font-size: 14px;
      white-space: nowrap; /* 确保文本不换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略号 */
      max-width: 100%; /* 确保宽度不超过父容器 */
    }
    .title-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 2px;
      color: var(---, #adb1ba);
      font-size: 10px;
      .upload-time {
        flex: 1;
      }
    }
  }
}
</style>
